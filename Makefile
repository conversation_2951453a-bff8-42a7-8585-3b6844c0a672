.PHONY: help
help:
	@echo "Usage: make <target>"
	@echo ""
	@echo "Targets:"
	@echo "  up                - Run the server on port 8201"
	@echo "  down              - Stop the server"
	@echo "  services-up       - Start required services (MySQL, BaseX) via Docker Compose"
	#@echo "  services-down     - Stop all services started by Docker Compose"
	#@echo "  services-status   - Show the status of running services"
	@echo ""
	@echo "  bus-up            - Start the Symfony Messenger worker"
	@echo "  bus-down          - Stop the Messenger worker"
	@echo "  bus-one           - Run a single Messenger job and exit"
	@echo ""
	@echo "  test              - Run PHPUnit tests with code coverage"
	@echo "  clean-test        - Rebuild test database and run PHPUnit tests with code coverage"
	@echo "  test-books        - Load test-specific Base Books"
	@echo "  test-projects     - Load test-specific Projects"
	@echo "  test-proposals    - Load test proposal fixtures into the database"
	@echo ""
	@echo "  xml2-books        - Load multiple XML2.0 books into the system"
	@echo "  xml2-projects     - Create XML2.0 projects based on predefined configurations"
	@echo ""
	@echo "  diff              - Generate a new Doctrine migration based on entity changes"
	@echo "  migrate           - Run all pending Doctrine migrations"
	@echo "  rollback          - Roll back the last Doctrine migration"
	@echo "  status            - Show the current status of Doctrine migrations"
	@echo "  cache-clear       - Clear the Symfony application cache"
	@echo ""
	@echo "Example:"
	@echo "  make test-projects   # Runs project-specific tests with fixture data"

.PHONY: up
up:
	@symfony server:start -d --port 8201

.PHONY: down
down:
	@symfony server:stop

#.PHONY: services-up
#services-up:
#	@docker compose up database -d

#.PHONY: services-down
#services-down:
#	@docker compose down

.PHONY: services-status
services-status:
	@docker compose ps

.PHONY: test clean-test test-coverage
test:
	vendor/bin/phpunit
clean-test:
	APP_TEST_REBUILD_DB=1 vendor/bin/phpunit
test-coverage:
	@XDEBUG_MODE=coverage vendor/bin/phpunit --coverage-html=coverage

.PHONY: bus-up
bus-up:
	@bin/console messenger:consume async_heavy --no-reset -vv &
	@bin/console messenger:consume async_codebook --no-reset -vv &

.PHONY: bus-down
bus-down:
	@bin/console messenger:stop-workers

.PHONY: bus-one
bus-one:
	@bin/console messenger:consume async --limit 1

.PHONY: xml2-books xml2-books-1.0 xml2-books-2.0 xml2-books-3.0
xml2-books:
	@make xml2-books-1.0
	@make xml2-books-2.0
xml2-books-1.0:
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 ICC Performance Code' 'First Version'                           -x ./fixtures/ICCPC2024V1.0/ICCPC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Existing Building Code' 'First Version'           -x ./fixtures/IEBC2024V1.0/IEBC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Energy Conservation Code' 'First Version'         -x ./fixtures/IECC2024V1.0/IECC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Energy Conservation Code v1.1' 'First Version'    -x ./fixtures/IECC2024V1.1/IECC2024V1.1.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Fire Code' 'First Version'                        -x ./fixtures/IFC2024V1.0/IFC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Fuel Gas Code' 'First Version'                    -x ./fixtures/IFGC2024V1.0/IFGC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Green Construction Code' 'First Version'          -x ./fixtures/IGCC2024V1.0/IGCC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Mechanical Code' 'First Version'                  -x ./fixtures/IMC2024V1.0/IMC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Plumbing Code' 'First Version'                    -x ./fixtures/IPC2024V1.0/IPC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Private Sewage Disposal Code' 'First Version'     -x ./fixtures/IPSDC2024V1.0/IPSDC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Residential Code' 'First Version'                 -x ./fixtures/IRC2024V1.0/IRC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Swimming Pool and Spa Code' 'First Version'       -x ./fixtures/ISPSC2024V1.0/ISPSC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Wildland-Urban Interface Code' 'First Version'    -x ./fixtures/IWUIC2024V1.0/IWUIC2024.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Zoning Code' 'First Version'                      -x ./fixtures/IZC2024V1.0/IZC2024.xml -o -q
xml2-books-2.0:
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Building Code' 'Second Version'                   -x ./fixtures/IBC2024V2.0/IBC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Existing Building Code' 'Second Version'          -x ./fixtures/IEBC2024V2.0/IEBC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Fire Code' 'Second Version'                       -x ./fixtures/IFC2024V2.0/IFC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Fuel Gas Code' 'Second Version'                   -x ./fixtures/IFGC2024V2.0/IFGC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Mechanical Code' 'Second Version'                 -x ./fixtures/IMC2024V2.0/IMC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Plumbing Code' 'Second Version'                   -x ./fixtures/IPC2024V2.0/IPC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Private Sewage Disposal Code' 'Second Version'    -x ./fixtures/IPSDC2024V2.0/IPSDC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Residential Code' 'Second Version'                -x ./fixtures/IRC2024V2.0/IRC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Swimming Pool and Spa Code' 'Second Version'      -x ./fixtures/ISPSC2024V2.0/ISPSC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Wildland-Urban Interface Code' 'Second Version'   -x ./fixtures/IWUIC2024V2.0/IWUIC2024V2.0.xml -o -q
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Zoning Code' 'Second Version'   					-x ./fixtures/IZC2024V2.0/IZC2024V2.0.xml -o -q
xml2-books-3.0:
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Property Maintenance Code' 'Third Version'   		-x ./fixtures/IPMC2024V3.0/IPMC2024V3.0.xml -o -q

.PHONY: xml2-projects xml2-projects-1.0 xml2-projects-2.0
xml2-projects:
	@make xml2-projects-1.0
	@make xml2-projects-2.0
xml2-projects-1.0:
	@bin/console app:project:create 'ICCPC2024v1.0-'$$(date +%Y%m%d%H%M%S) 'ICCPC2024V1.0-volume1-pub' --name '2024 ICC Performance Code' -l 0 -p 1
	@bin/console app:project:create 'IEBC2024v1.0-'$$(date +%Y%m%d%H%M%S)  'IEBC2024-pub'              --name '2024 International Existing Building Code' -l 0 -p 1
#	@bin/console app:project:create 'IECC2024v1.0-'$$(date +%Y%m%d%H%M%S)  'IECC2024V1.0-pub'          --name '2024 International Energy Conservation Code' -l 0 -p 1
#	@bin/console app:project:create 'IECC2024v1.1-'$$(date +%Y%m%d%H%M%S)  'IECC2024V1.1-pub'          --name '2024 International Energy Conservation Code v1.1' -l 0 -p 1
#	@bin/console app:project:create 'IFC2024v1.0-'$$(date +%Y%m%d%H%M%S)   'IFC2024V1.0-pub'           --name '2024 International Fire Code' -l 0 -p 1
	@bin/console app:project:create 'IFGC2024v1.0-'$$(date +%Y%m%d%H%M%S)  'IFGC2024V1.0-pub'          --name '2024 International Fuel Gas Code' -l 0 -p 1
	@bin/console app:project:create 'IGCC2024v1.0-'$$(date +%Y%m%d%H%M%S)  'IGCC2024V1.0-pub'          --name '2024 International Green Construction Code Code' -l 0 -p 1
	@bin/console app:project:create 'IMC2024v1.0-'$$(date +%Y%m%d%H%M%S)   'IMC2024V2.0-volume1-pub'   --name '2024 International Mechanical Code' -l 0 -p 1
	@bin/console app:project:create 'IPC2024v1.0-'$$(date +%Y%m%d%H%M%S)   'IPC2024V1.0-volume1-pub'   --name '2024 International Plumbing Code' -l 0 -p 1
	@bin/console app:project:create 'IPSDC2024v1.0-'$$(date +%Y%m%d%H%M%S) 'IPSDC2024V1.0-pub'         --name '2024 International Private Sewage Disposal Code' -l 0 -p 1
	@bin/console app:project:create 'IRC2024v1.0-'$$(date +%Y%m%d%H%M%S)   'IRC2024V1.0-pub'           --name '2024 International Residential Code' -l 0 -p 1
	@bin/console app:project:create 'ISPSC2024v1.0-'$$(date +%Y%m%d%H%M%S) 'ISPSC2024V1.0-pub'         --name '2024 International Swimming Pool and Spa Code' -l 0 -p 1
	@bin/console app:project:create 'IWUIC2024v1.0-'$$(date +%Y%m%d%H%M%S) 'IWUIC2024V1.0-pub'         --name '2024 International Wildland-Urban Interface Code' -l 0 -p 1
	@bin/console app:project:create 'IZC2024v1.0-'$$(date +%Y%m%d%H%M%S)   'IZC2024V1.0-pub'            --name '2024 International Zoning Code' -l 0 -p 1
xml2-projects-2.0:
	@bin/console app:project:create 'IBC2024V2.0-'$$(date +%Y%m%d%H%M%S)   'IBC2024V2.0-pub'           --name '2024 International Building Code v2.0' -l 0 -p 1
	@bin/console app:project:create 'IFC2024v2.0-'$$(date +%Y%m%d%H%M%S)   'IFC2024V2.0-pub'           --name '2024 International Fire Code v2.0' -l 0 -p 1
	@bin/console app:project:create 'IFGC2024v2.0-'$$(date +%Y%m%d%H%M%S)  'IFGC2024V2.0-pub'          --name '2024 International Fuel Gas Code v2.0' -l 0 -p 1
	@bin/console app:project:create 'IMC2024v2.0-'$$(date +%Y%m%d%H%M%S)   'IMC2024V2.0-volume1-pub'   --name '2024 International Mechanical Code v2.0' -l 0 -p 1
	@bin/console app:project:create 'IPC2024v2.0-'$$(date +%Y%m%d%H%M%S)   'IPC2024V2.0-volume1-pub'   --name '2024 International Plumbing Code v2.0' -l 0 -p 1
	@bin/console app:project:create 'IPSDC2024v2.0-'$$(date +%Y%m%d%H%M%S) 'IPSDC2024V2.0-pub'         --name '2024 International Private Sewage Disposal Code v2.0' -l 0 -p 1
	@bin/console app:project:create 'ISPSC2024v2.0-'$$(date +%Y%m%d%H%M%S) 'ISPSC2024V2.0-pub'         --name '2024 International Swimming Pool and Spa Code v2.0' -l 0 -p 1
	@bin/console app:project:create 'IWUIC2024V2.0-'$$(date +%Y%m%d%H%M%S) 'IWUIC2024V2.0-pub'         --name '2024 International Wildland-Urban Interface Code v2.0' -l 0 -p 1

.PHONY: test-proposals
test-proposals:
	@bin/console doctrine:fixtures:load --append --group=test_proposals

.PHONY: test-users
test-users:
	@bin/console doctrine:fixtures:load --append --group=qa

.PHONY: create-test-book
create-test-book:
	@bin/console app:base-book:add:file 'XML2.0' 2024 '2024 International Plumbing Code' 'First Version' -x ./fixtures/IPC2024V1.0/IPC2024.xml -o -q

.PHONY: create-test-project
create-test-project:
	@bin/console app:project:create 'TEST2027' 'IPC2024V1.0-volume1-pub' --name 'IPC' -l 0 -p 1 -q

# Path to the Symfony console (adjust if necessary)
CONSOLE = php bin/console

# Doctrine migration commands
.PHONY: diff
diff:
	$(CONSOLE) doctrine:migrations:diff

.PHONY: migrate
migrate:
	$(CONSOLE) doctrine:migrations:migrate

.PHONY: rollback
rollback:
	$(CONSOLE) doctrine:migrations:migrate prev

.PHONY: status
status:
	$(CONSOLE) doctrine:migrations:status

.PHONY: cache-clear
cache-clear:
    $(CONSOLE) cache:clear

.PHONY: test-fixtures
test-fixtures:
	@bin/console doctrine:fixtures:load --append --group=test
