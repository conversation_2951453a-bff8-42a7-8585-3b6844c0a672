# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
  app.system_email: '<EMAIL>'
  app.version: '%env(string:APP_VERSION)%'
  app.url: '%env(resolve:CT_FE_URL)%'
  app.api: '%env(resolve:CT_API_URL)%'
  cdp.api: '%env(resolve:CDPCMS_API_URL)%'
  content_hub.api: '%env(resolve:CONTENT_HUB_URI)%'
  xml.schema: '%kernel.project_dir%/config/xml/schema2/icc-schema.xsd'
  aws.cognito.id: '%env(string:AWS_ID)%'
  aws.cognito.secret: '%env(string:AWS_SECRET)%'
  aws.cognito.client_id: '%env(string:AWS_CLIENT_ID)%'
  aws.cognito.client_secret: '%env(string:AWS_CLIENT_SECRET)%'
  aws.cognito.region: '%env(string:AWS_REGION)%'
  aws.cognito.user_pool: '%env(string:AWS_USER_POOL_ID)%'
  aws.cognito.cookie_domain: '%env(string:COOKIE_DOMAIN)%'
  aws.cognito.cookie_secure: '%env(bool:COOKIE_SECURE)%'
  aws.sso.id: '%env(string:SSO_APP_ID)%'
  aws.sso.secret: '%env(string:SSO_APP_SECRET)%'
  aws.sso.uri: '%env(string:SSO_APP_URI)%'
  aws.sso.portal: '%env(string:SSO_LOGIN_PORTAL)%'
  aws.sso.portal_token: '%env(string:SSO_LOGIN_PORTAL_TOKEN)%'
  qr.domain: '%env(string:QR_DOMAIN)%'
  qr.username: '%env(string:QR_USERNAME)%'
  qr.password: '%env(string:QR_PASSWORD)%'
  security.test_user_provider_enabled: '%env(bool:TEST_USER_PROVIDER_ENABLED)%'

services:
  # default configuration for services in *this* file
  _defaults:
    autowire: true      # Automatically injects dependencies in your services.
    autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
    bind:
      $appVersion: '%app.version%'
      $apiUrl: '%app.api%'
      $asyncTransport: '@messenger.transport.async_codebook'
      $cdpCmsUrl: '%cdp.api%'
      $frontendUrl: '%app.url%'
      $kernelEnv: '%kernel.environment%'
      $kernelRootDir: '%kernel.project_dir%'
      $fixtureDir: '%kernel.project_dir%/fixtures'
      $loggerAuth: '@monolog.logger.auth'
      $loggerImages: '@monolog.logger.images'
      $loggerMongo: '@monolog.logger.mongo'
      $loggerPhp: '@monolog.logger.php'
      $loggerStopwatch: '@monolog.logger.stopwatch'
      $loggerXml: '@monolog.logger.xmlexporter'
      $projectXmlLogger: '@monolog.logger.projectxml'
      $reportsTimeLimit: 0
      $reportsMemoryLimit: -1
      $reportsDir: '%kernel.project_dir%/public/files/reports/'
      $testUserProviderEnabled: '%security.test_user_provider_enabled%'

  _instanceof:
    App\Serializer\Encoder\Xml2\Mapper\MapperInterface:
      tags: [ 'app.xml2.mapper' ]

  # makes classes in src/ available to be used as services
  # this creates a service per class whose id is the fully-qualified class name
  App\:
    resource: '../src/'
    exclude:
      - '../src/DependencyInjection/'
      - '../src/Entity/'
      - '../src/Event/'
      - '../src/Kernel.php'
      - '../src/Message/'

  # controllers are imported separately to make sure services can be injected
  # as action arguments even if you don't extend any base controller class
  App\Controller\:
    resource: '../src/Controller/'
    tags: [ 'controller.service_arguments' ]

  App\Serializer\Normalizer\DateTimeNullNormalizer:
    class: App\Serializer\Normalizer\DateTimeNullNormalizer
    decorates: serializer.normalizer.datetime
    arguments: [ '@App\Serializer\Normalizer\DateTimeNullNormalizer.inner' ]

  ICC\Component\Cognito\ICCCognitoContainer:
    arguments:
      $token: '%aws.sso.portal_token%'
      $apiPortalUrl: '%aws.sso.portal%'
      $options: { 'verify': 0 }
  ICC\Component\Cognito\ICCCognitoClient:
    public: true
    arguments:
      $awsId: '%aws.cognito.id%'
      $awsKey: '%aws.cognito.secret%'
      $appClientId: '%aws.cognito.client_id%'
      $appClientSecret: '%aws.cognito.client_secret%'
      $region: '%aws.cognito.region%'
      $userPoolId: '%aws.cognito.user_pool%'
  ICC\Component\Cognito\ICCCognitoCookieClient:
    arguments:
      $client: '@ICC\Component\Cognito\ICCCognitoClient'
      $container: '@ICC\Component\Cognito\ICCCognitoContainer'
      $cookieDomain: '%aws.cognito.cookie_domain%'
      $cookieSecure: '%aws.cognito.cookie_secure%'
      $httpOnly: false
      $expire: 1800
  App\Security\CognitoAuthenticator:
    arguments:
      $authenticationSuccessHandler: '@lexik_jwt_authentication.handler.authentication_success'
      $authenticationFailureHandler: '@lexik_jwt_authentication.handler.authentication_failure'
  App\Security\TestAuthenticator:
    arguments:
      $authenticationSuccessHandler: '@lexik_jwt_authentication.handler.authentication_success'
      $authenticationFailureHandler: '@lexik_jwt_authentication.handler.authentication_failure'

  ICC\Component\CHub\Security\CognitoTokenAuthenticator:
    arguments:
      $appId: '%aws.sso.id%'
      $appSecret: '%aws.sso.secret%'
      $appUrl: '%aws.sso.uri%'

  #temporary thing for testing
  App\Infrastructure\Http\Middleware\RequestTimingMiddleware:
    decorates: 'http_kernel'
    # Symfony creates an inner service with ".inner" suffix for you:
    arguments:
      $app: '@App\Infrastructure\Http\Middleware\RequestTimingMiddleware.inner'

  ICC\Component\CHub\CHubService:
    arguments:
      $cHubUrl: '%content_hub.api%'

  App\Service\UserService:
    arguments:
      $roleHierarchy: '%security.role_hierarchy.roles%'

  Psr\SimpleCache\CacheInterface:
    class: Symfony\Component\Cache\Psr16Cache
    arguments:
      - '@cache.app'

  App\Service\Xml2\CacheService:
    arguments:
      $cache: '@Psr\SimpleCache\CacheInterface'

  App\Service\CdpAccess\HtmlPurifierHelper:
    arguments:
      - '%kernel.project_dir%/var/html_purifier/cache/'

  App\EventSubscriber\Security\JwtEventSubscriber:
    arguments:
      $roleHierarchy: '%security.role_hierarchy.roles%'

  App\Serializer\Encoder\Xml2\Xml2Service:
    arguments:
      $mappers: !tagged_iterator 'app.xml2.mapper'

  App\Service\Qr\QrService:
    arguments:
      $domain: '%qr.domain%'
      $username: '%qr.username%'
      $password: '%qr.password%'

  App\Service\CodeXml\CodeXmlService:
    public: true
