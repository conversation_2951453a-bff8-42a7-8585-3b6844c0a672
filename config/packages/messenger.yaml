framework:
  messenger:
    # reset services after consuming messages
    reset_on_message: true

    # Uncomment this (and the failed transport below) to send failed messages to this transport for later handling.
    failure_transport: failed

    transports:
      # https://symfony.com/doc/current/messenger.html#transport-configuration
      async:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
        retry_strategy:
          max_retries: 0
          # milliseconds delay
          delay: 1000
          # causes the delay to be higher before each retry
          # e.g. 1 second delay, 2 seconds, 4 seconds
          multiplier: 2
          max_delay: 0
          # override all of this with a service that
          # implements Symfony\Component\Messenger\Retry\RetryStrategyInterface
          # service: null
      failed: 'doctrine://default?queue_name=failed'
      # sync: 'sync://'

    routing:
      # Route your messages to the transports
      'App\Message\Cdp\SyncProposalsFromEndpointMessage': async
      'App\Message\CodeBook\UpdateNodesByXRefAsyncMessage': async
      'App\Message\CodeBook\UpsertUrlLinksAsyncMessage': async
      'App\Message\CodeBook\UpsertXRefLinksAsyncMessage': async
      'App\Message\Project\CodeChanges\AssociateCodeChangesMessage': async
      'App\Message\Project\Action\ReEvaluateWorkflowStatusMessage': async
      'App\Message\Project\BookPDFExportMessage': async
      'App\Message\Project\BookRTFExportMessage': async
      'App\Message\Project\BookXMLExportMessage': async
      'App\Message\ChapterPdfVersionMessage': async
      'App\Message\Project\Action\UpdateActionWorkflowStatusMessage': async

when@test:
  framework:
    messenger:
      transports:
        async: 'in-memory://'

# when@test:
#    framework:
#        messenger:
#            transports:
#                # replace with your transport name here (e.g., my_transport: 'in-memory://')
#                # For more Messenger testing tools, see https://github.com/zenstruck/messenger-test
#                async: 'in-memory://'
