framework:
  messenger:
    reset_on_message: true
    failure_transport: failed

    transports:
      async_codebook:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%&queue_name=codebook'
        options:
          use_notify: true
        retry_strategy:
          max_retries: 0

      async_heavy:
        dsn: '%env(MESSENGER_TRANSPORT_DSN)%&queue_name=heavy'
        options:
          use_notify: true
        retry_strategy:
          max_retries: 0

      failed:
        dsn: 'doctrine://default?queue_name=failed'

    routing:
      # CODEBOOK
      App\Message\Cdp\SyncProposalsFromEndpointMessage: async_codebook
      App\Message\Project\Action\ReEvaluateWorkflowStatusMessage: async_codebook
      App\Message\CodeBook\UpdateNodesByXRefAsyncMessage: async_codebook
      App\Message\CodeBook\UpsertUrlLinksAsyncMessage: async_codebook
      App\Message\CodeBook\UpsertXRefLinksAsyncMessage: async_codebook
      App\Message\Project\CodeChanges\AssociateCodeChangesMessage: async_codebook

      # HEAVY
      App\Message\Project\BookPDFExportMessage: async_heavy
      App\Message\Project\BookRTFExportMessage: async_heavy
      App\Message\Project\BookXMLExportMessage: async_heavy
      App\Message\Project\ExportProjectMessage: async_heavy
      App\Message\ChapterPdfVersionMessage: async_heavy

when@test:
  framework:
    messenger:
      transports:
        async: 'in-memory://'
