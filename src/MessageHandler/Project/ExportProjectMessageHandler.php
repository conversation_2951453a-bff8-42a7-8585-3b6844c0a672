<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\Project;

use App\Entity\Project;
use App\Event\Project\PostExportProject;
use App\Event\Project\PreExportProject;
use App\Exception\ApiException;
use App\Message\Project\ExportProjectMessage;
use App\Message\Project\ExportProjectToContentHubMessage;
use App\Message\Project\ExportProjectToZipMessage;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\Helper\PhpHelper;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

#[AsMessageHandler]
class ExportProjectMessageHandler
{
    public function __construct(
        private readonly MessageBusInterface      $bus,
        private readonly EntityManagerInterface   $em,
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly LoggerInterface          $logger,
        private readonly Xml2Encoder              $xml2Encoder,
        private readonly CodeBookToXmlMapper      $codeBookToXmlMapper,
    ) {
    }

    public function __invoke(ExportProjectMessage $message): void
    {
        $request = $message->getRequest();
        PhpHelper::setUnlimitedLimits();
        $project = $this->em->getRepository(Project::class)->findOneBy([
            'shortCode' => $request->shortCode,
        ]);

        if (null === $project) {
            throw new RuntimeException(sprintf('Project %s not found.', $request->shortCode));
        }

        $preExport = new PreExportProject($project);
        $this->eventDispatcher->dispatch($preExport);

        $clean = $request->isClean ? '' : 'redline';
        foreach ($project->getCodeBook()->getVolumes() as $volume) {
            $volume->setPdfFormat($clean);
        }

        try {
            $xml2Element = $this->codeBookToXmlMapper->map($project->getCodeBook());
            $xml = $this->xml2Encoder->encode($xml2Element);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            throw new ApiException($e->getMessage(), previous: $e);
        }

        $postExport = new PostExportProject($project, $xml);
        $this->eventDispatcher->dispatch($postExport);
        $assets = $postExport->getAssets();

        if (!empty($request->zipFile)) {
            $message = new ExportProjectToZipMessage($request, $project, $postExport->getXml(), $assets);
            $this->bus->dispatch($message);
        }
        if ($request->contentHub) {
            $message = new ExportProjectToContentHubMessage($request, $project, $postExport->getXml(), $assets);
            $this->bus->dispatch($message);
        }
    }
}
