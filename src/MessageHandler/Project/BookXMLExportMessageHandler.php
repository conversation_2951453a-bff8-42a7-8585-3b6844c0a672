<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\MessageHandler\Project;

use App\Dto\Xml2\Project\ExportProjectRequest;
use App\Entity\JobStatus;
use App\Exception\XmlValidationException;
use App\Message\Project\BookXMLExportMessage;
use App\Message\Project\ExportProjectMessage;
use App\Service\Helper\PhpHelper;
use App\Service\JobStatusService;
use App\Service\Xml2\XmlValidationErrorFormatter;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\MessageBusInterface;

use function date;
use function mkdir;
use function sprintf;
use function str_contains;
use function unlink;

#[AsMessageHandler]
class BookXMLExportMessageHandler
{
    private LoggerInterface $logger;
    private JobStatusService $statusService;
    private MessageBusInterface $bus;
    private string $rootDir;
    private XmlValidationErrorFormatter $xmlValidationErrorFormatter;

    public function __construct(
        LoggerInterface             $logger,
        JobStatusService            $statusService,
        MessageBusInterface         $messageBus,
        XmlValidationErrorFormatter $xmlValidationErrorFormatter,
        string                      $kernelRootDir
    )
    {
        $this->logger = $logger;
        $this->statusService = $statusService;
        $this->rootDir = $kernelRootDir;
        $this->bus = $messageBus;
        $this->xmlValidationErrorFormatter = $xmlValidationErrorFormatter;
    }

    public function __invoke(BookXMLExportMessage $message): void
    {
        $job = $this->statusService->createExportStatus(
            $message->getProject()->getShortCode(),
            JobStatusService::BOOK_XML_EXPORT_TYPE,
            $message->isClean()
        );
        $this->run($message, $job);
    }

    public function run(BookXMLExportMessage $message, JobStatus $jobStatus): void
    {
        PhpHelper::setUnlimitedLimits();

        $project = $message->getProject();
        $nodeId = $project->getShortCode();
        $this->logger->info(sprintf('Begin exporting book content for "%s"...', $nodeId));
        $fileName = $this->getFileName($message);
        $fileDirectory = \dirname($fileName);
        $currentFile = \basename($fileName);
        $error = false;
        $errorMessage = '';

        try {
            $request = new ExportProjectRequest();
            $request->shortCode = $nodeId;
            $request->zipFile = $fileName;
            $request->isClean = $message->isClean();

            $message = new ExportProjectMessage($request);
            $this->bus->dispatch($message);

            $this->cleanupOldFiles($fileDirectory, $project->getShortCode(), $currentFile);

        } catch (\Throwable $e) {
            $exception = $e;

            if ($e instanceof HandlerFailedException) {
                foreach ($e->getNestedExceptions() as $nestedException) {
                    if ($nestedException instanceof XmlValidationException) {
                        $exception = $nestedException;
                        break;
                    }
                }

                if (!$exception instanceof XmlValidationException && $e->getPrevious()) {
                    $exception = $e->getPrevious();
                }
            }

            if ($exception instanceof XmlValidationException) {
                $firstIssue = $exception->getIssues()[0] ?? null;
                $details = $firstIssue
                    ? sprintf(
                        'node "%s" (%s) field "%s": %s',
                        $firstIssue->nodeId,
                        $firstIssue->nodeType,
                        $firstIssue->field,
                        $firstIssue->message
                    )
                    : $exception->getMessage();

                $validationMessage = sprintf('Xml export for %s failed XML validation - %s', $nodeId, $details);
                $this->logger->warning($validationMessage, [
                    'issues' => array_map(static fn ($issue) => [
                        'node_id' => $issue->nodeId,
                        'field' => $issue->field,
                        'message' => $issue->message,
                    ], $exception->getIssues()),
                ]);
                $errorMessage = $this->xmlValidationErrorFormatter->format($validationMessage, $exception);
            } else {
                $errorMessage = sprintf('Xml export for %s Export Error - %s', $nodeId, $exception->getMessage());
                $this->logger->critical($errorMessage, [
                    'exception' => $exception,
                ]);
            }
            $error = true;
        } finally {
            $this->logger->info(sprintf('Finished exporting book "%s"...', $nodeId));
            $status = $error ? JobStatus::STATUS_FAILED : JobStatus::STATUS_COMPLETED;

            $this->statusService->updateExportStatus($jobStatus, $status, $errorMessage);
        }

    }

    private function getFileName(BookXMLExportMessage $message): string
    {
        $project = $message->getProject();
        $folder = sprintf('export-xml2%s', $message->isClean() ? '-clean' : '');
        $path = sprintf('%s/public/files/book-%s', $this->rootDir, $folder);

        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        date_default_timezone_set('America/Chicago');

        return sprintf(
            '%s/%s-%s-(%s).zip',
            $path,
            $project->getShortCode(),
            date('F_j-Y_g:ia'),
            $message->getUserName()
        );
    }

    private function cleanupOldFiles(string $path, string $nodeId, string $currentFile): void
    {
        $iterator = new \DirectoryIterator($path);
        foreach ($iterator as $file) {
            if ($file->isDot()) {
                continue;
            }

            $fileName = $file->getFilename();
            if ($fileName === $currentFile) {
                continue;
            }

            if (!str_contains($fileName, $nodeId)) {
                continue;
            }

            $pathname = $file->getPathname();
            if ($file->isDir()) {
                $this->removeDirectory($pathname);
                continue;
            }

            if ($file->isFile() || $file->isLink()) {
                @unlink($pathname);
            }
        }
    }

    private function removeDirectory(string $directory): void
    {
        if (!is_dir($directory)) {
            return;
        }

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \FilesystemIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $item) {
            if ($item->isDir()) {
                @rmdir($item->getPathname());
            } else {
                @unlink($item->getPathname());
            }
        }

        @rmdir($directory);
    }
}
