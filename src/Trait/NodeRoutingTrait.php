<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Trait;

use App\Entity\CodeBook\AbstractCodeBookNode;

/**
 * Trait for generating routes to specific CodeBook nodes.
 * 
 * This trait provides functionality to generate URLs that redirect users
 * directly to the affected node in the book editor interface.
 */
trait NodeRoutingTrait
{
    /**
     * Generate a route URL for a specific CodeBook node.
     * 
     * @param string $projectId The project identifier
     * @param AbstractCodeBookNode $node The target node
     * @param string $chapterId The chapter identifier (used for non-chapter nodes)
     * @return string The generated route URL
     */
    public function routeForNode(string $projectId, AbstractCodeBookNode $node, string $chapterId): string
    {
        $nodeId = $node->getNodeId();
        $type   = $node->getDataType();

        return match ($type) {
            'chapter'    => sprintf('/book/%s/chapter/%s/edit/%s', $projectId, $nodeId, $nodeId),
            default      => sprintf('/book/%s/chapter/%s/edit/%s', $projectId, $chapterId, $nodeId),
        };
    }
}
