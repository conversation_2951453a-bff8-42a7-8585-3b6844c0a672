<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Dto\Xml2\Book;

use App\Entity\Project;

class BookReference
{
    public string $uuid = '';
    public string $id = '';
    public string $title = '';
    public bool $xml2Only = false;
    public ?string $ulid = null;
    public bool $isActive = true;

    public function __construct(Project $project)
    {
        $this->uuid = $project->getUuid();
        $this->id = $project->getShortCode();
        $this->title = $project->getBookTitle();
        $this->xml2Only = $project->isXml2only();
        $publication = $project->getCodeBook() ?? null;
        $this->ulid = $publication ? $publication->getUlid(): null ;
        $this->isActive = $project->isActive();
    }
}