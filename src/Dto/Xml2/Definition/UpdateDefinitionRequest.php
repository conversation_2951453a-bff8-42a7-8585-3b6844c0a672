<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Dto\Xml2\Definition;

use App\Dto\Xml2\Traits\QrCodeFieldsTrait;
use App\Enum\Status;
use Symfony\Component\Validator\Constraints as Assert;

class UpdateDefinitionRequest
{
    use QrCodeFieldsTrait;

    #[Assert\Choice(choices: Status::CASES)]
    public string $status = Status::IN_PROGRESS;

    // attributes
    public string $revisionBy = '';
    public string $revisionDateTime = '';
    public string $revision = '';
    public string $revisionGroup = '';
    public string $changed = '';
    public string $changedIn = '';
    public string $relocatedFromAttr = '';
    public string $indexNumber = '';

    // fields
    public string $committeeDesignation = '';
    public string $term = '';
    public string $definition = '';

//    public string $caption = '';
//    public string $legend = '';
//    public string $source = '';
//    public string $credit = '';
//    public string $creditTitle = '';
}
