<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Dto\Xml2\Definition;

use Symfony\Component\Validator\Constraints as Assert;

class MoveDefinitionRequest
{
    #[Assert\NotBlank]
    public string $parentNode = '';
    #[Assert\NotBlank]
    public string $neighbor = '';
    #[Assert\NotBlank]
    public string $term = '';
    public string $insertBefore = '';
}
