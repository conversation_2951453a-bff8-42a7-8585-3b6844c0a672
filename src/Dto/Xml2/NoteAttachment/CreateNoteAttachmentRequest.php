<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Dto\Xml2\NoteAttachment;

use Prugala\RequestDto\Dto\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

class CreateNoteAttachmentRequest implements RequestDtoInterface
{
    #[Assert\NotBlank]
    #[Assert\Type('string')]
    public string $file = '';

    #[Assert\NotBlank]
    #[Assert\Type('string')]
    public string $fileName = '';

    #[Assert\NotBlank]
    #[Assert\Type('string')]
    public string $noteType = '';
}
