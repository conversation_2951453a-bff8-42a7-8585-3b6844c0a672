<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Project\Action;

use App\Entity\Project\CodeChange\AppendixCodeChange;
use App\Entity\Project\CodeChange\ChapterCodeChange;
use App\Entity\Project\CodeChange\DefinitionCodeChange;
use App\Entity\Project\CodeChange\FigureCodeChange;
use App\Entity\Project\CodeChange\ProjectCodeChange;
use App\Entity\Project\CodeChange\PromulgatorCodeChange;
use App\Entity\Project\CodeChange\ReferenceStandardCodeChange;
use App\Entity\Project\CodeChange\SectionCodeChange;
use App\Entity\Project\CodeChange\TableCodeChange;
use App\Exception\ApiException;
use App\Helper\Xml2Helper;
use App\Service\CodeBook\ReferenceStandardYearParser;

use function explode;
use function is_numeric;
use function mb_split;
use function mb_strpos;
use function mb_strrpos;
use function mb_substr;
use function mb_trim;
use function preg_replace;
use function str_contains;
use function strip_tags;

class ProposalActionPathIdService
{
    public function updatePathId(ProjectCodeChange $action): string
    {
        $prefix = '';
        $number = '';
        $parentPathId = '';
        $originalXmlId = $action->getCdpCodeChange()->getOriginalXmlId();

        switch (true) {
            case $action instanceof AppendixCodeChange:
                $prefix = 'Appx';
                $number = $this->cleanNumber($action->getCdpCodeChange()->getOriginalOrdinal(),  $action->getNumber());
                $number = preg_replace('/^APPENDIX_/', '', $number);

                $parentPathId = 'BackMatter';
                break;

            case $action instanceof ChapterCodeChange:
                $prefix = 'Ch';
                $number = $this->cleanNumber($action->getCdpCodeChange()->getOriginalOrdinal(), $action->getNumber());
                $number = preg_replace('/^CHAPTER_/i', '', $number);
                if (is_numeric($number)) {
                    $number = sprintf('%02d', $number);
                }

                if (!empty($originalXmlId) && mb_strpos($originalXmlId, '_Pt')) {
                    $parentPathId = preg_replace('/(^.*?_)(Pt[^_]+)(_Ch.*$)/i', '$2', $originalXmlId);
                } else {
                    $parentPathId = 'Volume0';
                }
                break;

            case $action instanceof DefinitionCodeChange:
                $prefix = 'Def';
                $number = $this->cleanNumber($action->getCdpCodeChange()->getOriginalTitle(),  $action->getTerm());

                if (!empty($originalXmlId)) {
                    $parentPathId = preg_replace('/(^.*?_)([^_]+)(_Def.*$)/i', '$2', $originalXmlId);
                } else {
                    $parentPathId = 'Sec202';
                }
                break;

            case $action instanceof FigureCodeChange:
                $prefix = 'Fig';
                $number = $this->cleanNumber($action->getCdpCodeChange()->getOriginalOrdinal(), $action->getNumber());
                $number = preg_replace('/^FIGURE_/i', '', $number);

                $parentPathId = 'Sec' . preg_replace('/(.*?)_\d+$/i', '$1', $number);
                break;

            case $action instanceof PromulgatorCodeChange:
                $prefix = 'Prom';
                // cdp doesn't appear to store the "original" acronym in the title, parse it from the ID
                $originalNumber = !empty($originalXmlId)
                    ? mb_substr($originalXmlId, mb_strpos($originalXmlId, '_Prom') + 5)
                    : '';
                $number = $this->cleanNumber($originalNumber, $action->getAcronym());

                $parentPathId = preg_replace('/(^.*?_)(Ch\d+)(_Prom.*$)/i', '$2', $originalXmlId);
                break;

            case $action instanceof ReferenceStandardCodeChange:
                $prefix = 'RefStd';
                $numberSource = $action->getNumber();
                if ($numberSource === '') {
                    $original = $action->getCdpCodeChange()->getOriginalOrdinal();
                    if ($original !== '') {
                        [$numberSource] = ReferenceStandardYearParser::split($original);
                    }
                }
                $number = $this->cleanNumber('', $numberSource);

                if (!empty($action->getCdpCodeChange()->getPromulgatorAcronym())) {
                    $parentPathId = 'Prom' . $this->cleanNumber($action->getCdpCodeChange()->getPromulgatorAcronym(), '');
                }
                break;

            case $action instanceof SectionCodeChange:
                $prefix = 'Sec';
                $number = $this->cleanNumber($action->getCdpCodeChange()->getOriginalOrdinal(), $action->getNumber());
                $number = preg_replace('/^SECTION_/i', '', $number);

                if (str_contains($number, '.')) {
                    $parentPathId = 'Sec' . mb_substr($number, 0, mb_strrpos($number, '.'));
                } else {
                    if (!empty($originalXmlId)) {
                        $parentPathId = preg_replace('/(^.*?_)([^_]+)(_Sec.*$)/i', '$2', $originalXmlId);
                    } elseif (is_numeric($number)) {
                        $parentNumber = mb_substr($number, 0, mb_strlen($number) - 2);
                        $parentPathId = sprintf('Ch%02d', $parentNumber);
                    } else {
                        $parentPathId = preg_replace('/([A-Z])\d+/i', 'Appx$1', $number);
                    }
                }
                break;

            case $action instanceof TableCodeChange:
                $prefix = 'Tbl';
                $number = $this->cleanNumber($action->getCdpCodeChange()->getOriginalOrdinal(), $action->getNumber());
                $number = preg_replace('/^TABLE_/i', '', $number);

                $parentPathId = 'Sec' . preg_replace('/(.*?)_\d+$/i', '$1', $number);
                break;

            default: break;
        }

        $pathId = $prefix . $number;
        $action->setPathId($pathId);
        $action->setParentPathId($parentPathId);

        return $pathId;
    }

    public function cleanNumber(string $originalNumber, string $number): string
    {
        if (!empty($originalNumber)) {
            $number = $originalNumber;
        }

        try {
            $document = Xml2Helper::createDOMDocument($number, true);
        } catch (ApiException $exception) {
            return $this->sanitizePlainNumber($number);
        }

        if (null === $document->documentElement) {
            return $this->sanitizePlainNumber($number);
        }
        $xpath = Xml2Helper::createDOMXPath($document);

        $deleteNodes = $xpath->query('//x:*[name() = "del" or name() = "delete"]', $document->documentElement);
        for ($i = $deleteNodes->count() - 1; $i >= 0; --$i) {
            $node = $deleteNodes->item($i);
            $node->parentNode->removeChild($node);
        }

        $textContent = mb_trim($document->documentElement->textContent ?? '');
        // replace spaces, ( and - with _
        $textContent = preg_replace('#(\s+|\(|-)#', '_', $textContent);
        // removes all characters except letters, numbers, period and _
        $textContent = preg_replace('#[^\w\d._]#', '', $textContent);
        // reduce multiple _ to single
        $textContent = preg_replace('#_+#', '_', $textContent);
        // trims of . and _ from EOL
        $textContent = mb_rtrim($textContent, '._');

        return mb_strtoupper($textContent);
    }

    private function sanitizePlainNumber(string $value): string
    {
        // Drop deleted content entirely so fallback mirrors DOM branch behavior.
        $value = preg_replace('#<(?:delete|del)\b[^>]*>.*?</(?:delete|del)>#isu', '', $value);
        // Strip insert tags but keep their text.
        $value = preg_replace('#</?(?:insert|ins)\b[^>]*>#i', '', $value);
        $value = strip_tags((string) $value);
        $value = mb_trim($value);
        $value = preg_replace('#(\s+|\(|-)#', '_', $value);
        $value = preg_replace('#[^\w\d._]#', '', $value);
        $value = preg_replace('#_+#', '_', $value);
        $value = mb_rtrim($value, '._');

        return mb_strtoupper((string) $value);
    }
}
