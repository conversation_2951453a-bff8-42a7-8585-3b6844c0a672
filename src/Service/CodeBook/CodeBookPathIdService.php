<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\CopyrightPage;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Foreword;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\Index;
use App\Entity\CodeBook\IndexDivision;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Preface;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\PublisherNote;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\RelocatedFrom;
use App\Entity\CodeBook\RelocatedTo;
use App\Entity\CodeBook\SecondaryIndexEntry;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\TertiaryIndexEntry;
use App\Entity\CodeBook\TitlePage;
use App\Entity\CodeBook\Volume;
use App\Exception\ApiException;
use App\Helper\Xml2Helper;
use ReflectionClass;

use function array_filter;
use function array_unshift;
use function array_values;
use function count;
use function implode;
use function in_array;
use function is_numeric;
use function is_string;
use function max;
use function mb_strtoupper;
use function mb_substr;
use function mb_trim;
use function method_exists;
use function preg_replace;
use function spl_object_hash;
use function sprintf;
use function str_ends_with;
use function usort;

class CodeBookPathIdService
{

    /** @deprecated */
    public function getNodeId(AbstractCodeBookNode $node): string
    {
        $pathId = $this->updatePathId($node);

        if ($node->getParent()) {
            return sprintf('%s_%s', $node->getParent()->getNodeId(), $pathId);
        }
        return $pathId;
    }

    public function getPath(AbstractCodeBookNode $node): string
    {
        $segments = [$this->updatePathId($node)];
        $parent = $node->getParent();

        while (null !== $parent) {
            array_unshift($segments, $this->updatePathId($parent));
            $parent = $parent->getParent();
        }

        return implode('_', $segments);
    }

    public function getHumanReadablePath(AbstractCodeBookNode $node): string
    {
        try {
            $segments = [$this->updatePathId($node)];
            $parent = $node->getParent();

            while (null !== $parent) {
                array_unshift($segments, $this->updatePathId($parent));
                $parent = $parent->getParent();
            }

            return $this->withoutFirstSegment(implode('_', $segments));
        } catch (\Throwable $e) {
            return sprintf('[PATH_ERROR] Unable to build human-readable path : %s', $node->getUlid());
        }
    }

    public function updatePathId(AbstractCodeBookNode $node): string
    {
        $pathId = $this->buildPathId($node);
        $node->setPathId($pathId);

        return $pathId;
    }

    public function cleanNumber(string $originalNumber, string $number, bool $isDeleted): string
    {
        // if deleted, always use original number if it exists.
        if ($isDeleted && !empty($originalNumber)) {
            $number = $originalNumber;
        }

        if ($number === '') {
            return '';
        }

        try {
            $document = Xml2Helper::createDOMDocument($number, true);
        } catch (ApiException $exception) {
            return $this->sanitizePlainNumber($number);
        }

        if ($document->documentElement === null) {
            return $this->sanitizePlainNumber($number);
        }

        $xpath = Xml2Helper::createDOMXPath($document);

        $deleteNodes = $xpath->query('//x:delete', $document->documentElement);
        for ($i = $deleteNodes->count() - 1; $i >= 0; --$i) {
            $node = $deleteNodes->item($i);
            $node->parentNode->removeChild($node);
        }

        $textContent = mb_trim($document->documentElement->textContent);

        // if $number is empty, it probably was `<delete>x</delete>` only. Restore that.
        if (empty($textContent)) {
            $document = Xml2Helper::createDOMDocument($number, true);
            $textContent = mb_trim($document->documentElement->textContent);
        }

        // replace spaces, ( and - with _
        $textContent = preg_replace('#(\s+|\(|-|—|–)#', '_', $textContent);
        // removes all characters except letters, numbers, period and _
        $textContent = preg_replace('#[^\w\d._]#', '', $textContent);
        // reduce multiple _ to single
        $textContent = preg_replace('#_+#', '_', $textContent);
        // trims of . and _ from EOL
        $textContent = mb_rtrim($textContent, '._');

        return mb_strtoupper($textContent);
    }

    private function sanitizePlainNumber(string $value): string
    {
        $value = preg_replace('#<(?:delete|del)\\b[^>]*>.*?</(?:delete|del)>#isu', '', $value);
        $value = preg_replace('#</?(?:insert|ins)\\b[^>]*>#i', '', $value);
        $value = strip_tags((string) $value);
        $value = mb_trim($value);
        $value = preg_replace('#(\\s+|\\(|-|—|–)#', '_', $value);
        $value = preg_replace('#[^\\w\\d._]#', '', $value);
        $value = preg_replace('#_+#', '_', $value);
        $value = mb_rtrim($value, '._');

        return mb_strtoupper((string) $value);
    }

    private function buildPathId(AbstractCodeBookNode $node): string
    {
        $base = $this->buildBasePathId($node);

        if ($base === '') {
            return '';
        }

        $collisionSuffix = $this->resolveCollisionSuffix($node, $base);

        return $collisionSuffix === '' ? $base : sprintf('%s_%s', $base, $collisionSuffix);
    }

    private function buildBasePathId(AbstractCodeBookNode $node): string
    {
        [$prefix, $number] = $this->determinePrefixAndNumber($node);

        $pathId = $prefix . $number;

        if ($pathId === '') {
            return '';
        }

        if ($node->isDeleted()) {
            $pathId .= '-deleted';
        }

        return $pathId;
    }

    /**
     * @return array{string, string}
     */
    private function determinePrefixAndNumber(AbstractCodeBookNode $node): array
    {
        $prefix = '';
        $number = '';

        switch (true) {
            case $node instanceof Appendix:
                $prefix = 'Appx';
                $number = $this->cleanNumber($node->getOriginalNumber(), $node->getNumber(), $node->isDeleted());
                break;

            case $node instanceof BackMatter:
                $prefix = 'BackMatter';
                break;

            case $node instanceof Chapter:
                $prefix = 'Ch';
                $number = $this->cleanNumber($node->getOriginalNumber(), $node->getNumber(), $node->isDeleted());
                if (is_numeric($number)) {
                    $number = sprintf('%02d', $number);
                }
                break;

            case $node instanceof CopyrightPage:
                $prefix = 'Copyright';
                break;

            case $node instanceof Definition:
                $prefix = 'Def';
                $number = $this->cleanNumber('', $node->getTerm(), $node->isDeleted());
                break;

            case $node instanceof DefinitionList:
                $prefix = 'DefList';
                $number = (string) $node->getPosition();
                break;

            case $node instanceof Figure:
                $prefix = 'Fig';
                $number = $this->cleanNumber($node->getOriginalNumber(), $node->getNumber(), $node->isDeleted());
                break;

            case $node instanceof Foreword:
                $prefix = 'Foreword';
                break;

            case $node instanceof FrontMatter:
                $prefix = 'FrontMatter';
                break;

            case $node instanceof Index:
                $prefix = 'Index';
                break;

            case $node instanceof IndexDivision:
                $prefix = 'IndexDiv';
                $number = $this->cleanNumber('', $node->getTitle(), $node->isDeleted());
                $number = mb_substr($number, 0, 1);
                break;

            case $node instanceof IndexEntry:
            case $node instanceof SecondaryIndexEntry:
            case $node instanceof TertiaryIndexEntry:
                $prefix = 'Index';
                $number = $this->cleanNumber('', $node->getTerm(), $node->isDeleted());
                break;

            case $node instanceof Preface:
                $prefix = 'Preface';
                break;

            case $node instanceof Promulgator:
                $prefix = 'Prom';
                $number = $this->cleanNumber('', $node->getAcronym(), $node->isDeleted());
                break;

            case $node instanceof Publication:
                $number = $node->getNodeId();
                break;

            case $node instanceof PublisherNote:
                $prefix = 'PublisherNote';
                break;

            case $node instanceof ReferenceStandard:
                $prefix = 'RefStd';
                $canonical = $node->getNumber();
                if ($canonical === '' && $node->getOriginalNumber() !== '') {
                    [$canonical] = ReferenceStandardYearParser::split($node->getOriginalNumber());
                }
                $number = $canonical === ''
                    ? ''
                    : $this->cleanNumber('', $canonical, $node->isDeleted());
                break;

            case $node instanceof RelocatedFrom:
                $prefix = 'RelocatedFrom';
                break;

            case $node instanceof RelocatedTo:
                $prefix = 'RelocatedTo';
                break;

            case $node instanceof Section:
                $prefix = 'Sec';
                $number = $this->cleanNumber($node->getOriginalNumber(), $node->getNumber(), $node->isDeleted());
                if ($number === '') {
                    $number = (string) $node->getPosition();
                }
                break;

            case $node instanceof Table:
                $prefix = 'Tbl';
                $number = $this->cleanNumber($node->getOriginalNumber(), $node->getNumber(), $node->isDeleted());
                break;

            case $node instanceof TitlePage:
                $prefix = 'TitlePage';
                break;

            case $node instanceof Volume:
                $prefix = 'Vol';
                $number = (string) $node->getPosition();
                break;

            default:
                $prefix = (new ReflectionClass($node))->getShortName();
                break;
        }

        return [$prefix, $number];
    }

    private function resolveCollisionSuffix(AbstractCodeBookNode $node, string $basePathId): string
    {
        $parent = $node->getParent();
        if ($parent === null) {
            return '';
        }

        $siblings = $parent->getChildren();
        if ($siblings === []) {
            return '';
        }

        $duplicates = array_filter(
            $siblings,
            fn(AbstractCodeBookNode $sibling): bool => $this->buildBasePathId($sibling) === $basePathId
        );

        if (!in_array($node, $duplicates, true)) {
            $duplicates[] = $node;
        }
        $duplicates = array_values($duplicates);

        if (count($duplicates) <= 1) {
            return '';
        }

        usort($duplicates, static fn(AbstractCodeBookNode $a, AbstractCodeBookNode $b
        ): int => $a->getPosition() <=> $b->getPosition());

        $tokenMap = [];
        $usedTokens = [];

        foreach ($duplicates as $duplicate) {
            $token = $this->buildCollisionToken($duplicate, $basePathId);
            if ($token === '') {
                $token = sprintf('POS%03d', max(0, $duplicate->getPosition()));
            }

            $normalizedToken = $token;
            $suffix = 2;
            while (in_array($normalizedToken, $usedTokens, true)) {
                $normalizedToken = sprintf('%s_%d', $token, $suffix);
                ++$suffix;
            }

            $usedTokens[] = $normalizedToken;
            $tokenMap[spl_object_hash($duplicate)] = $normalizedToken;
        }

        return $tokenMap[spl_object_hash($node)] ?? '';
    }

    private function buildCollisionToken(AbstractCodeBookNode $node, string $basePathId): string
    {
        $reflection = new ReflectionClass($node);
        $candidates = [];

        if (method_exists($node, 'getLabel')) {
            $candidates[] = $node->getLabel();
        }
        if (method_exists($node, 'getRole')) {
            $candidates[] = $node->getRole();
        }
        if (method_exists($node, 'getTitle')) {
            $candidates[] = $node->getTitle();
        }
        if (method_exists($node, 'getTerm')) {
            $candidates[] = $node->getTerm();
        }
        if (method_exists($node, 'getNumber')) {
            $candidates[] = $node->getNumber();
        }

        $candidates[] = $reflection->getShortName();

        foreach ($candidates as $candidate) {
            if (!is_string($candidate) || $candidate === '') {
                continue;
            }

            $token = $this->normalizeFreeFormToken($candidate);
            if ($token !== '') {
                if (str_ends_with($basePathId, $token)) {
                    $classToken = $this->normalizeFreeFormToken($reflection->getShortName());
                    if ($classToken !== '' && $classToken !== $token) {
                        $token = $classToken;
                    }
                }
                return $token;
            }
        }

        return '';
    }

    private function normalizeFreeFormToken(string $value): string
    {
        $value = mb_trim($value);
        if ($value === '') {
            return '';
        }

        $value = preg_replace('#(\s+|\(|-|—|–)#u', '_', $value);
        $value = preg_replace('#[^\w\d._]#u', '', $value);
        $value = preg_replace('#_+#', '_', $value);
        $value = mb_trim($value, '_');

        return mb_strtoupper($value);
    }

    private function withoutFirstSegment(?string $s): string
    {
        if (!$s) {
            return '';
        }
        $pos = strpos($s, '_');
        return $pos === false ? '' : substr($s, $pos + 1);
    }
}
