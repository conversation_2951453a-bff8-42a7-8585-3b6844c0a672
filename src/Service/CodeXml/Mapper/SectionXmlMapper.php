<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\CodeXml\Mapper;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Section;
use App\Exception\ApiException;
use App\Service\CodeXml\CodeXmlNode;
use DOMDocument;
use DOMXPath;

use function assert;
use function mb_trim;
use function preg_replace;
use function sprintf;

class SectionXmlMapper extends AbstractXmlMapper
{
    /** @throws ApiException */
    public function map(AbstractCodeBookNode $entity, DOMDocument $document, DOMXPath $xpath): CodeXmlNode
    {
        assert($entity instanceof Section);
        $expr = sprintf('//x:*[(local-name() = "section" or starts-with(local-name(), "level-")) and @ct-uuid="%s"][1]', $entity->getUlid());
        return $this->findNode($entity, $document, $xpath, $expr);
    }

    public function syncEntity(CodeXmlNode $codeXmlNode, DOMXPath $xpath): bool
    {
        if (!$codeXmlNode->hasChanges()) {
            return true;
        }

        $domEl = $codeXmlNode->getDomElement();
        $entity = $codeXmlNode->getEntity();
        assert($entity instanceof Section);

        $this->syncCommonAttributes($entity, $domEl);
        $this->syncRevisionAttributes($entity, $domEl);
        $this->syncTitleGroup($entity, $domEl, $xpath);

        // attributes
        $entity->setIndexNumber($domEl->getAttribute('indexnum'));
        $entity->setTocEntry('yes' === $domEl->getAttribute('tocentry'));
        $entity->setReserveCount($this->getIntAttribute($domEl, 'reservecount'));
        $entity->setDisplayLevel($this->getIntAttribute($domEl, 'disp-level', 1));

        // fields
        $entity->setAbstract($this->getElementValue($domEl, $xpath, './x:abstract[1]'));
        $entity->setAbstractTitle($this->getElementValue($domEl, $xpath, './x:abstract[1]/x:titlegroup[1]/x:title[1]'));
        $entity->setKeywords($this->getElementValue($domEl, $xpath, './x:keywords[1]'));
        $entity->setKeywordsTitle($this->getElementValue($domEl, $xpath, './x:keywords[1]/x:titlegroup[1]/x:title[1]'));
        $entity->setBody($this->getElementValue($domEl, $xpath, './x:body[1]'));

        // temporary:
        $removeTitleGroup = [
            'abstract' => $entity->getAbstract(),
            'keywords' => $entity->getKeywords(),
        ];
        $replaced = preg_replace(self::REGEX_TITLE_GROUP, '', $removeTitleGroup);
        $entity->setAbstract(mb_trim($replaced['abstract']));
        $entity->setKeywords(mb_trim($replaced['keywords']));

        return true;
    }
}
