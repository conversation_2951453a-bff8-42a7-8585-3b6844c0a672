<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\CodeXml;

use App\Entity\CodeBook;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use DOMDocument;
use DOMXPath;

class CodeXmlService
{
    private array $cache = [];

    public function __construct(
        private readonly Mapper\AppendixXmlMapper            $appendixXmlMapper,
        private readonly Mapper\BackMatterXmlMapper          $backMatterXmlMapper,
        private readonly Mapper\ChapterXmlMapper             $chapterXmlMapper,
        private readonly Mapper\CopyrightPageXmlMapper       $copyrightPageXmlMapper,
        private readonly Mapper\DefinitionXmlMapper          $definitionXmlMapper,
        private readonly Mapper\DefinitionListXmlMapper      $definitionListXmlMapper,
        private readonly Mapper\FigureXmlMapper              $figureXmlMapper,
        private readonly Mapper\ForewordXmlMapper            $forewordXmlMapper,
        private readonly Mapper\FrontMatterXmlMapper         $frontMatterXmlMapper,
        private readonly Mapper\IndexXmlMapper               $indexXmlMapper,
        private readonly Mapper\IndexDivisionXmlMapper       $indexDivisionXmlMapper,
        private readonly Mapper\IndexEntryXmlMapper          $indexEntryXmlMapper,
        private readonly Mapper\PrefaceXmlMapper             $prefaceXmlMapper,
        private readonly Mapper\PromulgatorXmlMapper         $promulgatorXmlMapper,
        private readonly Mapper\PublicationXmlMapper         $publicationXmlMapper,
        private readonly Mapper\PublisherNoteXmlMapper       $publisherNoteXmlMapper,
        private readonly Mapper\ReferenceStandardXmlMapper   $referenceStandardXmlMapper,
        private readonly Mapper\RelocatedFromXmlMapper       $relocatedFromXmlMapper,
        private readonly Mapper\RelocatedToXmlMapper         $relocatedToXmlMapper,
        private readonly Mapper\SecondaryIndexEntryXmlMapper $secondaryIndexEntryXmlMapper,
        private readonly Mapper\SectionXmlMapper             $sectionXmlMapper,
        private readonly Mapper\TableXmlMapper               $tableXmlMapper,
        private readonly Mapper\TertiaryIndexEntryXmlMapper  $tertiaryIndexEntryXmlMapper,
        private readonly Mapper\TitlePageXmlMapper           $titlePageXmlMapper,
        private readonly Mapper\VolumeXmlMapper              $volumeXmlMapper,
        private readonly CodeBookToXmlMapper                 $codeBookToXmlMapper,
        private readonly Xml2Encoder                         $xml2Encoder,
    ) {
    }

    public function create(AbstractCodeBookNode $entity): CodeXml
    {
        $splHash = spl_object_hash($entity);
        if (isset($this->cache[$splHash])) {
            return $this->cache[$splHash];
        }

        $xml2element = $this->codeBookToXmlMapper->map($entity);
        $xml = $this->xml2Encoder->encode($xml2element);

        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);
        $codeXml = new CodeXml($document, $xpath);

        foreach ($entity as $i) {
//            if ($i instanceof CodeBook\Promulgator
//                || $i instanceof CodeBook\ReferenceStandard
//                || $i instanceof CodeBook\IndexEntry) {
//                continue;
//            }
            $codeXml->addChild($this->mapEntity($i, $document, $xpath));
        }

        $this->cache[$splHash] = $codeXml;

        return $codeXml;
    }

    protected function mapEntity(AbstractCodeBookNode $entity, DOMDocument $document, DOMXPath $xpath): CodeXmlNode
    {
        return match (true) {
            $entity instanceof CodeBook\Appendix            => $this->appendixXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\BackMatter          => $this->backMatterXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Chapter             => $this->chapterXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\CopyrightPage       => $this->copyrightPageXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Definition          => $this->definitionXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\DefinitionList      => $this->definitionListXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Figure              => $this->figureXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Foreword            => $this->forewordXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\FrontMatter         => $this->frontMatterXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Index               => $this->indexXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\IndexDivision       => $this->indexDivisionXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\IndexEntry          => $this->indexEntryXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Preface             => $this->prefaceXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Promulgator         => $this->promulgatorXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Publication         => $this->publicationXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\PublisherNote       => $this->publisherNoteXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\ReferenceStandard   => $this->referenceStandardXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\RelocatedFrom       => $this->relocatedFromXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\RelocatedTo         => $this->relocatedToXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\SecondaryIndexEntry => $this->secondaryIndexEntryXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Section             => $this->sectionXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Table               => $this->tableXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\TertiaryIndexEntry  => $this->tertiaryIndexEntryXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\TitlePage           => $this->titlePageXmlMapper->map($entity, $document, $xpath),
            $entity instanceof CodeBook\Volume              => $this->volumeXmlMapper->map($entity, $document, $xpath),
            default                                         => null,
        };
    }

    public function sync(CodeXml $codeXml): void
    {
        $xpath = $codeXml->getXpath();

        foreach ($codeXml->getChildNodes() as $childNode) {
            $this->syncEntity($childNode, $xpath);
        }
    }

    private function syncEntity(CodeXmlNode $codeXmlNode, DOMXPath $xpath): void
    {
        $entity = $codeXmlNode->getEntity();
        $synced = match (true) {
            $entity instanceof CodeBook\Appendix            => $this->appendixXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\BackMatter          => $this->backMatterXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Chapter             => $this->chapterXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\CopyrightPage       => $this->copyrightPageXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Definition          => $this->definitionXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\DefinitionList      => $this->definitionListXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Figure              => $this->figureXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Foreword            => $this->forewordXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\FrontMatter         => $this->frontMatterXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Index               => $this->indexXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\IndexDivision       => $this->indexDivisionXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\IndexEntry          => $this->indexEntryXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Preface             => $this->prefaceXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Promulgator         => $this->promulgatorXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Publication         => $this->publicationXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\PublisherNote       => $this->publisherNoteXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\ReferenceStandard   => $this->referenceStandardXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\RelocatedFrom       => $this->relocatedFromXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\RelocatedTo         => $this->relocatedToXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\SecondaryIndexEntry => $this->secondaryIndexEntryXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Section             => $this->sectionXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Table               => $this->tableXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\TertiaryIndexEntry  => $this->tertiaryIndexEntryXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\TitlePage           => $this->titlePageXmlMapper->syncEntity($codeXmlNode, $xpath),
            $entity instanceof CodeBook\Volume              => $this->volumeXmlMapper->syncEntity($codeXmlNode, $xpath),
            default                                         => false,
        };
    }
}
