<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service;

use App\Dto\Xml2\Chapter\GetChapterVersionsListResponse;
use App\Dto\Xml2\Chapter\GetChapterVersionsResponse;
use App\Dto\Xml2\Chapter\VersionResponse;
use App\Entity\ChapterPdfVersion;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\File\RemoteFile;
use App\Entity\Project;
use App\Entity\User\User;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Repository\ChapterPdfVersionRepository;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeBook\CodeBookPathIdService;
use App\Service\Xml2\ReportService;
use App\Traits\NotesIncludedTrait;
use App\Traits\ReportsName;
use DateTimeImmutable;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;

use function base64_encode;
use function hash;
use function method_exists;
use function unlink;

class ChapterPdfVersionService
{
    use NotesIncludedTrait;
    use ReportsName;

    public function __construct(
        private readonly EntityManagerInterface      $em,
        private readonly ChapterPdfVersionRepository $repository,
        private readonly ReportService               $reportService,
        private readonly CodeBookToXmlMapper         $codeBookToXmlMapper,
        private readonly CodeBookPathIdService       $pathIdService,
        private readonly Xml2Encoder                 $xml2Encoder,
    ) {
    }

    public function createPdfVersionOnStatusChange(
        AbstractCodeBookNode $chapter,
        Project              $project,
        string               $newStatus,
        ?User                $user = null
    ): array {
        $versionWithoutNotes = null;
        $versionWithNotes = null;
        $errors = [];

        try {
            $versionWithoutNotes = $this->createSinglePdfVersion(
                $chapter,
                $project,
                $newStatus,
                $user,
                'none'
            );
        } catch (\Exception $e) {
            $errors['without_notes'] = $e->getMessage();
            // Create error PDF instead of failing completely
            $versionWithoutNotes = $this->createErrorPdfVersion(
                $chapter,
                $project,
                $newStatus,
                $user,
                'none',
                $e->getMessage()
            );
        }

        try {
            $versionWithNotes = $this->createSinglePdfVersion(
                $chapter,
                $project,
                $newStatus,
                $user,
                'all'
            );
        } catch (\Exception $e) {
            $errors['with_notes'] = $e->getMessage();
            $versionWithNotes = $this->createErrorPdfVersion(
                $chapter,
                $project,
                $newStatus,
                $user,
                'all',
                $e->getMessage()
            );
        }

        $result = [
            'without_notes' => $versionWithoutNotes,
            'with_notes'    => $versionWithNotes,
        ];

        if (!empty($errors)) {
            $result['errors'] = $errors;
        }

        return $result;
    }

    private function createErrorPdfVersion(
        AbstractCodeBookNode $chapter,
        Project              $project,
        string               $status,
        ?User                $user,
        string               $notesType,
        string               $errorMessage
    ): ChapterPdfVersion {
        $filename = $this->generateFilename($project, $chapter, $status, $notesType);

        $pdfVersion = new ChapterPdfVersion();
        $pdfVersion->setCodeBookNode($chapter);
        $pdfVersion->setProject($project);
        $pdfVersion->setFilename('[ERROR] ' . $filename);
        $pdfVersion->setStatus($status);
        $pdfVersion->setCreatedBy($user);
        $pdfVersion->setCreatedByEmail($user?->getUserIdentifier() ?? 'system');
        $pdfVersion->setErrorMessage($errorMessage);

        $this->em->persist($pdfVersion);
        $this->em->flush();

        return $pdfVersion;
    }

    private function createSinglePdfVersion(
        AbstractCodeBookNode $chapter,
        Project              $project,
        string               $status,
        ?User                $user,
        string               $notesType
    ): ChapterPdfVersion {

        $filename = $this->generateFilename($project, $chapter, $status, $notesType);

        $pdfVersion = new ChapterPdfVersion();
        $pdfVersion->setCodeBookNode($chapter);
        $pdfVersion->setProject($project);
        $pdfVersion->setFilename($filename);
        $pdfVersion->setStatus($status);
        $pdfVersion->setCreatedBy($user);
        $pdfVersion->setCreatedByEmail($user?->getUserIdentifier() ?? 'system');

        $pdfContent = $this->generatePdf($chapter, $notesType);

        $remoteFile = $this->createRemoteFile($pdfContent, $filename);
        $pdfVersion->setPdfFile($remoteFile);

        $this->em->persist($pdfVersion);
        $this->em->flush();

        return $pdfVersion;
    }

    public function createPdfVersion(
        AbstractCodeBookNode $chapter,
        Project              $project,
        ?User                $user = null,
        ?string              $notesType = 'none'
    ): ChapterPdfVersion {
        return $this->createSinglePdfVersion(
            $chapter,
            $project,
            $chapter->getStatus(),
            $user,
            $notesType ?? 'none'
        );
    }

    private function createRemoteFile(string $pdfContent, string $filename): RemoteFile
    {
        $hash = hash('sha256', $pdfContent);
        $existingFile = $this->em->getRepository(RemoteFile::class)->findOneBy(['hash' => $hash]);
        if ($existingFile) {
            return $existingFile;
        }

        $dataUri = 'data:application/pdf;base64,' . base64_encode($pdfContent);

        $remoteFile = new RemoteFile();
        $remoteFile->setHash($hash);
        $remoteFile->setMimeType('application/pdf');
        $remoteFile->setContents($dataUri);

        $this->em->persist($remoteFile);
        $this->em->flush();

        return $remoteFile;
    }

    public function downloadPdf(ChapterPdfVersion $version): string
    {
        if ($version->hasError()) {
            throw new \RuntimeException(
                sprintf('PDF generation failed: %s', $version->getErrorMessage())
            );
        }

        $content = $version->getPdfContent();

        if ($content === null) {
            throw new \RuntimeException('PDF content not found for version ' . $version->getId());
        }

        return $content;
    }

    public function getChapterVersions(Project $project, string $baseUrl): GetChapterVersionsListResponse
    {
        $versionsByChapter = $this->repository->findByProjectGroupedByChapter($project);

        $output = new GetChapterVersionsListResponse();
        $output->versionList = [];

        if (empty($versionsByChapter)) {
            return $output;
        }

        foreach ($versionsByChapter as $versions) {
            if (empty($versions)) {
                continue;
            }

            $firstVersion = $versions[0];
            $chapter = $firstVersion->getCodeBookNode();

            if (!$chapter) {
                continue;
            }

            $chapterResponse = new GetChapterVersionsResponse();
            $chapterResponse->id = $chapter->getNodeId();
            $chapterResponse->label = method_exists($chapter, 'getLabel') ? $chapter->getLabel() : '';
            $chapterResponse->number = method_exists($chapter, 'getNumber') ? $chapter->getNumber() : '';
            $chapterResponse->title = method_exists($chapter, 'getTitle') ? $chapter->getTitle() : '';

            $chapterResponse->versions = [];
            foreach ($versions as $version) {
                if ($version->hasError()) {
                    // For error versions, don't provide a URL since there's no PDF to download
                    $chapterResponse->versions[] = new VersionResponse(
                        $version->getFilename(),
                        null,
                        $version->getErrorMessage()
                    );
                } else {
                    $url = $baseUrl . '/pdf-version/' . $version->getId() . '/' . $version->getFilename();
                    $chapterResponse->versions[] = new VersionResponse($version->getFilename(), $url);
                }
            }

            $output->versionList[] = $chapterResponse;
        }

        return $output;
    }


    //TODO:
    private function hasProjectAccess(Project $project, User $user): bool
    {
        if (
            $user->hasRole('ROLE_SUPER_ADMIN') ||
            ($user->hasRole('ROLE_ADMIN') && $user->getCategories()->count() == 0)
        ) {
            return true;
        }

        foreach ($project->getUsers() as $projectUser) {
            if ($projectUser->getId() === $user->getId()) {
                return true;
            }
        }

        $projectCategory = $project->getProjectCategory();
        if ($projectCategory) {
            foreach ($projectCategory->getUsers() as $categoryUser) {
                if ($categoryUser->getId() === $user->getId()) {
                    return true;
                }
            }
        }

        return false;
    }

    private function generatePdf(AbstractCodeBookNode $chapter, ?string $notesType = 'none'): string
    {
        try {
            if ($notesType !== null && $notesType !== 'none') {
                if ($chapter instanceof Appendix || $chapter instanceof Chapter || $chapter instanceof Section) {
                    $body = $this->appendInternalNotesToBody($chapter->getBody(), $chapter, $notesType);
                    $chapter->setBody($body);
                } elseif ($chapter instanceof Table) {
                    $table = $this->appendInternalNotesToBody($chapter->getTable(), $chapter, $notesType);
                    $chapter->setTable($table);
                } elseif ($chapter instanceof Figure) {
                    $media = $this->appendInternalNotesToBody($chapter->getMedia(), $chapter, $notesType);
                    $chapter->setMedia($media);
                }
            }

            $chapter->setPdfFormat('redline');
            $xml2Element = $this->codeBookToXmlMapper->map($chapter);
            $xml = $this->xml2Encoder->encode($xml2Element);

            // Reset it so the value isn't persisted to the database
            $chapter->setPdfFormat('');

            $filename = sprintf('%s_%s.zip', 'chapter_pdf_version', uniqid());
            $zip = new \ZipArchive();

            if ($zip->open($filename, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== true) {
                throw new \RuntimeException('Failed to create ZIP file: ' . $filename);
            }

            $zip->addFromString('file.xml', $xml);
            $zip->close();
            $response = $this->reportService->getPdfFile($filename);
            $pdfContent = $response->getContent();

            // Clean up the temporary ZIP file
            if (file_exists($filename)) {
                unlink($filename);
            }

            return $pdfContent;
        } catch (\Exception $e) {
            // Clean up the temporary ZIP file if it exists
            $filename = $filename ?? sprintf('%s_%s.zip', 'chapter_pdf_version', uniqid());
            if (file_exists($filename)) {
                unlink($filename);
            }

            $chapterInfo = sprintf('Chapter: %s (ID: %s)',
                $chapter->getNodeId() ?? 'Unknown',
                $chapter->getId() ?? 'Unknown'
            );
            throw new \RuntimeException(
                sprintf('PDF generation failed for %s. Error: %s', $chapterInfo, $e->getMessage()),
                0,
                $e
            );
        }
    }

    private function generateFilename(
        Project              $project,
        AbstractCodeBookNode $chapter,
        string               $status,
        string               $notesType
    ): string {
        $bookId = $project->getShortCode();
        $chapterId = $this->pathIdService->getHumanReadablePath($chapter);
        $humanReadableChapterStatus = $this->convertChapterStatus($status);
        $now = new DateTimeImmutable('now', new DateTimeZone('UTC'));
        $date = $now->format('Y-m-d_H-i-s');
        $notesIndicator = ($notesType === 'none') ? '' : 'NOTES';

        return sprintf(
            '%s_%s_%s_%s_%s.pdf',
            $bookId,
            $chapterId,
            $date,
            $humanReadableChapterStatus,
            $notesIndicator
        );
    }
}
