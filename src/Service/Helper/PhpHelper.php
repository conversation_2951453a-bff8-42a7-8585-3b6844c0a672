<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Helper;

use Psr\Log\NullLogger;

/**
 * @codeCoverageIgnore
 */
final class PhpHelper
{
    /**
     * @var bool
     */
    private static $init = false;

    /**
     * @var NullLogger|null
     */
    private static $logger = null;

    private static function init(): void
    {
        if (false === self::$init) {
            self::$init = true;
            self::$logger = new NullLogger();
        }
    }

    public static function setUnlimitedLimits(): array|bool
    {
        PhpHelper::init();

        $setTimeLimit = self::setTimeLimit();
        $setMaxExecutionTimeLimit = self::setMaxExecutionTimeLimit();
        $setMemoryLimit = self::setMemoryLimit();
        $setMysqlConnectTimeoutLimit = self::setMysqlConnectTimeoutLimit();

        $status = true;
        $errors = [];

        if (false === $setTimeLimit) {
            $status = false;
            $errors[] = 'setTimeLimit';
        }

        if (false === $setMaxExecutionTimeLimit) {
            $status = false;
            $errors[] = 'setMaxExecutionTimeLimit';
        }

        if (false === $setMemoryLimit) {
            $status = false;
            $errors[] = 'setMemoryLimit';
        }

        if (false === $setMysqlConnectTimeoutLimit) {
            $status = false;
            $errors[] = 'setMysqlConnectTimeoutLimit';
        }

        return $status ? $status : $errors;
    }

    /**
     * @return array|bool
     */
    public static function setUnlimitedInputLimits()
    {
        PhpHelper::init();

        $setMaxInputTimeLimit = self::setMaxInputTimeLimit();
        $setPostMaxSizeLimit = self::setPostMaxSizeLimit();
        $setUploadMaxFilesizeLimit = self::setUploadMaxFilesizeLimit();

        $status = true;
        $errors = [];

        if (false === $setMaxInputTimeLimit) {
            $status = false;
            $errors[] = 'setMaxInputTimeLimit';
        }

        if (false === $setPostMaxSizeLimit) {
            $status = false;
            $errors[] = 'setPostMaxSizeLimit';
        }

        if (false === $setUploadMaxFilesizeLimit) {
            $status = false;
            $errors[] = 'setUploadMaxFilesizeLimit';
        }

        return $status ? $status : $errors;
    }

    /**
     * @return array|bool
     */
    public static function setInputLimits(?string $value = '2G')
    {
        PhpHelper::init();

        $setPostMaxSizeLimit = self::setPostMaxSizeLimit($value);
        $setUploadMaxFilesizeLimit = self::setUploadMaxFilesizeLimit($value);

        $status = true;
        $errors = [];

        if (false === $setPostMaxSizeLimit) {
            $status = false;
            $errors[] = 'setPostMaxSizeLimit';
        }

        if (false === $setUploadMaxFilesizeLimit) {
            $status = false;
            $errors[] = 'setUploadMaxFilesizeLimit';
        }

        return $status ? $status : $errors;
    }

    /**
     * @param int|null $value
     *
     * @return bool
     */
    public static function setTimeLimit(?int $value = 0): bool
    {
        PhpHelper::init();

        $result = \set_time_limit($value);

        if (false === $result) {
            self::$logger->error(\sprintf('%s::%s: could not set value: %s', \get_called_class(), __FUNCTION__, $value));
        }

        return $result;
    }

    /**
     * @param string|null $value
     *
     * @return false|string
     */
    public static function setMaxExecutionTimeLimit(?string $value = '0')
    {
        PhpHelper::init();

        $result = \ini_set('max_execution_time', $value);

        if (false === $result) {
            self::$logger->error(\sprintf('%s::%s: could not set value: %s', \get_called_class(), __FUNCTION__, $value));
        }

        return $result;
    }

    /**
     * @param string|null $value
     *
     * @return false|string
     */
    public static function setMemoryLimit(?string $value = '-1')
    {
        PhpHelper::init();

        $result = \ini_set('memory_limit', $value);

        if (false === $result) {
            self::$logger->error(\sprintf('%s::%s: could not set value: %s', \get_called_class(), __FUNCTION__, $value));
        }

        return $result;
    }

    /**
     * @param string|null $value
     *
     * @return false|string
     */
    public static function setMysqlConnectTimeoutLimit(?string $value = '0')
    {
        PhpHelper::init();

        $result = \ini_set('mysql.connect_timeout', $value);

        if (false === $result) {
            self::$logger->error(\sprintf('%s::%s: could not set value: %s', \get_called_class(), __FUNCTION__, $value));
        }

        return $result;
    }

    /**
     *
     * @param string|null $value
     *
     * @return false|string
     */
    public static function setMaxInputTimeLimit(?string $value = '-1')
    {
        PhpHelper::init();

        $result = \ini_set('max_input_time', $value);

        if (false === $result) {
            self::$logger->error(\sprintf('%s::%s: could not set value: %s', \get_called_class(), __FUNCTION__, $value));
        }

        return $result;
    }

    /**
     * value above 2G could cause complications: https://stackoverflow.com/questions/4614147/uploading-a-file-larger-than-2gb-using-php
     *
     * @param string|null $value
     *
     * @return false|string
     */
    public static function setPostMaxSizeLimit(?string $value = '2G')
    {
        PhpHelper::init();

        $result = \ini_set('post_max_size', $value);

        if (false === $result) {
            self::$logger->error(\sprintf('%s::%s: could not set value: %s', \get_called_class(), __FUNCTION__, $value));
        }

        return $result;
    }

    /**
     * value above 2G could cause complications: https://stackoverflow.com/questions/4614147/uploading-a-file-larger-than-2gb-using-php
     *
     * @param string|null $value
     *
     * @return false|string
     */
    public static function setUploadMaxFilesizeLimit(?string $value = '2G')
    {
        PhpHelper::init();

        $result = \ini_set('upload_max_filesize', $value);

        if (false === $result) {
            self::$logger->error(\sprintf('%s::%s: could not set value: %s', \get_called_class(), __FUNCTION__, $value));
        }

        return $result;
    }
}
