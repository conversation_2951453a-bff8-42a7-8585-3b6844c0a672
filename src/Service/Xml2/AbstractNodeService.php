<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Exception\ApiException;
use App\Repository\CodeBookNodeRepository;
use App\Service\Helper\PhpHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

abstract class AbstractNodeService
{
    public function __construct(
        protected readonly EntityManagerInterface $em,
        protected readonly CodeBookNodeRepository $repo,
        protected readonly Security               $security,
    ) {
    }

    /** @throws ApiException */
    protected function moveToParent(
        Publication          $codeBook,
        AbstractCodeBookNode $node,
        string               $parentNodeId,
        string               $insertBeforeId
    ): void {
        PhpHelper::setTimeLimit();

        $parentNode = $codeBook->getNodeById($parentNodeId);
        if (null === $parentNode) {
            throw new ApiException(sprintf('Parent node "%s" was not found.', $parentNodeId));
        }

        $insertBefore = null;
        if (!empty($insertBeforeId)) {
            foreach ($parentNode->getChildren() as $child) {
                if ($insertBeforeId === $child->getNodeId()) {
                    $insertBefore = $child;
                    break;
                }
            }
        } elseif ($node instanceof Figure || $node instanceof Table) {
            // If no insertBeforeId is provided, we look for the first Section child to insert before
            foreach ($parentNode->getChildren() as $child) {
                if ($child instanceof Section) {
                    $insertBefore = $child;
                    break;
                }
            }
        }

        $parentNode->insertBefore($node, $insertBefore);
    }

    /** @throws ApiException */
    protected function moveToParentByAction(
        Publication          $codeBook,
        AbstractCodeBookNode $node,
        string               $parentNodeId,
        ?string              $neighborId = null,
        ?string              $action = 'before'
    ): void {
        PhpHelper::setTimeLimit();

        $parentNode = $codeBook->getNodeById($parentNodeId);
        if (null === $parentNode) {
            throw new ApiException(sprintf('Parent node "%s" was not found.', $parentNodeId));
        }

        $neighborNode = null;
        if (!empty($neighborId)) {
            foreach ($parentNode->getChildren() as $child) {
                if ($neighborId === $child->getNodeId()) {
                    $neighborNode = $child;
                    break;
                }
            }
        }

        if ($action === 'after' && $neighborNode !== null) {
            $parentNode->insertAfter($node, $neighborNode);
        } else {
            $parentNode->insertBefore($node, $neighborNode);
        }
    }

    /** @throws ApiException */
    protected function movePromulgatorTo(Publication $codeBook, Promulgator $promulgator, string $parentNodeId): void
    {
        // validate parent
        $parentNode = $codeBook->getNodeById($parentNodeId);
        if (null === $parentNode) {
            throw new ApiException(sprintf('Parent node "%s" was not found.', $parentNodeId));
        }

        if (!$parentNode instanceof Chapter) {
            throw new ApiException(sprintf('Parent node "%s" is not a valid parent.', $parentNodeId));
        }

        $parentChildren = $parentNode->getChildren();
        if (count(array_filter($parentChildren, fn($child) => !$child instanceof Promulgator)) > 0) {
            throw new ApiException(sprintf('Parent node "%s" is not a valid promulgator chapter.', $parentNodeId));
        }

        $parentNode->appendChild($promulgator);

        // sort children by acronym
        $sorted = [];
        foreach ($parentNode->getChildren() as $child) {
            $sorted[$child->getPathId()] = $child;
        }

        ksort($sorted);
        $parentNode->setChildren($sorted);
    }
}
