<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\ErrorResponseDto;
use App\Dto\ResponseDtoInterface;
use App\Dto\Xml2\Book\Book;
use App\Dto\Xml2\Book\ListContentsResponse;
use App\Dto\Xml2\Book\NodeParentsResponse;
use App\Dto\Xml2\Book\NodeReferencingResponse;
use App\Dto\Xml2\Book\TableOfContents;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\Volume;
use App\Entity\Project;
use App\Enum\ObjectType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;

use function sprintf;

class ContentsService
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly SerializerInterface    $serializer,
        private readonly NormalizerInterface    $normalizer,
        private readonly string                 $apiUrl
    ) {
    }

    public function getContents(Project $project): ListContentsResponse
    {
        $response = new ListContentsResponse();
        $response->book = new Book();
        $response->book->id = $project->getShortCode();
        $response->book->title = $project->getBookTitle();
        $response->book->tableOfContents = new TableOfContents();

        /** @var Volume $volume */
        if ($project->getCodeBook() && $volume = $project->getCodeBook()->getVolume()) {
            foreach ($project->getCodeBook()->getChildren() as $volume) {
                if ($volume instanceof Volume) {
                    $response->book->volumes[] = $this->getTableOfContents($volume, $response->book->id);
                }
            }
        }

        return $response;
    }

    private function getTableOfContents(Volume $volume, string $bookId): TableOfContents
    {
        $frontMatter = [];
        $chapters = [];
        $backMatter = [];
        $frontMatterId = '';
        $backMatterId = '';

        foreach ($volume->getChildren() as $child) {
            if ($child instanceof Chapter || ($child instanceof Section && $child->isPart())) {
                $child->setChildren([]);
                $chapters[] = $child;
            }
        }
        if ($volume->getFrontMatter()) {
            $frontMatterId = $volume->getFrontMatter()->getNodeId();
            foreach ($volume->getFrontMatter()->getChildren() as $child) {
                $child->setChildren([]);
                $frontMatter[] = $child;
            }
        }
        if ($volume->getBackMatter()) {
            $backMatterId = $volume->getBackMatter()->getNodeId();
            foreach ($volume->getBackMatter()->getChildren() as $child) {
                $child->setChildren([]);
                $backMatter[] = $child;
            }
        }

        $toc = new TableOfContents();
        $toc->volumeTitle = $volume->getTitle();
        $toc->volumeNodeId = $volume->getNodeId();
        $toc->frontMatterId =  $frontMatterId;
        $toc->backMatterId =  $backMatterId;
        $toc->frontMatter = json_decode($this->serializer->serialize($frontMatter, 'json'), true);
        $toc->chapters = json_decode($this->serializer->serialize($chapters, 'json'), true);
        $toc->backMatter = json_decode($this->serializer->serialize($backMatter, 'json'), true);
        array_walk($toc->frontMatter, [$this, 'addLinks'], $bookId);
        array_walk($toc->chapters, [$this, 'addLinks'], $bookId);
        array_walk($toc->backMatter, [$this, 'addLinks'], $bookId);

        return $toc;
    }

    public function getNodeParents(string $nodeId): ResponseDtoInterface
    {
        $repo = $this->em->getRepository(AbstractCodeBookNode::class);

        $node = $repo->findOneBy([
            'ulid'        => $nodeId,
            'deletedDate' => null,
        ]);

        if ($node === null) {
            return new ErrorResponseDto(
                sprintf('The node %s does not exists.', $nodeId),
                Response::HTTP_NOT_FOUND
            );
        }

        $parentSection = null;
        $childSection = null;
        $chapter = null;
        $book = null;

        $currentNode = $node;

        while ($currentNode) {
            switch (true) {
                case $currentNode instanceof Section ||
                    $currentNode instanceof Promulgator ||
                    $currentNode instanceof Definition ||
                    $currentNode instanceof Table ||
                    $currentNode instanceof Figure:
                    $this->handleSection($currentNode, $parentSection, $childSection);
                    break;

                case $chapter === null && ($currentNode instanceof Chapter || $currentNode instanceof Appendix):
                    $chapter = $this->formatNodeData($currentNode);
                    break;

                case $book === null && $currentNode instanceof Publication:
                    $book = $this->formatNodeData($currentNode);
                    break;
            }

            $currentNode = $currentNode->getParent();
        }

        $response = new NodeParentsResponse();
        $response->childSection = $childSection;
        $response->book = $book;
        $response->chapter = $chapter;
        $response->parentSection = $parentSection;
        $response->type = $node->getDataType();

        return $response;
    }

    public function getNodeReferencing(Project $project, string $uuid): ResponseDtoInterface
    {
        $response = new NodeReferencingResponse();
        $node = $this->em
            ->getRepository(AbstractCodeBookNode::class)
            ->findOneBy([
                'ulid'      => $uuid,
                'deletedDate' => null,
            ]);

        if ($node === null) {
            $response->ok = false;
            $response->message = sprintf('The Node %s has been moved/deleted or does not exist..', $uuid);
            return $response;
        }

        $response->node = $this->normalizer->normalize($node);
        return $response;
    }

    private function addLinks(array &$node, int $key, string $bookId): void
    {
        $node['@links'] = [];
        $node['@links']['reports'] = [
            [
                'title' => 'PDF of Chapter (No Notes)',
                'type'  => 'pdf',
                'url'   => 'code-changes.pdf?notes=none',
            ],
            [
                'title' => 'PDF of Chapter (Codes and Pubs)',
                'type'  => 'pdf',
                'url'   => 'code-changes.pdf?notes=pubs',
            ],
            [
                'title' => 'PDF of Chapter (Pubs to Pubs)',
                'type'  => 'pdf',
                'url'   => 'code-changes.pdf?notes=typesetter',
            ],
            [
                'title' => 'PDF of Chapter (All Notes)',
                'type'  => 'pdf',
                'url'   => 'code-changes.pdf?notes=all',
            ],
            [
                'title' => 'Chapter Notes Report (All Notes)',
                'type'  => 'pdf',
                'url'   => 'notes-report?notes=all',
            ],
            [
                'title' => 'Chapter Notes Report (Codes to Codes)',
                'type'  => 'pdf',
                'url'   => 'notes-report?notes=internal',
            ],
            [
                'title' => 'Chapter Notes Report (Codes and Pubs)',
                'type'  => 'pdf',
                'url'   => 'notes-report?notes=pubs',
            ],
            [
                'title' => 'Chapter Notes Report (Pubs to Pubs)',
                'type'  => 'pdf',
                'url'   => 'notes-report?notes=typesetter',
            ],
            [
                'title'  => 'Errata Report - PDF',
                'type'   => 'pdf',
                'prefix' => '',
                'url'    => 'chapter-errata-report.pdf',
            ],
            [
                'title' => 'History Report - PDF',
                'type'  => 'pdf',
                'url'   => 'history-report?type=pdf',
            ],
            [
                'title' => 'History Report - RTF',
                'type'  => 'rtf',
                'url'   => 'history-report?type=rtf',
            ],
            [
                'title' => 'Deletion Arrows Report',
                'type'  => 'csv',
                'url'   => 'deletion-arrows',
            ],
            [
                'title' => 'All Links Report',
                'type'  => 'csv',
                'url'   => 'links-report',
            ],
        ];

        if (in_array($node['dataType'], [ObjectType::TYPE_APPENDIX, ObjectType::TYPE_CHAPTER])) {

            $chapterType = $node['dataType'];

            $node['@links']['reports'][] = [
                'title' => 'Is/Was',
                'type'  => 'csv',
                'url'   => "is-was?chapterType={$chapterType}&trailing=false",
            ];
            $node['@links']['reports'][] = [
                'title' => 'Is/Was with Trail Data',
                'type'  => 'csv',
                'url'   => "is-was?chapterType={$chapterType}&trailing=true",
            ];
        }

        foreach ($node['@links']['reports'] as &$i) {
            $url = $this->getReportUrl($node['id'], $bookId);
            $i['url'] = sprintf('%s/%s', $url, $i['url']);
        }
    }

    private function getReportUrl(string $chapterId, ?string $bookId = null): string
    {
        $chapterPart = $chapterId ? sprintf('/chapters/%s', $chapterId) : '';
        $bookPart = $bookId ? sprintf('/%s', $bookId) : '';

        return sprintf('%s/api/v2/reports%s%s', $this->apiUrl, $bookPart, $chapterPart);
    }

    private function handleSection(
        AbstractCodeBookNode $currentNode,
        ?array &$parentSection,
        ?array &$childSection
    ): void {
        if ($childSection === null && $currentNode->getParent() instanceof Section || $currentNode->getParent() instanceof DefinitionList) {
            $childSection = $this->formatNodeData($currentNode);
        } elseif (
            $parentSection === null &&
            ($currentNode->getParent() instanceof Chapter || $currentNode->getParent() instanceof Appendix)
        ) {
            $parentSection = $this->formatNodeData($currentNode);
        }
    }

    private function formatNodeData(AbstractCodeBookNode $node): array
    {
        return [
            'nodeId' => $node->getNodeId(),
            'ulid'   => $node->getUlid(),
        ];
    }
}
