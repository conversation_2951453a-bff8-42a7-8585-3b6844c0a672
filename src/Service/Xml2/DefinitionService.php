<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Definition\CreateDefinitionRequest;
use App\Dto\Xml2\Definition\ListDefinitionsResponse;
use App\Dto\Xml2\Definition\MoveDefinitionRequest;
use App\Dto\Xml2\Definition\UpdateDefinitionRequest;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\DefinitionList;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\ApiException;
use App\Traits\DefinitionTermOrderingTrait;
use App\Traits\GetNodeChildrenStatusesTrait;
use App\Traits\QrCodeSetterTrait;

class DefinitionService extends AbstractNodeService
{
    use GetNodeChildrenStatusesTrait;
    use QrCodeSetterTrait;
    use DefinitionTermOrderingTrait;

    public function list(Project $project): ListDefinitionsResponse
    {
        /** @var Definition[] $definitions */
        $definitions = $this->repo->findByType($project->getCodeBook(), Definition::class);

        return new ListDefinitionsResponse($definitions);
    }

    /** @throws ApiException */
    public function create(Project $project, CreateDefinitionRequest $request): Definition
    {
        $definitionList = $this->repo->findNodeById($request->parentNode);
        if ($definitionList === null) {
            $neighborParent = $this->repo->findNodeById($request->neighbor);
            if ($neighborParent === null) {
                throw new ApiException(sprintf('Neighbor Parent "%s" not found.', $request->neighbor));
            }
            $definitionList = new DefinitionList();
            $definitionList->setParent($neighborParent);
            $neighborParent->addChild($definitionList);
            $this->em->persist($definitionList);
        }

        $definition = new Definition();
        $this->setFields($definition, $request);

        $this->moveToParent($project->getCodeBook(), $definition, $definitionList->getNodeId(), $request->insertBefore);

        $this->em->persist($definition);
        $this->em->flush();

        return $definition;
    }

    public function update(Definition $definition, UpdateDefinitionRequest $request): Definition
    {
        $this->setFields($definition, $request);
        $parent = $definition->getChapterParent();
        $statuses = $this->getChildrenStatuses($parent);
        $this->updateParentStatusIfNeeded($statuses, $parent);
        $this->em->flush();

        return $definition;
    }

    public function delete(Definition $definition): void
    {
        $currentUser = $this->security->getUser();
        $user = (string) $this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);

        $definition->setRevisionBy($user);
        $definition->deleteNode($user);
        $this->em->flush();
    }

    /** @throws ApiException */
    public function move(Project $project, Definition $definition, MoveDefinitionRequest $request): Definition
    {
        $definition->setTerm($request->term);
        $definition->setRelocatedFromAttr($definition->getNodeId());

        $definitionList = $this->repo->findNodeById($request->parentNode);
        if ($definitionList === null) {
            $neighborParent = $this->repo->findNodeById($request->neighbor);
            if ($neighborParent === null) {
                throw new ApiException(sprintf('Neighbor Parent "%s" not found.', $request->neighbor));
            }
            $definitionList = new DefinitionList();
            $definitionList->setParent($neighborParent);
            $neighborParent->addChild($definitionList);
            $this->em->persist($definitionList);
        }

        $this->moveToParent($project->getCodeBook(), $definition, $definitionList->getNodeId(), $request->insertBefore);
        $this->em->flush();

        return $definition;
    }

    private function setFields(Definition $definition, UpdateDefinitionRequest $request): void
    {
        $definition->setStatus($request->status);

        $definition->setCommitteeDesignation($request->committeeDesignation);
        $definition->setTerm($request->term);
        $definition->setDefinition($this->processDefinitionTerm($request->definition));

        // @deprecated
//        $definition->setCaption($request->caption);
//        $definition->setLegend($request->legend);
//        $definition->setSource($request->source);
//        $definition->setCredit($request->credit);
//        $definition->setCreditTitle($request->creditTitle);

        // Set QrCode fields
        $this->setQrCodeFields($definition, $request);
    }
}
