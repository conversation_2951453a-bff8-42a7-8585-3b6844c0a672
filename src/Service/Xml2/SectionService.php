<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\DeleteNodeRequest;
use App\Dto\Xml2\Section\CreateSectionRequest;
use App\Dto\Xml2\Section\DeleteSectionResponse;
use App\Dto\Xml2\Section\UpdateSectionRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\Interfaces\RelocatableNodeInterface;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\ApiException;
use App\Message\CodeBook\UpdateNodesByXRefAsyncMessage;
use App\Repository\CodeBookNodeRepository;
use App\Traits\GetNodeChildrenStatusesTrait;
use App\Traits\InsertMarkingTrait;
use App\Traits\NumberCleanerTrait;
use App\Traits\QrCodeSetterTrait;
use App\Traits\RelocationTrait;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Messenger\MessageBusInterface;

class SectionService extends AbstractNodeService
{
    use QrCodeSetterTrait;
    use GetNodeChildrenStatusesTrait;
    use NumberCleanerTrait;
    use RelocationTrait;
    use InsertMarkingTrait;

    public function __construct(
        EntityManagerInterface               $em,
        CodeBookNodeRepository               $repo,
        Security                             $security,
        private readonly MessageBusInterface $messageBus,
        private readonly EquationService     $equationService,
    ) {
        parent::__construct($em, $repo, $security);
    }

    public function list(Project $project): iterable
    {
        return $this->repo->findByType($project->getCodeBook(), Section::class);
    }

    /** @throws ApiException */
    public function create(Project $project, CreateSectionRequest $request): Section
    {
        $section = new Section();
        $this->setFields($section, $request);
        $this->moveToParentByAction($project->getCodeBook(), $section, $request->parentNode, $request->neighbor, $request->action);

        $this->em->persist($section);

        $updatedBody = $this->equationService->processEquationsInSection($section);
        if ($updatedBody) {
            $section->setBody($updatedBody);
        }
        $this->em->flush();

        return $section;
    }

    public function update(Project $project, Section $section, UpdateSectionRequest $request): Section
    {
        $this->setFields($section, $request);

        if (!$section->isPart()) {
            $parent = $section->getChapterParent();
            $statuses = $this->getChildrenStatuses($parent);
            $this->updateParentStatusIfNeeded($statuses, $parent);
        }

        if ($request->parentNode) {
            $section->setRelocatedFromAttr($section->getNodeId());
            $this->moveToParentByAction($project->getCodeBook(), $section, $request->parentNode, $request->neighbor, $request->action);
            $parentNode = $this->repo->findNodeById($request->parentNode);

            $this->relocateAndUpdateChildren($section, $parentNode, $request->newNumber);
        }

        $updatedBody = $this->equationService->processEquationsInSection($section);
        if ($updatedBody) {
            $section->setBody($updatedBody);
        }

        $this->em->flush();

        return $section;
    }

    public function delete(Section $section, DeleteNodeRequest $request): DeleteSectionResponse
    {
        $currentUser = $this->security->getUser();
        $user = (string) $this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);

        $section->setRevisionBy($user);
        $section->setRevisionDateTime(new DateTime());
        $section->deleteNode($user, $request->showDeletionMarker);
        $this->em->flush();

        return new DeleteSectionResponse($section);
    }

    private function createACopyFrom(Section $section): Section
    {
        $wrapper = new Section();

        $wrapper->setParent($section->getParent());
        $wrapper->setNumber($this->wrapInsert($section->getNumber()));

        $wrapper->setTitle($this->wrapInsert('Title'));
        $wrapper->setBody($this->wrapInsert('Body'));

        $wrapper->setStatus($section->getStatus());
        $wrapper->setLabel($this->wrapInsert($section->getLabel()));
        $wrapper->setCommitteeDesignation($this->wrapInsert($section->getCommitteeDesignation()));

        return $wrapper;
    }

    /** @throws ApiException */
    public function wrap(Project $project, Section $section, string $wrapId, UpdateSectionRequest $request): Section
    {
        $parent = $section->getParent();

        if (!$parent) {
            throw new ApiException(
                sprintf('The parent section not found.')
            );
        }

        $this->em->wrapInTransaction(function () use ($project, $section, $parent, $request) {

            $wrapper = $this->createACopyFrom($section);
            $parent->replaceChild($wrapper, $section);
            $this->em->persist($wrapper);

            $currentChildren = $section->getChildren(false);
            foreach ($currentChildren as $child) {
                if ($child instanceof Table || $child instanceof Figure) {
                    continue;
                }
                $wrapper->appendChild($child);
            }


            $section->setRelocatedFromAttr($section->getNodeId());
            $section->setNumber($request->newNumber);
            $section->setOriginalNumber($section->getNumber());
            $this->moveToParentByAction($project->getCodeBook(), $section, $wrapper->getNodeId(), $request->neighbor, $request->action);

            $this->em->persist($section);
            $this->relocateAndUpdateChildren($section, $wrapper, $request->number);
        });


        $this->em->flush();
        return $section;
    }

    private function setFields(Section $section, UpdateSectionRequest $request): void
    {
        $section->setStatus($request->status);
        $section->setSuperTitle($request->superTitle);
        $section->setCommitteeDesignation($request->committeeDesignation);
        $section->setLabel($request->label);
        $section->setNumber($request->number);
        $section->setCorrelated($request->correlated);
        $section->setTitle($request->title);
        $section->setSubTitle($request->subTitle);
        $section->setBody($request->body);
        $section->setAbstractTitle($request->abstractTitle);
        $section->setAbstract($request->abstract);
        $section->setKeywordsTitle($request->keywordsTitle);
        $section->setKeywords($request->keywords);
        $section->setObjectivesTitle($request->objectivesTitle);
        $section->setObjectives($request->objectives);
        $section->setHistory($request->history);

        // Set QrCode fields
        $this->setQrCodeFields($section, $request);
    }

    public function relocateAndUpdateChildren(
        AbstractCodeBookNode $section, AbstractCodeBookNode $parentNode, string $parentNewNumber
    ): void {
        $this->messageBus->dispatch(new UpdateNodesByXRefAsyncMessage($section));

        foreach ($section->getChildren(false) as $child) {
            if ($child instanceof RelocatableNodeInterface) {
                $oldChildNumber = $this->getCleanOrdinal($child->getNumber());
                $newChildNumber = $this->computeNewChildNumber($parentNewNumber, $oldChildNumber);

                $this->setTransformedNumber($child, $oldChildNumber, $newChildNumber);
                $this->relocateAndUpdateChildren($child, $section, $newChildNumber);
            }
        }
    }
}
