<?php

namespace App\Service\Xml2;

use App\Entity\Project;
use App\Util\QueryPath;
use App\Util\TextUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Routing\RouterInterface;

/**
 * @deprecated
 * TODO: Refactor this service
 */
class SearchService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly RouterInterface        $router
    ) {
    }

    public function getDefinitionUsages(
        Project $project, string $query, array $similarMatches, string $page = '1', string $limit = '10'
    ): array {
        $page = (int) $page ?? 1;
        $start = (int) ($limit * ($page - 1));

        $conn = $this->entityManager->getConnection();
        $sqlParams = ['codeBookId' => $project->getCodeBook()->getId(),
                      'query'      => '%' . $query . '%'];

        $sqlJoinBodyCondition = "(nt.id = cbx.id AND (cbx.body LIKE :query)";
        $sqlJoinTableCondition = "(nt.id = cbx.id AND (cbx.table LIKE :query)";
        if (!empty($similarMatches)) {
            $sqlJoinBodyCondition .= " OR (cbx.body IN (:similarMatches))";
            $sqlJoinTableCondition .= " OR (cbx.table IN (:similarMatches))";
            $sqlParams['similarMatches'] = implode(",", $similarMatches);
        }
        $sqlJoinBodyCondition .= ")";
        $sqlJoinTableCondition .= ")";

        $sql = "WITH RECURSIVE node_tree AS
            (
                SELECT *
                FROM code_book_nodes
                WHERE id = :codeBookId
                UNION ALL
                SELECT cbn.*
                FROM code_book_nodes cbn
                INNER JOIN node_tree nt ON cbn.parent_id = nt.id
            )
            SELECT nt.id, nt.node_id, nt.path_level, nt.path, nt.ulid, nt.dataType, cbx.title, cbx.body, cbx.number, REGEXP_REPLACE(cbx.number, '[^0-9.]', '') as ordinal
            FROM node_tree nt
            INNER JOIN code_book_section cbx ON $sqlJoinBodyCondition
            UNION
            SELECT nt.id, nt.node_id, nt.path_level, nt.path, nt.ulid, nt.dataType, cbx.title, cbx.body, cbx.number, REGEXP_REPLACE(cbx.number, '[^0-9.]', '') as ordinal
            FROM node_tree nt
            INNER JOIN code_book_chapter cbx ON $sqlJoinBodyCondition
            UNION
            SELECT nt.id, nt.node_id, nt.path_level, nt.path, nt.ulid, nt.dataType, cbx.title, cbx.table, cbx.number, REGEXP_REPLACE(cbx.number, '[^0-9.]', '') as ordinal
            FROM node_tree nt
            INNER JOIN code_book_table cbx ON $sqlJoinTableCondition
            ORDER BY ordinal";

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery($sqlParams);
        $nodes = $result->fetchAllAssociative();

        $total = count($nodes);
        $lastPage = ceil($total / $limit);
        $nextPage = $lastPage >= ($page + 1) ? $page + 1 : null;
        $prevPage = $page > 1 ? $page - 1 : null;

        $thisUrlParams = ['bookId' => $project->getCodeBook()->getNodeId(), 'query' => $query, 'page' => $page, 'limit' => $limit];

        $jsonResults = [];

        $paginatedNodes = array_slice($nodes, $start, $limit);

        foreach ($paginatedNodes as $result) {
            $jsonResult = [];
            $jsonResult['book']['id'] = $project->getCodeBook()->getNodeId();
            $jsonResult['book']['title'] = $project->getBookTitle();
            switch ($result['dataType']) {
                case 'chapter':
                    $chapter = $this->entityManager
                        ->getRepository(\App\Entity\CodeBook\Chapter::class)
                        ->findOneBy([
                            'id' => $result['id'],
                        ]);
                    $jsonResult['chapter']['ordinal'] = $chapter->getNumber();
                    $jsonResult['chapter']['title'] = $chapter->getTitle();
                    $jsonResult['chapter']['id'] = $chapter->getNodeId();
                    $jsonResult['chapter']['label'] = 'CHAPTER';
                    $jsonResult['content'] = $chapter->getTitle() . $chapter->getBody();
                    break;
                case 'section':
                    $section = $this->entityManager
                        ->getRepository(\App\Entity\CodeBook\Section::class)
                        ->findOneBy([
                            'id' => $result['id'],
                        ]);
                    $jsonResult['section']['ordinal'] = $section->getNumber();
                    $jsonResult['section']['title'] = $section->getTitle();
                    $jsonResult['section']['id'] = $section->getNodeId();
                    $jsonResult['section']['label'] = 'SECTION';
                    $jsonResult['content'] = $section->getTitle() . $section->getBody();
                    break;
                case 'table':
                    $table = $this->entityManager
                        ->getRepository(\App\Entity\CodeBook\Table::class)
                        ->findOneBy([
                            'id' => $result['id'],
                        ]);
                    $jsonResult['table']['ordinal'] = $table->getNumber();
                    $jsonResult['table']['title'] = $table->getTitle();
                    $jsonResult['table']['id'] = $table->getNodeId();
                    $jsonResult['table']['label'] = 'TABLE';
                    $jsonResult['content'] = $table->getTitle() . $table->getTable();
                    break;
                default:
                    // unhandled type
            }
            $jsonResult['additionalContent'] = null;
            $jsonResult['content'] = $this->highlightDefinitionUsageTerms($jsonResult['content'], $result['node_id'], $query, $similarMatches) ?? $jsonResult['content'];
            $jsonResults['member'][] = $jsonResult;
        }

        return array_merge([
            'id'           => $this->generateDefinitionUsageUrl($thisUrlParams),
            'firstPage'    => $this->generateDefinitionUsageUrl($thisUrlParams, ['page' => 1]),
            'previousPage' => $prevPage !== null ? $this->generateDefinitionUsageUrl($thisUrlParams, ['page' => $prevPage]) : null,
            'nextPage'     => $nextPage !== null ? $this->generateDefinitionUsageUrl($thisUrlParams, ['page' => $nextPage]) : null,
            'lastPage'     => $this->generateDefinitionUsageUrl($thisUrlParams, ['page' => $lastPage]),
            'pageCount'    => $lastPage,
            'total'        => $total,
            'limit'        => $limit,
        ], $jsonResults);
    }

    private function highlightDefinitionUsageTerms(
        string $content, string $sectionId, string $query, array $similarMatches
    ) {

        $query = preg_replace('/[^A-Za-z0-9.’ -\/]/', '', $query);
        $termPattern = str_replace('/', '\/', $query);
        $formalTermPattern = preg_quote('<span class="formal_usage"><mark data-instance=', '"') . '[0-9]+' . preg_quote('>') . $termPattern . preg_quote('</mark></span>', '/');

        $content = strpos($sectionId, '_Tbl') ? $content : $this->parseContentString($content);
        $matchFound = false;
        $instances = 0;
        $termSpan = null;
        $termPos = strpos($content, '<span class="term">');
        $formalUsages = [];

        // Sanitize string of special characters
        foreach ($similarMatches as $key => $match) {
            $similarMatches[$key] = preg_replace('/[^A-Za-z0-9. -]/', '', $match);
        }

        // Remove term span from content
        if ($termPos !== false) {
            preg_match('/<span class="term">.*?<\/span>/', $content, $termSpan);
            $content = str_replace($termSpan[0], '', $content);
        }

        // Hash non-matching formal uses
        $output = preg_replace_callback('/<span class="formal_usage">.*?<\/span>/', function ($m
        ) use ($query, $similarMatches, &$formalUsages) {
            $match = false;
            $strippedTerm = TextUtils::trimHtml(strip_tags($m[0]));

            if (strpos(strtolower($query), strtolower($strippedTerm)) !== false) {
                $match = true;
            } else {
                foreach ($similarMatches as $similarMatch) {
                    if (strnatcasecmp($strippedTerm, $similarMatch) == 0) {
                        $match = true;
                    }
                }
            }

            if (!$match) {
                $hashText = '<span class="nonmatched_formal_usage">HASHBROWN-INDEX' . count($formalUsages) . '</span>';
                $formalUsages[$hashText] = $m[0];

                return $hashText;
            } else {
                return $m[0];
            }
        }, $content);

        // Mark all instances of term
        $output = preg_replace_callback(sprintf('/\b(%s)([\b\W])/i', $termPattern), function ($m) use (
            $query, &$instances, &$matchFound
        ) {
            if (strnatcasecmp($m[1], $query) == 0) {
                $instances++;
                $matchFound = true;

                return '<mark data-instance=' . $instances . '>' . $m[1] . '</mark>' . $m[2];
            } else {
                return $m[0];
            }
        }, $output);

        // Fix marking on formal usages
        $output = preg_replace_callback(sprintf('/(%s)([\b\W])/i', $formalTermPattern), function ($m) {
            preg_match('/[0-9]+/', $m[1], $instanceMatches);

            $match = preg_replace('/<mark data-instance=[0-9]+>/', '<mark data-instance="[0-9]">', $m[1]);
            $match = str_replace('<mark data-instance="[0-9]">', '', $match);
            $match = str_replace('</mark>', '', $match);

            return '<mark data-formal-usage="true" data-instance=' . $instanceMatches[0] . '>' . $match . '</mark>' . $m[2];
        }, $output);


        // Re-add term span from content
        if ($termSpan !== null) {
            $output = substr_replace($output, $termSpan[0], $termPos, 0);
        }

        // Un-hash formal usages
        foreach ($formalUsages as $key => $value) {
            $formalPos = strpos($output, $key);
            $output = str_replace($key, '', $output);
            $output = substr_replace($output, $value, $formalPos, 0);
        }

        foreach ($similarMatches as $similar) {
            $instances = 0;

            $similarTermPattern = preg_quote($similar);
            $similarFormalTermPattern = preg_quote('<span class="formal_usage"><mark data-instance=', '"') . '[0-9]+' . preg_quote('>') . preg_quote($similar) . preg_quote('</mark></span>', '/');

            // If the Similar Match exists within the Definition Term ensure that matches aren't already marked Definition Terms
            $output = preg_replace_callback(sprintf('/\b(%s)([\b\W])/i', $similarTermPattern), function ($m) use (
                $similar, &$instances, &$matchFound
            ) {

                if (strnatcasecmp($m[1], $similar) == 0) {
                    $instances++;
                    $matchFound = true;

                    return '<mark data-instance=' . $instances . '>' . $m[1] . '</mark>' . $m[2];
                } else {
                    return $m[0];
                }
            }, $output);

            // Fix marking on formal usages
            $output = preg_replace_callback(sprintf('/(%s)([\b\W])/i', $similarFormalTermPattern), function ($m) use (
                $similar
            ) {
                preg_match('/[0-9]+/', $m[1], $instanceMatches);

                $match = preg_replace('/<mark data-instance=[0-9]+>/', '<mark data-instance="[0-9]">', $m[1]);
                $match = str_replace('<mark data-instance="[0-9]">', '', $match);
                $match = str_replace('</mark>', '', $match);

                return '<mark data-formal-usage="true" data-instance=' . $instanceMatches[0] . '>' . $match . '</mark>' . $m[2];
            }, $output);
        }

        return ($matchFound) ? $output : null;
    }

    public function parseContentString($content)
    {
        $content = preg_replace('/<(del|ins)(\s+[^>]*)?>/iU', '<$1>', $content);
        $content = '<section>' . $content . '</section>';

        $qp = QueryPath::getQueryPath($content);
        $qp->find('span')->each(function ($index, $item) {
            $section = QueryPath::getQueryPath($item);
            $class = $section->attr('class');
            $style = $section->attr('style');

            if (
                $style ||
                ($class && in_array($class, ['bold', 'italic']))
            ) {
                $section->contents()->unwrap();
            }
        });

        $content = $qp->xml();
        $content = str_replace('<?xml version="1.0" encoding="UTF-8"?>', '', $content);
        $html = $this->sanitizer->cleanHtmlContent($content, true, 'ins');

        return TextUtils::trimHtml($html);
    }

    /**
     * @param array $defaultParams
     * @param array $params
     *
     * @return string
     */
    private function generateDefinitionUsageUrl(array $defaultParams, array $params = []): string
    {
        return $this->router->generate('app_api_xml2_definition_getDefinitionUsage', array_merge($defaultParams, $params), RouterInterface::ABSOLUTE_URL);
    }
}
