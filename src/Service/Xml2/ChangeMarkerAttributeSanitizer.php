<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Helper\Xml2Helper;
use DOMDocument;
use DOMElement;

use function trim;

class ChangeMarkerAttributeSanitizer
{
    private const REVISION_DEFAULTS = [
        'insert' => 'added',
        'delete' => 'deleted',
    ];

    /**
     * Ensures change marker attributes exist on all <insert>/<delete> nodes.
     *
     * @return array<int, array{node: DOMElement, tag: string, attributes_added: array<int, string>}>
     */
    public function sanitize(DOMDocument $document, $project): array
    {
        $projectLevel = $this->getProjectLevelValue($project);
        $dataChangedDefaults = [
            'insert' => 'changed_' . $projectLevel,
            'delete' => 'deleted_current',
        ];

        $xpath = Xml2Helper::createDOMXPath($document);
        $nodes = $xpath->query('//x:insert | //x:delete');

        if (false === $nodes || 0 === $nodes->length) {
            return [];
        }

        $updates = [];

        foreach ($nodes as $node) {
            if (!$node instanceof DOMElement) {
                continue;
            }

            $missing = [];

            if (!$this->hasValue($node, 'data-changed')) {
                $default = $dataChangedDefaults[$node->localName] ?? null;
                if (null !== $default) {
                    $node->setAttribute('data-changed', $default);
                    $missing[] = 'data-changed';
                }
            } else {
                $currentValue = trim($node->getAttribute('data-changed'));
                if ('changed_current' === $currentValue) {
                    $node->setAttribute('data-changed', 'changed_' . $projectLevel);
                    $missing[] = 'data-changed (updated)';
                }
            }

            if (!$this->hasValue($node, 'revision')) {
                $default = self::REVISION_DEFAULTS[$node->localName] ?? null;
                if (null !== $default) {
                    $node->setAttribute('revision', $default);
                    $missing[] = 'revision';
                }
            }

            if ([] === $missing) {
                continue;
            }

            $updates[] = [
                'node' => $node,
                'tag' => $node->localName,
                'attributes_added' => $missing,
            ];
        }

        return $updates;
    }

    private function hasValue(DOMElement $node, string $attribute): bool
    {
        if (!$node->hasAttribute($attribute)) {
            return false;
        }

        return '' !== trim($node->getAttribute($attribute));
    }

    private function getProjectLevelValue($project): string 
    {
        if (null === $project) {
            return 'level0';
        }
        $projectType = method_exists($project, 'getProjectType') ? $project->getProjectType() : null;
        if (null === $projectType || !method_exists($projectType, 'getName')) {
            return 'level0';
        }
        $projectLevel = $projectType->getName();
        $pos = strpos($projectLevel, ' - ');
        $projectLevel = ($pos !== false) ? substr($projectLevel, 0, $pos) : $projectLevel;
        return strtolower(str_replace(' ', '', $projectLevel));
    }
}
