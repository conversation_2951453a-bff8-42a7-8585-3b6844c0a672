<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Validation\XmlValidationMessage;
use App\Dto\Xml2\Validation\XmlValidationResult;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\EventListener\CodeBook\UpdateFieldsListener;
use App\Helper\Xml2Helper;
use App\Enum\XmlValidationIssueType;
use App\Service\Xml2\Formatter\XmlErrorSnippetFormatter;
use App\Service\Xml2\Formatter\XmlTitleFormatter;
use App\Service\Xml2\Formatter\XmlValidationErrorClassifier;
use App\Service\Xml2\Proposals\Identifier;
use App\Traits\NumberCleanerTrait;
use Psr\Log\LoggerInterface;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Throwable;

use function array_map;
use function array_merge;
use function count;
use function implode;
use function is_string;
use function preg_replace;
use function sprintf;
use function str_contains;
use function trim;

class XmlValidationService
{
    use NumberCleanerTrait;

    private readonly PropertyAccessorInterface $accessor;

    public function __construct(
        private readonly LoggerInterface $projectXmlLogger,
        private readonly XmlErrorSnippetFormatter $snippetFormatter,
        private readonly XmlTitleFormatter $titleFormatter,
        private readonly XmlValidationErrorClassifier $errorClassifier
    ) {
        $this->accessor = PropertyAccess::createPropertyAccessor();
    }

    /**
     * @return XmlValidationResult[]
     */
    public function validateChapter(AbstractCodeBookNode $chapter): array
    {
        return $this->validateNodeTree($chapter);
    }

    /**
     * @return XmlValidationResult[]
     */
    public function validateProject(Project $project): array
    {
        $codeBook = $project->getCodeBook();
        if (!$codeBook instanceof AbstractCodeBookNode) {
            return [];
        }

        return $this->validateNodeTree($codeBook);
    }

    /**
     * @return XmlValidationResult[]
     */
    private function validateNodeTree(AbstractCodeBookNode $root): array
    {
        $issues = [];

        foreach ($root->getIterator() as $node) {
            if ($node->isDeleted()) {
                continue;
            }

            $issues = array_merge($issues, $this->validateNode($node));
        }

        return $issues;
    }

    /**
     * @return XmlValidationResult[]
     */
    private function validateNode(AbstractCodeBookNode $node): array
    {
        $issues = [];

        foreach (UpdateFieldsListener::FIELDS as $field) {
            try {
                $readable = $this->accessor->isReadable($node, $field);
            } catch (Throwable $exception) {
                $this->projectXmlLogger->error('Failed to inspect field during XML validation.', [
                    'node_id'   => $node->getNodeId(),
                    'node_type' => $node->getDataType(),
                    'field'     => $field,
                    'error'     => $exception->getMessage(),
                ]);
                continue;
            }

            if (!$readable) {
                continue;
            }

            try {
                $value = $this->accessor->getValue($node, $field);
            } catch (Throwable $exception) {
                $this->projectXmlLogger->error('Failed to read field during XML validation.', [
                    'node_id'   => $node->getNodeId(),
                    'node_type' => $node->getDataType(),
                    'field'     => $field,
                    'error'     => $exception->getMessage(),
                ]);
                continue;
            }

            if (!is_string($value)) {
                continue;
            }

            $value = trim($value);
            if ($value === '') {
                continue;
            }

            $rawErrors = Xml2Helper::collectXmlErrors($value, true);
            if ($rawErrors === []) {
                continue;
            }

            $messages = array_map(
                fn(array $error) => new XmlValidationMessage(
                    $this->formatErrorMessage($error['message'], $error['line'], $error['column']),
                    $error['line'],
                    $error['column']
                ),
                $rawErrors
            );

            $type = null;
            $firstRawMessage = $rawErrors[0]['message'] ?? null;
            if (is_string($firstRawMessage) && $firstRawMessage !== '') {
                $type = $this->errorClassifier->classify($firstRawMessage);
            }

            $issues[] = $this->createIssue($node, $field, $value, $messages, $type);

            $this->projectXmlLogger->error('Invalid XML detected during validation.', [
                'node_id'     => $node->getNodeId(),
                'node_ulid'   => $node->getUlid(),
                'node_type'   => $node->getDataType(),
                'field'       => $field,
                'message'     => $messages[0]->message ?? 'Malformed XML detected.',
                'error_count' => count($messages),
            ]);
        }

        return $issues;
    }

    private function createIssue(
        AbstractCodeBookNode $node,
        string $field,
        string $value,
        array $messages,
        ?XmlValidationIssueType $type
    ): XmlValidationResult {
        $primary = $messages[0] ?? null;
        $summary = $primary?->message ?? 'Malformed XML detected.';

        return new XmlValidationResult(
            $node->getNodeId(),
            $node->getUlid(),
            $node->getPathId(),
            $node->getDataType(),
            $field,
            $summary,
            $this->snippetFormatter->format($value, $primary),
            $type,
            $this->resolveTitle($node),
            $this->resolveNumber($node),
            $messages,
            $primary?->line,
            $primary?->column
        );
    }

    private function formatErrorMessage(string $message, ?int $line, ?int $column): string
    {
        $message = trim($message);
        $normalized = preg_replace('/\s+/', ' ', $message);
        if (is_string($normalized)) {
            $message = $normalized;
        }

        $parts = [];
        if ($line !== null) {
            $parts[] = sprintf('line %d', $line);
        }

        if ($column !== null) {
            $parts[] = sprintf('column %d', $column);
        }

        if ($parts === []) {
            return $message;
        }

        $suffix = sprintf(' (%s)', implode(', ', $parts));

        if (str_contains($message, 'line') || str_contains($message, 'column')) {
            return $message;
        }

        return $message . $suffix;
    }

    private function resolveTitle(AbstractCodeBookNode $node): ?string
    {
        $title = $this->readStringProperty($node, 'title');

        return $this->titleFormatter->format($title);
    }

    private function resolveNumber(AbstractCodeBookNode $node): ?string
    {
        return  $this->getCleanOrdinal(Identifier::ofCodeBook($node));
    }

    private function readStringProperty(AbstractCodeBookNode $node, string $property): ?string
    {
        if (!$this->accessor->isReadable($node, $property)) {
            return null;
        }

        $value = $this->accessor->getValue($node, $property);

        return is_string($value) ? trim($value) ?: null : null;
    }
}
