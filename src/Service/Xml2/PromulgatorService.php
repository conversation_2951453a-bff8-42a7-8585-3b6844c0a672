<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Promulgator\AddStandardToPromulgatorResponse;
use App\Dto\Xml2\Promulgator\CreatePromulgatorRequest;
use App\Dto\Xml2\Promulgator\DeletePromulgatorResponse;
use App\Dto\Xml2\Promulgator\MovePromulgatorRequest;
use App\Dto\Xml2\Promulgator\MovePromulgatorResponse;
use App\Dto\Xml2\Promulgator\UpdatePromulgatorRequest;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\ApiException;
use App\Helper\Xml2Helper;
use App\Repository\CodeBookNodeRepository;
use App\Service\CodeBook\CodeBookPathIdService;
use App\Service\CodeBook\ReferenceStandardYearParser;
use App\Traits\GetNodeChildrenStatusesTrait;
use App\Traits\QrCodeSetterTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Security;

use function preg_replace;
use function sprintf;
use function strip_tags;
use function trim;

class PromulgatorService extends AbstractNodeService
{
    use GetNodeChildrenStatusesTrait;
    use QrCodeSetterTrait;

    public function __construct(
        EntityManagerInterface                 $em,
        CodeBookNodeRepository                 $repo,
        Security                               $security,
        private readonly CodeBookPathIdService $nodePathService,
    ) {
        parent::__construct($em, $repo, $security);
    }

    public function list(Project $project): iterable
    {
        return $this->repo->findByType($project->getCodeBook(), Promulgator::class);
    }

    /** @throws ApiException */
    public function create(Project $project, CreatePromulgatorRequest $request): Promulgator
    {

        $promulgator = new Promulgator();
        $this->setFields($promulgator, $request);
        $this->movePromulgatorTo($project->getCodeBook(), $promulgator, $request->parentNode);

        $this->em->persist($promulgator);
        $this->em->flush();

        return $promulgator;
    }

    public function update(Promulgator $promulgator, UpdatePromulgatorRequest $request): Promulgator
    {
        $this->setFields($promulgator, $request);

        $parent = $promulgator->getChapterParent();

        $statuses = $this->getChildrenStatuses($parent);
        $this->updateParentStatusIfNeeded($statuses, $parent);

        $this->em->flush();

        return $promulgator;
    }

    public function delete(Promulgator $promulgator): DeletePromulgatorResponse
    {
        $currentUser = $this->security->getUser();
        $user = (string) $this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);

        $promulgator->deleteNode($user);
        $this->em->flush();

        return new DeletePromulgatorResponse($promulgator);
    }

    /** @throws ApiException */
    public function addStandard(Promulgator $promulgator, string $standardId): AddStandardToPromulgatorResponse
    {
        $referencedStandard = $this->em
            ->getRepository(ReferenceStandard::class)
            ->findOneBy([
                'nodeId'      => $standardId,
                'deletedDate' => null,
            ]);

        if ($referencedStandard === null) {
            throw new ApiException(sprintf('ReferenceStandard node "%s" was not found.', $standardId));
        }

        if ($promulgator->hasReferencedStandard($referencedStandard)) {
            throw new ApiException(sprintf('This standard: "%s" is already referenced in this promulgator.', $standardId));
        }

        $promulgator->addChild($referencedStandard);
        $this->em->flush();

        return new AddStandardToPromulgatorResponse($promulgator, $standardId);
    }

    /** @throws ApiException */
    public function move(
        Project                $project,
        Promulgator            $promulgator,
        MovePromulgatorRequest $request
    ): MovePromulgatorResponse {
        $promulgator->setRelocatedFromAttr($promulgator->getNodeId());
        $this->movePromulgatorTo($project->getCodeBook(), $promulgator, $request->parentNode);

        if ($request->validateOnly) {
            $response = new MovePromulgatorResponse($promulgator);
            $response->message = 'Promulgator move is valid';
            $response->status = Response::HTTP_OK;

            return $response;
        }

        $this->em->flush();

        return new MovePromulgatorResponse($promulgator);
    }

    private function setFields(Promulgator $promulgator, UpdatePromulgatorRequest $request): void
    {
        $currentUser = $this->security->getUser();
        $user = (string) $this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);
        $promulgator->setStatus($request->status);

        $promulgator->setAcronym($request->acronym);
        $promulgator->setAddressLine($request->addressLine);
        $promulgator->setOrganizationName($request->organizationName);
        $promulgator->setStreet($request->street);
        $promulgator->setCity($request->city);
        $promulgator->setState($request->state);
        $promulgator->setPostalCode($request->postalCode);
        $promulgator->setCountry($request->country);
        $promulgator->setEmail($request->email);
        $promulgator->setUrl($request->url);
        $promulgator->setPhone($request->phone);
        $promulgator->setFax($request->fax);
        $promulgator->setInternational($request->international);

        // Set QrCode fields
        $this->setQrCodeFields($promulgator, $request);

        $promulgator->setRevisionBy($user);
        $promulgator->setRevisionDateTime(new \DateTime());

        if (!empty($request->referencedStandards)) {
            foreach ($request->referencedStandards as $ref) {
                $referencedStandard = $this->em
                    ->getRepository(ReferenceStandard::class)
                    ->findOneBy([
                        'nodeId'      => $ref['id'],
                        'deletedDate' => null,
                    ]);

                if ($referencedStandard === null) {
                    $referencedStandard = new ReferenceStandard();
                    $promulgator->addChild($referencedStandard);
                }
                [$cleanNumber, $year] = ReferenceStandardYearParser::split($ref['number']);
                $referencedStandard->setNumber($cleanNumber);
                $referencedStandard->setNumberYear($year ?? '');
                $referencedStandard->setTitleYear($year ?? '');
                $referencedStandard->setTitle($ref['title']);
                $referencedStandard->setStatus($ref['status']);
                $referencedStandard->setNavPointerGroup($this->renderNavPointersString($ref['sections_referencing_standard']));

                if ($ref['action'] === 'DELETED') {
                    $referencedStandard->setDeletedDate(new \DateTime());
                    $referencedStandard->setDeletedBy($user);
                }
                $this->em->persist($referencedStandard);
            }
        }
    }

    /**
     * @deprecated
     *
     * @param array $sections
     *
     * @return string
     * @throws \DOMException
     */
    public function renderNavPointersString(array $sections): string
    {
        if (empty($sections)) {
            return '';
        }
        $doc = new \DOMDocument();
        $doc->formatOutput = true;

        $root = $doc->createElement('nav-pointer-group');
        $doc->appendChild($root);

        foreach ($sections as $item) {
            $navPointerElement = $doc->createElement('nav-pointer');

            if (!empty($item['isNew'])) {
                $insert = $doc->createElement('insert', $item['title']);
                $navPointerElement->appendChild($insert);
            } else {
                $navPointerElement->appendChild($doc->createTextNode($item['title']));
            }

            if (isset($item['rid'])) {
                $navPointerElement->setAttribute('rid', $item['rid']);
            }

            $root->appendChild($navPointerElement);
        }

        return $doc->saveXML($root);
    }

    /**
     * @deprecated
     *
     * @param string $nodeId
     *
     * @return array
     */
    public function getNavPointersGroupFromString(string $nodeId): array
    {
        $reference = $this->em
            ->getRepository(ReferenceStandard::class)
            ->findOneBy([
                'nodeId'      => $nodeId,
                'deletedDate' => null,
            ]);

        $values = [];
        if ($reference instanceof ReferenceStandard) {
            $doc = new \DOMDocument();
            $doc->loadXML($reference->getNavPointerGroup());
            $navPointers = $doc->getElementsByTagName('nav-pointer');

            foreach ($navPointers as $navPointer) {
                $values[] = $navPointer->nodeValue;
            }
        }

        return $values;
    }

    /**
     * @deprecated
     *
     * Sanitizes all non-alphanumeric characters from an acronym. Used in create the xml id for a promulgator.
     *
     * @param string $acronym
     *
     * @return string
     */
    public function sanitizeAcronym(string $acronym): string
    {
        // Trim spaces and punctuation characters.
        $acronym = strip_tags(trim($acronym, ' ._-'));

        // Replace all ".", "-", "/" and space characters with underscore.
        $acronym = preg_replace('/[\s\-.\/]+/', '_', $acronym);

        return preg_replace('/[^A-Za-z0-9_]/', '', $acronym);
    }

    public function parseAcronym(string $acronym, string $tagToKeep = 'insert'): string
    {
        return Xml2Helper::clearTrackedChanges($acronym);
    }
}
