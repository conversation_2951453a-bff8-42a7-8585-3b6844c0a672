<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2\Proposals;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\Promulgator;
final class Identifier
{
    public static function ofCodeBook(AbstractCodeBookNode $node): string
    {
        if ($node instanceof Definition) {
            return $node->getTerm() ?? '';
        }

        if ($node instanceof Promulgator) {
            return $node->getAcronym() ?? '';
        }

        return $node->getNumber() ?? '';
    }
}
