<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Validation\XmlValidationResult;
use App\Exception\XmlValidationException;

use function array_map;
use function json_encode;

/**
 * Formats XML validation issues for persistence and transport between backend and UI layers.
 */
class XmlValidationErrorFormatter
{
    public function format(string $message, XmlValidationException $exception): string
    {
        $issues = $this->normalize($exception->getIssues());
        $payload = [
            'kind'                => 'xml_validation',
            'message'             => $message,
            'issues'              => $issues,
            'xmlValidationNodeId' => $issues[0]['sectionId'] ?? null,
        ];

        $encoded = json_encode($payload);

        return $encoded === false ? $message : $encoded;
    }

    /**
     * @param XmlValidationResult[] $issues
     */
    public function normalize(array $issues): array
    {
        return array_map(static fn(XmlValidationResult $issue) => [
            'title'     => $issue->title,
            'body'      => $issue->snippet,
            'sectionId' => $issue->nodeId,
            'number'    => $issue->number,
            'type'      => $issue->type?->value,
        ], $issues);
    }
}
