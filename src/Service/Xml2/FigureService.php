<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\DeleteNodeRequest;
use App\Dto\Xml2\Figure\CreateFigureRequest;
use App\Dto\Xml2\Figure\DeleteFigureResponse;
use App\Dto\Xml2\Figure\UpdateFigureRequest;
use App\Entity\CodeBook\Figure;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\ApiException;
use App\Traits\GetNodeChildrenStatusesTrait;
use App\Traits\QrCodeSetterTrait;
use DateTime;

class FigureService extends AbstractNodeService
{
    use GetNodeChildrenStatusesTrait;
    use QrCodeSetterTrait;

    public function list(Project $project): iterable
    {
        return $this->repo->findByType($project->getCodeBook(), Figure::class);
    }

    /** @throws ApiException */
    public function create(Project $project, CreateFigureRequest $request): Figure
    {
        $figure = new Figure();
        $this->setFields($figure, $request);
        $figure->setNumber($request->number);

        $this->moveToParent($project->getCodeBook(), $figure, $request->parentNode, $request->insertBefore);

        $this->em->persist($figure);
        $this->em->flush();

        return $figure;
    }

    /**
     * @throws ApiException
     */
    public function update(Project $project, Figure $figure, UpdateFigureRequest $request): Figure
    {
        $this->setFields($figure, $request);

        $parent = $figure->getChapterParent();

        $statuses = $this->getChildrenStatuses($parent);
        $this->updateParentStatusIfNeeded($statuses, $parent);

        if ($request->parentNode) {
            $figure->setRelocatedFromAttr($figure->getNodeId());
            $this->moveToParentByAction($project->getCodeBook(), $figure, $request->parentNode, $request->neighbor, $request->action);
        }

        $this->em->flush();

        return $figure;
    }

    public function delete(Figure $figure, DeleteNodeRequest $request): DeleteFigureResponse
    {
        $currentUser = $this->security->getUser();
        $user = (string)$this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);

        $figure->setRevisionBy($user);
        $figure->setRevisionDateTime(new DateTime());
        $figure->deleteNode($user, $request->showDeletionMarker);
        $this->em->flush();

        return new DeleteFigureResponse($figure);
    }

    private function setFields(Figure $figure, UpdateFigureRequest $request): void
    {
        $figure->setStatus($request->status);

        $figure->setSuperTitle($request->superTitle);
        $figure->setNumber($request->number);
        $figure->setCommitteeDesignation($request->committeeDesignation);
        $figure->setLabel($request->label);
        $figure->setCorrelated($request->correlated);
        $figure->setTitle($request->title);
        $figure->setSubTitle($request->subTitle);

        $figure->setMedia($request->content);
        $figure->setCaption($request->caption);
        $figure->setFigureNotes($request->figureNotes);
        $figure->setLegend($request->legend);
        $figure->setSource($request->source);
        $figure->setCreditTitle($request->creditTitle);
        $figure->setCredit($request->credit);
        $figure->setQaCheck($request->qaCheck);

        // Set QrCode fields
        $this->setQrCodeFields($figure, $request);
    }
}
