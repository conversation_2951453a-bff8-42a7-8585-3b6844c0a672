<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Project\DeleteProjectResponse;
use App\Dto\Project\UpdateProjectRequest;
use App\Dto\Xml2\Project\CreateProjectRequest;
use App\Entity\Project;
use App\Entity\ProjectCategory;
use App\Entity\ProjectType;
use App\Entity\User\User;
use App\Entity\VersionType;
use App\Exception\ApiException;
use App\Message\Project\CreateProjectMessage;
use App\Serializer\Dto\Project\ProjectTypeDto;
use App\Serializer\Dto\Project\VersionTypeDto;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\SerializerInterface;

use function array_map;
use function is_int;
use function is_string;

class ProjectService
{
    public function __construct(
        private readonly SerializerInterface    $serializer,
        private readonly EntityManagerInterface $em,
        private readonly MessageBusInterface    $bus
    ) {
    }

    public function getProjectDto(Project $project): array
    {
        return $this->serializer->normalize($project, 'dto', ['groups' => ['project:read']]);
    }

    /** @throws ApiException */
    public function create(CreateProjectRequest $request): Project
    {
        if ($this->checkDuplicateShortCode($request->shortCode)) {
            throw new ApiException(sprintf('Project with Short Code "%s" already exists.', $request->shortCode));
        }

        $message = new CreateProjectMessage($request);
        $this->bus->dispatch($message);

        return $message->getProject();
    }

    /**
     * @throws ApiException
     */
    public function update(Project $project, UpdateProjectRequest $request): void
    {
        if (is_string($request->bookTitle)) {
            $project->setBookTitle($request->bookTitle);
        }
        if (is_string($request->workingTitle)) {
            $project->setWorkingTitle($request->workingTitle);
        }
        if (is_int($request->versionType)) {
            $versionType = $this->em->getRepository(VersionType::class)->find($request->versionType);

            if ($versionType) {
                $project->setVersionType($versionType);
            } else {
                throw new ApiException(sprintf('The version type %d not found.', $request->versionType));
            }
        }
        if (is_string($request->category)) {
            $category = $this->em->getRepository(ProjectCategory::class)->findOneBy(['name' => $request->category]);

            if (null === $category) {
                $category = new ProjectCategory();
                $category->setName($request->category);
                $this->em->persist($category);
            }

            $project->setProjectCategory($category);
        }
        if (is_string($request->manualImagePath)) {
            $project->setManualImagePath($request->manualImagePath);
        }
        if (is_bool($request->commentaryEnabled)) {
            $project->setCommentaryEnabled($request->commentaryEnabled);
        }
        if (is_bool($request->hasCdpAccess)) {
            $project->setHasCdpAccess($request->hasCdpAccess);
        }

        $this->em->flush();
    }

    public function delete(Project $project, User $byUser): DeleteProjectResponse
    {
        $project->setDeleted(true);
        $project->setDeletedBy($byUser->__toString());
        $project->setDeletedDate(new DateTime());

        $this->em->flush();

        return new DeleteProjectResponse;
    }

    public function list(User $user): array
    {
        $user = $this->em->getRepository(User::class)->findOneBy(['email' => $user->getEmail()]);
        $projects = $this->em->getRepository(Project::class)->findByUser($user);

        return array_map([$this, 'getProjectDto'], $projects);
    }

    public function listActiveProjects(User $user): array
    {
        $projects = $this->em->getRepository(Project::class)->findActiveByUser($user);
        return array_map([$this, 'getProjectDto'], $projects);
    }

    public function checkDuplicateShortCode($shortCode): bool
    {
        $existing = $this->em->getRepository(Project::class)->findOneBy([
            'shortCode' => $shortCode,
        ]);
        return $existing instanceof Project;
    }

    public function getProjectVersionTypes(): array
    {
        return array_map(function (VersionType $type): VersionTypeDto {
            $dto = new VersionTypeDto();
            $dto->id = $type->getId();
            $dto->name = $type->getName();
            return $dto;
        }, $this->em->getRepository(VersionType::class)->findAll());
    }

    public function getProjectTypes(): array
    {
        return array_map(function (ProjectType $type): ProjectTypeDto {
            $dto = new ProjectTypeDto();
            $dto->id = $type->getId();
            $dto->name = $type->getName();
            return $dto;
        }, $this->em->getRepository(ProjectType::class)->findAll());
    }
}
