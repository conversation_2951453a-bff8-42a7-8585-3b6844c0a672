<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2\FrontMatter;

use App\Dto\Xml2\FrontMatter\CreatePrefaceRequest;
use App\Dto\Xml2\FrontMatter\DeletePrefaceResponse;
use App\Dto\Xml2\FrontMatter\UpdatePrefaceRequest;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\Preface;
use App\Entity\Project;
use App\Exception\ApiException;
use App\Service\Xml2\AbstractNodeService;
use Symfony\Component\HttpFoundation\Response;

class PrefaceService extends AbstractNodeService
{
    /** @throws ApiException */
    public function create(Project $project, CreatePrefaceRequest $request): Preface
    {
        $frontMatter = $this->repo->findByChildNodeId($project->getCodeBook(), $request->parentNode);
        if (!$frontMatter instanceof FrontMatter) {
            throw new ApiException(
                'Preface can only be added to a FrontMatter.',
                Response::HTTP_NOT_ACCEPTABLE);
        }
        if (null !== $frontMatter->getPreface()) {
            throw new ApiException(
                'Preface already exists in FrontMatter.',
                Response::HTTP_NOT_ACCEPTABLE);
        }

        $preface = new Preface();
        $this->setFields($preface, $request);
        $this->moveToParent($project->getCodeBook(), $preface, $request->parentNode, '');
        $frontMatter->addChild($preface);

        $this->em->persist($preface);
        $this->em->flush();

        return $preface;
    }

    public function update(Preface $preface, UpdatePrefaceRequest $request): Preface
    {
        $this->setFields($preface, $request);
        $this->em->flush();

        return $preface;
    }

    public function delete(Preface $preface): DeletePrefaceResponse
    {
        $preface->getParent()->removeChild($preface);
        $this->em->remove($preface);
        $this->em->flush();

        return new DeletePrefaceResponse();
    }

    private function setFields(Preface $preface, UpdatePrefaceRequest $request): void
    {
        $preface->setBody($request->body);
    }
}
