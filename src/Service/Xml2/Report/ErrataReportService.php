<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2\Report;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use DOMElement;
use DOMXPath;
use ICC\Component\CHub\CHubService;
use SplFileObject;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Twig\Environment;

use function array_unshift;
use function file_put_contents;
use function sys_get_temp_dir;
use function tempnam;
use function unlink;

class ErrataReportService
{
    public function __construct(
        private readonly Environment         $twig,
        private readonly CHubService         $chubService,
        private readonly Xml2Encoder         $xml2Encoder,
        private readonly CodeBookToXmlMapper $codeBookToXmlMapper,
    ) {
    }

    public function getErrataReport(Project $project, AbstractCodeBookNode $rootNode): Response
    {
        $xml2element = $this->codeBookToXmlMapper->map($rootNode);
        $xml = $this->xml2Encoder->encode($xml2element);

        $html = $this->getHtmlFromContentHub($xml);
        $errataList = $this->getErrataList($html);

        $report = $this->twig->render('xml2/report/errata.html.twig', [
            'project'    => $project,
            'errataList' => $errataList,
        ]);

        $spl = new SplFileObject(tempnam(sys_get_temp_dir(), 'ErrataReport'), 'r+');
        $spl->fwrite($report);

        $pdf = $this->chubService->getServiceApi()->convertHtmlToPdf([
            'media' => 'print',
            'file'  => new UploadedFile($spl->getPathname(), 'ErrataReport.pdf'),
        ]);

        return $pdf;
    }

    private function getHtmlFromContentHub(string $xml): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'ErrataReport');
        file_put_contents($tempFile, $xml);

        $uploadedFile = new UploadedFile($tempFile, 'errata-report.html', 'text/html');
        $response = $this->chubService->getServiceApi()->convertXmlToHtml([
            'file' => $uploadedFile,
        ]);

        unlink($tempFile);

        return $response->getContent();
    }

    private function getErrataList(string $html): array
    {
        $document = Xml2Helper::createDOMDocument($html);
        $xpath = new DOMXPath($document);
        $xpath->registerNamespace('x', 'http://www.w3.org/1999/xhtml');

        $errataList = [];
        $nodeList = $xpath->query('//x:span[@class="insert"]/ancestor::*[@id][1]', $document->documentElement);

        for ($i = $nodeList->length; $i > 0; $i--) {
            $node = $nodeList->item($i - 1);

            for ($k = $node->childNodes->length; $k > 0; $k--) {
                $child = $node->childNodes->item($k - 1);
                if ($child instanceof DOMElement && $child->hasAttribute('id')) {
                    $node->removeChild($child);
                }
            }

            array_unshift($errataList, $document->saveXML($node));
        }

        return $errataList;
    }
}
