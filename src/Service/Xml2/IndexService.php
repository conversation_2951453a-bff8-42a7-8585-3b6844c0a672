<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Index\CreateIndexEntryRequest;
use App\Dto\Xml2\Index\DeleteIndexEntryResponse;
use App\Dto\Xml2\Index\UpdateIndexEntryRequest;
use App\Dto\Xml2\Index\UpdateIndexEntryResponse;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Index;
use App\Entity\CodeBook\IndexDivision;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\SecondaryIndexEntry;
use App\Entity\CodeBook\TertiaryIndexEntry;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\ApiException;
use App\Repository\CodeBookNodeRepository;
use App\Traits\GetNodeChildrenStatusesTrait;
use App\Traits\QrCodeSetterTrait;
use App\Util\TextUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

use function array_fill_keys;
use function array_filter;
use function range;

class IndexService extends AbstractNodeService
{
    use GetNodeChildrenStatusesTrait;
    use QrCodeSetterTrait;

    public function __construct(
        EntityManagerInterface              $em,
        CodeBookNodeRepository              $repo,
        Security                            $security,
        private readonly PromulgatorService $promulgatorService,
    ) {
        parent::__construct($em, $repo, $security);
    }

    public function list(Project $project): iterable
    {
        return $this->repo->findByType($project->getCodeBook(), IndexEntry::class);
    }

    /** @throws ApiException */
    public function create(Publication $codeBook, CreateIndexEntryRequest $request): IndexEntry
    {
        $indexEntry = new IndexEntry();
        $this->setFields($indexEntry, $request);

        $parentNode = $this->em->getRepository(AbstractCodeBookNode::class)->findOneBy([
            'publication' => $codeBook,
            'nodeId'      => $request->parentNode,
        ]);
        if ($parentNode) {
            $parentNode->addChild($indexEntry);
        }

        $this->em->persist($indexEntry);
        $this->em->flush();

        return $indexEntry;
    }

    /** @throws ApiException */
    public function update(IndexEntry $indexEntry, UpdateIndexEntryRequest $request): UpdateIndexEntryResponse
    {
        $user = (string) $this->security->getUser();
        $this->setFields($indexEntry, $request);
        $this->updateSubTerms($request->subTerms, $indexEntry, $user);
        $this->em->persist($indexEntry);

        $parent = $indexEntry->getChapterParent();

        $statuses = $this->getChildrenStatuses($parent);
        $this->updateParentStatusIfNeeded($statuses, $parent);


        $this->em->flush();

        return new UpdateIndexEntryResponse($indexEntry);
    }

    public function delete(IndexEntry $indexEntry): DeleteIndexEntryResponse
    {
        $currentUser = $this->security->getUser();
        $user = (string) $this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);

        $indexEntry->setRevisionBy($user);
        $indexEntry->deleteNode($user);
        $this->em->flush();

        return new DeleteIndexEntryResponse($indexEntry);
    }

    private function updateSubTerms(array $subTerms, AbstractCodeBookNode $parentEntry, string $user): void
    {
        foreach ($subTerms as $subTermRequest) {
            $subTerm = null;

            if (!empty($subTermRequest['nodeId'])) {
                $subTerm = $this->findSubTerm($subTermRequest, $parentEntry);
            }

            if (!empty($subTermRequest['isDeleted']) && $subTerm) {
                $subTerm->setRevisionBy($user);
                $subTerm->deleteNode($user);
                $this->em->persist($subTerm);
                continue;
            }

            if (!$subTerm && !empty($subTermRequest['isNew']) && $subTermRequest['isNew']) {
                $subTerm = $this->createNewSubTerm($parentEntry);
            }

            if ($subTerm) {
                $subTerm->setTerm($subTermRequest['term']);
                $subTerm->setStatus($subTermRequest['status']);
                $navPointerGroup = $this->promulgatorService->renderNavPointersString($subTermRequest['sectionsReferencing']);
                $subTerm->setNavPointerGroup($navPointerGroup);

                if (!empty($subTermRequest['subTerms'])) {
                    $this->updateSubTerms($subTermRequest['subTerms'], $subTerm, $user);
                }

                $this->em->persist($subTerm);
            }
        }
    }

    private function findSubTerm(array $subTermRequest, AbstractCodeBookNode $parentEntry)
    {
        if ($parentEntry instanceof IndexEntry) {
            return $this->em->getRepository(SecondaryIndexEntry::class)->findOneBy([
                'nodeId'      => $subTermRequest['nodeId'],
                'parent'      => $parentEntry->getId(),
                'deletedDate' => null,
            ]);
        } elseif ($parentEntry instanceof SecondaryIndexEntry) {
            return $this->em->getRepository(TertiaryIndexEntry::class)->findOneBy([
                'nodeId'      => $subTermRequest['nodeId'],
                'parent'      => $parentEntry->getId(),
                'deletedDate' => null,
            ]);
        }
        return null;
    }

    private function createNewSubTerm(AbstractCodeBookNode $parentEntry)
    {
        $subTerm = $parentEntry instanceof IndexEntry
            ? new SecondaryIndexEntry()
            : new TertiaryIndexEntry();

        $parentEntry->appendChild($subTerm);
        return $subTerm;
    }

    private function getIndexDivision(Index $index, string $letter): IndexDivision
    {
        $validKeys = range('A', 'Z');
        $validKeys[] = '#';
        $divisions = array_fill_keys($validKeys, null);

        foreach ($index->getChildren() as $division) {
            if (!$division instanceof IndexDivision) {
                continue;
            }

            $key = $division->getTitle();

            if (!in_array($key, $validKeys, true)) {
                throw new ApiException('Invalid division title: ' . $key);
            }

            if ($divisions[$key] instanceof IndexDivision) {
                throw new ApiException('Duplicate division title: ' . $key);
            }

            $divisions[$key] = $division;
        }

        if (!in_array($letter, $validKeys)) {
            $letter = '#';
        }

        if (!$divisions[$letter] instanceof IndexDivision) {
            $divisions[$letter] = new IndexDivision();
            $divisions[$letter]->setTitle($letter);
            $index->setChildren(array_filter($divisions));

            $this->em->persist($divisions[$letter]);
        }

        return $divisions[$letter];
    }

    private function setFields(IndexEntry $indexEntry, UpdateIndexEntryRequest $request): void
    {
        $indexEntry->setTerm($request->term);
        $indexEntry->setNavPointerGroup($this->promulgatorService->renderNavPointersString($request->sectionsReferencing));
        $indexEntry->setStatus($request->status);

        // Set QrCode fields
        $this->setQrCodeFields($indexEntry, $request);
    }

    /**
     * @deprecated
     */
    public function generateSubEntryId(string $entryId, string $term, string $level = 'secondary'): string
    {
        $sanitizedTerm = TextUtils::sanitizeTerm($term);

        if ($level === 'tertiary') {
            return sprintf('%s_SubSubIndex%s', $entryId, $sanitizedTerm);
        }
        return sprintf('%s_SubIndex%s', $entryId, $sanitizedTerm);
    }
}
