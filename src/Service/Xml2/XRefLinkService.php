<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\XRefLink\XRefLinkValidationResult;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\XRefLink;
use App\Entity\Project;
use App\Enum\XRefLinkValidationStatus;
use App\Repository\CodeBookNodeRepository;
use App\Repository\XRefLinkRepository;
use App\Service\CodeBook\CodeBookPathIdService;
use App\Service\Xml2\Proposals\Identifier;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

final class XRefLinkService extends AbstractNodeService
{
    public function __construct(
        EntityManagerInterface                 $em,
        CodeBookNodeRepository                 $repository,
        Security                               $security,
        private readonly XRefLinkRepository    $xRefLinkRepository,
        private readonly CodeBookPathIdService $pathIdService,
    ) {
        parent::__construct($em, $repository, $security);
    }

    /**
     * @return XRefLinkValidationResult[]
     */
    public function validateOutgoingLinksByChapterTree(Project $project, AbstractCodeBookNode $chapter): array
    {
        $links = $this->xRefLinkRepository->findOutgoingByNodeTree($chapter);

        $results = [];
        foreach ($links as $link) {
            $results[] = $this->validateLink($project, $chapter, $link);
        }
        return $results;
    }

    private function validateLink(Project $project, AbstractCodeBookNode $chapter, XRefLink $link
    ): XRefLinkValidationResult {
        $to = $link->getToNode();

        if ($to === null) {
            return $this->buildResult(
                $project,
                $chapter,
                $link,
                XRefLinkValidationStatus::INVALID,
                'Referenced node not found (rid may be invalid or stale).'
            );
        }

        if ($to->getDeletedDate() !== null) {
            return $this->buildResult(
                $project,
                $chapter,
                $link,
                XRefLinkValidationStatus::INVALID,
                'Referenced node is deleted.'
            );
        }

        $expected = $this->expectedDisplayFor($to);
        if ($expected === null) {
            return $this->buildResult(
                $project,
                $chapter,
                $link,
                XRefLinkValidationStatus::VALID,
                'OK (no expected format rule)'
            );
        }

        $actual = $link->getOriginalBody();
        $toNodeId = $this->pathIdService->getHumanReadablePath($to);
        if ($this->normalizeStrong($actual) === $this->normalizeStrong($expected)) {
            return $this->buildResult(
                $project,
                $chapter,
                $link,
                XRefLinkValidationStatus::VALID,
                sprintf('Section Number "%s" (%s)', Identifier::ofCodeBook($to), $toNodeId),
                $expected,
                $actual
            );
        }

        if ($this->normalizeWeak($actual) === $this->normalizeWeak($expected)) {
            return $this->buildResult(
                $project,
                $chapter,
                $link,
                XRefLinkValidationStatus::WARNING,
                sprintf('Link text "%s" differs in case/punctuation from expected "%s".', $actual, $expected),
                $expected,
                $actual
            );
        }

        return $this->buildResult(
            $project,
            $chapter,
            $link,
            XRefLinkValidationStatus::WARNING,
            sprintf('Link text "%s" does not match referenced number/title "%s".', $actual, $expected),
            $expected,
            $actual
        );
    }

    private function buildResult(
        Project                  $project,
        AbstractCodeBookNode     $chapter,
        XRefLink                 $link,
        XRefLinkValidationStatus $status,
        string                   $message,
        ?string                  $expected = null,
        ?string                  $actual = null,
    ): XRefLinkValidationResult {
        $codeBookUlid = $link->getCodeBook()?->getUlid() ?? '';

        $fromNode = $link->getFromNode();
        $toNode = $link->getToNode();
        $fromPathId = $fromNode?->getPathId() ?? '';
        $toNodeUuid = $link->getToNodeUuid() ?? '';
        $fromUlid = $fromNode?->getUlid() ?? '';

        $toPathShort = $toNode
            ? $this->pathIdService->getHumanReadablePath($toNode)
            : '';
        $fromPathShort = $fromNode
            ? $this->pathIdService->getHumanReadablePath($fromNode)
            : '';

        return new XRefLinkValidationResult(
            $codeBookUlid,
            (string) ($link->getOriginalBody() ?? ''),
            $fromPathId,
            $toNodeUuid,
            $toPathShort,
            $fromUlid,
            $fromPathShort,
            $chapter->getUlid(),
            $project->getShortCode(),
            $status,
            $message,
            $expected,
            $actual
        );
    }


    private function expectedDisplayFor(AbstractCodeBookNode $node): ?string
    {
        return match (true) {
            $node instanceof Section           => 'Section ' . (string) $node->getNumber(),
            $node instanceof Table             => 'Table ' . (string) $node->getNumber(),
            $node instanceof Figure            => 'Figure ' . (string) $node->getNumber(),
            $node instanceof Chapter           => 'Chapter ' . (string) $node->getNumber(),
            $node instanceof Appendix          => 'Appendix ' . (string) $node->getNumber(),
            $node instanceof Definition        => (string) $node->getTerm(),
            $node instanceof IndexEntry        => (string) $node->getTerm(),
            $node instanceof Promulgator       => (string) $node->getAcronym(),
            $node instanceof ReferenceStandard => (string) $node->getNumber(),
            default                            => null,
        };
    }

    private function normalizeStrong(string $s): string
    {
        $s = str_replace(["\u{2014}", "\u{2013}", "\u{2011}"], '-', $s);
        $s = preg_replace('/\s+/u', ' ', $s);
        return trim($s);
    }

    private function normalizeWeak(string $s): string
    {
        $s = $this->normalizeStrong($s);
        $s = mb_strtolower($s, 'UTF-8');
        $s = preg_replace('/[^a-z0-9]+/u', '', $s);
        return $s;
    }
}
