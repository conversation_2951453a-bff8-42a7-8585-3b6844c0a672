<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\ErrorResponseDto;
use App\Dto\ResponseDtoInterface;
use App\Dto\Xml2\Chapter\CreateChapterRequest;
use App\Dto\Xml2\Chapter\GetChapterSectionsResponse;
use App\Dto\Xml2\Chapter\GetChapterVersionsListResponse;
use App\Dto\Xml2\Chapter\GetChapterVersionsResponse;
use App\Dto\Xml2\Chapter\UpdateChapterRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\CopyrightPage;
use App\Entity\CodeBook\Foreword;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\Index;
use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\TitlePage;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\ApiException;
use App\Repository\CodeBookNodeRepository;
use App\Traits\NumberCleanerTrait;
use App\Traits\QrCodeSetterTrait;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Response;

use function array_key_exists;
use function array_map;
use function array_merge;
use function array_slice;
use function is_dir;
use function is_readable;
use function mb_strpos;
use function mb_substr;
use function scandir;
use function sort;
use function sprintf;

class ChapterService extends AbstractNodeService
{
    use NumberCleanerTrait;
    use QrCodeSetterTrait;

    public function __construct(
        EntityManagerInterface           $em,
        CodeBookNodeRepository           $repository,
        Security                         $security,
        private readonly ContentsService $contentsService,
        private readonly XRefLinkService $xRefLinkService,
        private readonly SectionService  $sectionService,
        private readonly string          $kernelRootDir,
    ) {
        parent::__construct($em, $repository, $security);
    }

    public function list(Project $project): iterable
    {
        return $this->repo->findByType($project->getCodeBook(), Chapter::class);
    }

    /** @throws ApiException */
    public function create(Project $project, CreateChapterRequest $request): Chapter
    {
        $chapter = new Chapter();
        $this->setFields($chapter, $request);
        $chapter->setNumber($request->number);
        $this->moveToParentByAction($project->getCodeBook(), $chapter, $request->parentNode, $request->neighbor, $request->action);

        $this->em->persist($chapter);
        $this->em->flush();

        return $chapter;
    }

    /**
     * @throws ApiException
     */
    public function update(Project $project, Chapter $chapter, UpdateChapterRequest $request): Chapter
    {
        $this->setFields($chapter, $request);

        if ($request->parentNode) {
            $chapter->setRelocatedFromAttr($chapter->getNodeId());
            $this->moveToParentByAction($project->getCodeBook(), $chapter, $request->parentNode, $request->neighbor, $request->action);

            $this->sectionService->relocateAndUpdateChildren($chapter, $project->getCodeBook()->getVolume(), $request->newNumber);
        }

        $this->em->flush();

        return $chapter;
    }

    public function delete(Chapter $chapter): void
    {
        $currentUser = $this->security->getUser();
        $user = (string) $this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);

        $chapter->setRevisionBy($user);
        $chapter->setRevisionDateTime(new DateTime());
        $chapter->deleteNode($user);
        $this->em->flush();
    }

    private function setFields(Chapter $chapter, UpdateChapterRequest $request): void
    {
        $chapter->setSuperTitle($request->superTitle);
        $chapter->setCommitteeDesignation($request->committeeDesignation);
        $chapter->setLabel($request->label);
        $chapter->setCorrelated($request->correlated);
        $chapter->setNumber($request->number);
        $chapter->setTitle($request->title);
        $chapter->setSubTitle($request->subTitle);
        $chapter->setBody($request->body);
        $chapter->setHistory($request->history);
        $chapter->setObjectivesTitle($request->objectivesTitle);
        $chapter->setObjectives($request->objectives);
        $chapter->setAbstractTitle($request->abstractTitle);
        $chapter->setAbstract($request->abstract);
        $chapter->setKeywordsTitle($request->keywordsTitle);
        $chapter->setKeywords($request->keywords);

        // Set QrCode fields
        $this->setQrCodeFields($chapter, $request);
    }

    public function getSections(AbstractCodeBookNode $node, bool $isReferenceLinks): ResponseDtoInterface
    {
        $sections = $isReferenceLinks ? [] : $this->addNodeAsSection($node);
        $values = $this->assignValues($node->getChildren());

        return new GetChapterSectionsResponse(
            $node->getParent()->getNodeId(),
            $node->getUlid(),
            array_merge($sections, $values['sections']),
            $values['promulgators'],
            $values['backMatter']
        );
    }

    public function getReferenceSections(string $ulid): ResponseDtoInterface
    {
        $node = $this->repo->findNodeByUlid($ulid);

        if (null === $node) {
            $node = $this->repo->findNodeById($ulid);

            if (null === $node) {
                throw new ApiException(sprintf('Node with ulid "%s" was not found.', $ulid));
            }
        }
        $values = $this->assignValues($node->getChildren(false));

        return new GetChapterSectionsResponse(
            $node->getParent()->getNodeId(),
            $node->getUlid(),
            $values['sections'],
            $values['promulgators'],
            $values['backMatter']
        );
    }

    public function getChapterVersions(Project $project, User $user, string $pdfPrefix)
    {
        $qb = $this->em->getRepository(Project::class)->createQueryBuilder('p')
                       ->select('p.shortCode');

        if ($user->hasRole('ROLE_SUPER_ADMIN') || ($user->hasRole('ROLE_ADMIN') && count($user->getCategories()->toArray()) == 0)) {
            $projects = $qb->getQuery()->getResult();
        } else {
            $qb->leftJoin('p.users', 'u')
               ->join('p.projectCategory', 'pc')
               ->leftJoin('pc.users', 'cu')
               ->where('u.id = :userId OR cu.id = :userId')
               ->setParameter('userId', $user->getId())
               ->orderBy('p.id', 'ASC');

            $projects = $qb->getQuery()->getResult();
        }

        $shortCodes = array_map(function ($project) {
            return $project['shortCode'];
        }, $projects);

        return in_array($project->getShortCode(), $shortCodes)
            ? $this->generateChapterVersionsResponse($project, $pdfPrefix)
            : new ErrorResponseDto('Permission denied for this book.', Response::HTTP_UNAUTHORIZED);
    }

    private function generateChapterVersionsResponse(Project $project, string $pdfPrefix)
    {
        $fileList = $this->getFileList($project->getShortCode());

        if (!$fileList) {
            return new ErrorResponseDto(
                sprintf('Versions "%s" not found.', $project->getShortCode()),
                Response::HTTP_NOT_FOUND
            );
        }

        $chapters = $this->getAllChapters($project);

        $chaptersById = [];
        foreach ($chapters as $chapter) {
            $chaptersById[$chapter['id']] = $chapter;
        }

        $versions = [];
        foreach ($fileList as $filename) {
            $chapterId = mb_substr($filename, 0, mb_strpos($filename, '__'));

            if (array_key_exists($chapterId, $versions)) {
                $versions[$chapterId][] = ['filename' => $filename, 'url' => $pdfPrefix . $filename];
            } else {
                $versions[$chapterId] = [
                    ['filename' => $filename, 'url' => $pdfPrefix . $filename],
                ];
            }
        }
        $output = new GetChapterVersionsListResponse();
        $output->versionList = [];

        foreach ($versions as $id => $versionArray) {
            sort($versionArray);
            if (isset($chaptersById[$id])) {
                $chapter = $chaptersById[$id];
                $chapterResponse = new GetChapterVersionsResponse();
                $chapterResponse->id = $id;
                $chapterResponse->label = $chapter['label'];
                $chapterResponse->number = $chapter['number'];
                $chapterResponse->title = $chapter['title'];
                $chapterResponse->versions = $versionArray;

                $output->versionList[] = $chapterResponse;
            }
        }

        return $output;
    }

    private function getFileList(string $shortCode): array
    {
        try {
            $directoryPath = $this->kernelRootDir . '/public/files/backups/' . $shortCode;

            if (!is_dir($directoryPath) || !is_readable($directoryPath)) {
                return [];
            }

            $fileList = scandir($directoryPath);

            if ($fileList === false) {
                return [];
            }

            return array_slice($fileList, 2);
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getAllChapters(Project $project): array
    {
        $chapterResponse = $this->contentsService->getContents($project);
        $tableOfContents = $chapterResponse->book->tableOfContents;

        $chapters = [];
        if (!empty($tableOfContents->chapters)) {
            $chapters = array_merge($chapters, $tableOfContents->chapters);
        }
        if (!empty($tableOfContents->frontMatter)) {
            $chapters = array_merge($chapters, $tableOfContents->frontMatter);
        }
        if (!empty($tableOfContents->backMatter)) {
            $chapters = array_merge($chapters, $tableOfContents->backMatter);
        }

        return $chapters;
    }

    private function assignValues(array $nodes): array
    {
        $sections = [];
        $promulgators = [];
        $backMatter = [];

        foreach ($nodes as $node) {
            switch (true) {
                case $node instanceof Section:
                    $sections[] = $node;
                    break;

                case $node instanceof Promulgator:
                    $promulgators[] = $node;
                    break;

                case $node instanceof BackMatter:
                    $backMatter[] = $node;
                    break;

                default:
                    $sections[] = $node;
            }
        }

        usort($promulgators, function (Promulgator $a, Promulgator $b) {
            $nameA = preg_replace('/[^a-zA-Z0-9]/', '', strip_tags($a->getAcronym()));
            $nameB = preg_replace('/[^a-zA-Z0-9]/', '', strip_tags($b->getAcronym()));
            return strcasecmp($nameA, $nameB);
        });

        foreach ($promulgators as $promulgator) {
            foreach ($promulgator->getChildren() as $child) {
                if (method_exists($child, 'getNavPointerGroup') && $child->getNavPointerGroup()) {
                    $child->setNavPointerGroup(
                        $this->sortNavPointers($child->getNavPointerGroup())
                    );
                }
            }
        }

        return [
            'sections'     => $sections,
            'promulgators' => $promulgators,
            'backMatter'   => $backMatter,
        ];
    }

    private function addNodeAsSection(AbstractCodeBookNode $node): array
    {
        $sections = [];

        if ($node instanceof Chapter) {
            $sections[] = $this->mapChapterAsSection($node);
        } elseif ($node instanceof Index) {
            $sections[] = $this->mapIndexAsSection($node);
        } elseif ($node instanceof Appendix) {
            $sections[] = $node;
        } elseif ($node instanceof Foreword) {
            $sections[] = $node;
        } elseif ($node instanceof CopyrightPage) {
            $sections[] = $node;
        } elseif ($node instanceof TitlePage) {
            $sections[] = $node;
        } elseif ($node instanceof Section && $node->getParent() instanceof FrontMatter) {
            $sections[] = $this->mapSectionAsFMChapter($node);
        }

        return $sections;
    }

    private function mapChapterAsSection(Chapter $chapter): Chapter
    {
        $newChapter = new Chapter();
        $newChapter->setUlid($chapter->getUlid());
        $newChapter->setStatus($chapter->getStatus());
        $newChapter->setNodeId($chapter->getNodeId());
        $newChapter->setSuperTitle($chapter->getSuperTitle());
        $newChapter->setCommitteeDesignation($chapter->getCommitteeDesignation());
        $newChapter->setLabel($chapter->getLabel());
        $newChapter->setNumber($chapter->getNumber());
        $newChapter->setTitle($chapter->getTitle());
        $newChapter->setSubTitle($chapter->getSubTitle());
        $newChapter->setPubsNotes($chapter->getPubsNotes());
        $newChapter->setCodesNotes($chapter->getCodesNotes());
        $newChapter->setBody($chapter->getBody());
        $newChapter->setHistory($chapter->getHistory());
        $newChapter->setObjectives($chapter->getObjectives());
        $newChapter->setObjectivesTitle($chapter->getObjectivesTitle());
        $newChapter->setAbstract($chapter->getAbstract());
        $newChapter->setAbstractTitle($chapter->getAbstractTitle());
        $newChapter->setKeywords($chapter->getKeywords());
        $newChapter->setKeywordsTitle($chapter->getKeywordsTitle());
        $newChapter->setDeletedBy($chapter->getDeletedBy());
        $newChapter->setDeletedDate($chapter->getDeletedDate());

//        $newChapter->setQrUrl($chapter->getQrUrl());
//        $newChapter->setQrIcon($chapter->getQrIcon());
//        $newChapter->setQrDepartment($chapter->getQrDepartment());
//        $newChapter->setqrBusinessUnit($chapter->getqrBusinessUnit());
//        $newChapter->setQrImage($chapter->getQrImage());
//        $newChapter->setQrShortUrl($chapter->getQrShortUrl());
//        $newChapter->setQrCodeDisplay($chapter->getQrCodeDisplay());
        $newChapter->setShowDeletionMarker($chapter->getShowDeletionMarker());
        $newChapter->setCorrelated($chapter->getCorrelated());

        return $newChapter;
    }

    private function mapSectionAsFMChapter(Section $section): Chapter
    {
        $newFMChapter = new Chapter();
        $newFMChapter->setUlid($section->getUlid());
        $newFMChapter->setStatus($section->getStatus());
        $newFMChapter->setRole('frontmatter-chapter');
        //        $newFMChapter->setNodeId($section->getNodeId());
        $newFMChapter->setSuperTitle($section->getSuperTitle());
        $newFMChapter->setCommitteeDesignation($section->getCommitteeDesignation());
        $newFMChapter->setLabel($section->getLabel());
        $newFMChapter->setNumber($section->getNumber());
        $newFMChapter->setTitle($section->getTitle());
        $newFMChapter->setSubTitle($section->getSubTitle());
        $newFMChapter->setPubsNotes($section->getPubsNotes());
        $newFMChapter->setCodesNotes($section->getCodesNotes());
        $newFMChapter->setBody($section->getBody());

//        $newFMChapter->setQrUrl($section->getQrUrl());
//        $newFMChapter->setQrIcon($section->getQrIcon());
//        $newFMChapter->setQrDepartment($section->getQrDepartment());
//        $newFMChapter->setqrBusinessUnit($section->getqrBusinessUnit());
//        $newFMChapter->setQrImage($section->getQrImage());
//        $newFMChapter->setQrShortUrl($section->getQrShortUrl());
//        $newFMChapter->setQrCodeDisplay($section->getQrCodeDisplay());
        $newFMChapter->setShowDeletionMarker($section->getShowDeletionMarker());

        return $newFMChapter;
    }

    private function mapIndexAsSection(Index $index): Index
    {
        $newIndex = new Index();
        $newIndex->setUlid($index->getUlid());
        $newIndex->setStatus($index->getStatus());
        //        $newIndex->setNodeId($index->getNodeId());
        $newIndex->setTitle($index->getTitle());
        $newIndex->setDeletedBy($index->getDeletedBy());
        $newIndex->setDeletedDate($index->getDeletedDate());

//        $newIndex->setQrUrl($index->getQrUrl());
//        $newIndex->setQrIcon($index->getQrIcon());
//        $newIndex->setQrDepartment($index->getQrDepartment());
//        $newIndex->setqrBusinessUnit($index->getqrBusinessUnit());
//        $newIndex->setQrImage($index->getQrImage());
//        $newIndex->setQrShortUrl($index->getQrShortUrl());
//        $newIndex->setQrCodeDisplay($index->getQrCodeDisplay());
        $newIndex->setShowDeletionMarker($index->getShowDeletionMarker());

        return $newIndex;
    }

    private function sortNavPointers(string $navPointerGroupXml): string
    {
        if (empty($navPointerGroupXml)) {
            return $navPointerGroupXml;
        }
        $xml = simplexml_load_string($navPointerGroupXml);
        if ($xml === false) {
            return $navPointerGroupXml;
        }
        $pointers = [];
        foreach ($xml->{'nav-pointer'} as $navPointer) {
            $content = trim((string) $navPointer);
            $xmlText = trim(strip_tags($navPointer->asXML()));
            $textToSort = !empty($content) ? $content : $xmlText;
            $numericValue = PHP_FLOAT_MAX;
            if (!empty($textToSort) && preg_match('/\d+(?:\.\d+)*/', $textToSort, $matches)) {
                $numericValue = (float) $matches[0];
            }
            $pointers[] = [
                'xml'           => $navPointer->asXML(),
                'numeric_value' => $numericValue,
                'original_text' => $textToSort,
            ];
        }
        usort($pointers, function ($a, $b) {
            if ($a['numeric_value'] !== $b['numeric_value']) {
                return $a['numeric_value'] <=> $b['numeric_value'];
            }
            return strnatcasecmp($a['original_text'], $b['original_text']);
        });
        return '<nav-pointer-group>' . implode('', array_column($pointers, 'xml')) . '</nav-pointer-group>';
    }

    public function getAllLinksValidationResult(Project $project, AbstractCodeBookNode $node): array
    {
        return $this->xRefLinkService->validateOutgoingLinksByChapterTree($project, $node);
    }
}
