<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\NoteAttachment\CreateNoteAttachmentRequest;
use App\Dto\Xml2\NoteAttachment\CreateNoteAttachmentResponse;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\File\RemoteFile;
use App\Entity\NoteAttachment;
use App\Exception\ApiException;
use App\Repository\CodeBookNodeRepository;
use App\Repository\NoteAttachmentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

use function base64_decode;
use function hash;
use function mb_strpos;
use function mb_substr;

class NoteAttachmentService extends AbstractNodeService
{
    public function __construct(
        EntityManagerInterface                    $em,
        CodeBookNodeRepository                    $repo,
        Security                                  $security,
        private readonly NoteAttachmentRepository $attachmentsRepository,
        private readonly UrlGeneratorInterface    $urlGenerator,

    ) {
        parent::__construct($em, $repo, $security);
    }

    /** @throws ApiException */
    public function create(
        CreateNoteAttachmentRequest $request,
        string                      $bookId,
        AbstractCodeBookNode        $node
    ): CreateNoteAttachmentResponse {

        if (!preg_match('#^data:application/[a-zA-Z0-9.+-]+;base64,#', $request->file)) {
            throw new ApiException('Invalid data URI format.');
        }

        $encoded = mb_substr($request->file, mb_strpos($request->file, ',') + 1);
        $decoded = base64_decode($encoded, true);
        if ($decoded === false) {
            throw new ApiException('Invalid base64 encoding.');
        }

        $allowed = ['doc', 'docx', 'pdf', 'txt', 'xls', 'xlsx'];
        $ext = strtolower(pathinfo($request->fileName, PATHINFO_EXTENSION));
        if (!in_array($ext, $allowed, true)) {
            throw new ApiException(
                sprintf('Invalid file extension. Allowed: %s', implode(', ', $allowed))
            );
        }

        $hash = hash('sha256', $decoded);
        $fileRepo = $this->em->getRepository(RemoteFile::class);

        $remoteFile = $fileRepo->findOneBy(['hash' => $hash]);

        if (!$remoteFile) {
            $tmp = tempnam(sys_get_temp_dir(), 'attachment');
            file_put_contents($tmp, $decoded);
            $mime = mime_content_type($tmp) ?: 'application/octet-stream';
            unlink($tmp);

            $remoteFile = new RemoteFile();
            $remoteFile->setHash($hash);
            $remoteFile->setMimeType($mime);
            $remoteFile->setContents($request->file);

            $this->em->persist($remoteFile);
        }

        $currentUser = $this->security->getUser();
        $attachment = new NoteAttachment();
        $attachment->setBookId($bookId);
        $attachment->setSectionId($node->getNodeId());
        $attachment->setNode($node);
        $attachment->setNoteType($request->noteType);
        $attachment->setFilename($request->fileName);
        $attachment->setUploadedBy($currentUser->getEmail());
        $attachment->setUploadedAt(new \DateTime());
        $attachment->setFile($remoteFile);

        $this->em->persist($attachment);
        $this->em->flush();

        $url = $this->urlGenerator->generate(
            'app_note_attachment',
            ['hash' => $hash],
            UrlGeneratorInterface::ABSOLUTE_URL
        );

        return CreateNoteAttachmentResponse::create($attachment, $url);
    }

    /** @throws ApiException */
    public function delete(int $attachmentId): void
    {
        /** @var NoteAttachment|null $attachment */
        $attachment = $this->em->find(NoteAttachment::class, $attachmentId);

        if (!$attachment) {
            throw new ApiException('Attachment not found.', Response::HTTP_NOT_FOUND);
        }

        if (!$this->security->isGranted('DELETE', $attachment)) {
            throw new ApiException('Access denied.', Response::HTTP_FORBIDDEN);
        }

        $remote = $attachment->getFile();

        $this->em->remove($attachment);
        $this->em->flush();

        if ($remote) {
            $usedElsewhere = $this->attachmentsRepository->countByRemoteFile($remote);
            if ((int) $usedElsewhere === 0) {
                $this->em->remove($remote);
                $this->em->flush();
            }
        }
    }
}
