<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Cdp\CdpGetLastSync;
use App\Dto\Xml2\Cdp\CdpGetProposalsList;
use App\Dto\Xml2\Cdp\CdpUpdateProposalRequest;
use App\Dto\Xml2\Cdp\CdpUpdateProposalResponse;
use App\Dto\Xml2\Cdp\CreateFromProposalActionRequest;
use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\Project;
use App\Entity\Project\CodeChange\DefinitionCodeChange;
use App\Entity\Project\CodeChange\ProjectCodeChange;
use App\Entity\Project\CodeChange\SectionCodeChange;
use App\Enum\ProposalActionStatus;
use App\Exception\ApiException;
use App\ObjectMapper\CodeBook\Action\ProposalActionToCodeBookMapper;
use App\Repository\CodeBookNodeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

use function method_exists;

class CdpService extends AbstractNodeService
{
    public function __construct(
        EntityManagerInterface                          $em,
        CodeBookNodeRepository                          $repo,
        Security                                        $security,
        private readonly ProposalActionToCodeBookMapper $actionMapper,
    ) {
        parent::__construct($em, $repo, $security);
    }

    /** @throws ApiException */
    public function create(
        Project                         $project,
        CreateFromProposalActionRequest $request
    ): AbstractCodeBookNode {
        $action = $this->em->getRepository(ProjectCodeChange::class)->find($request->id);

        if (null === $action) {
            throw new ApiException(sprintf('ProjectCodeChange with id "%s" was not found.', $request->id));
        }

        // creates codebook node
        $parentId = $request->parentId;
        if ($action instanceof SectionCodeChange) {
            $action->setBody($this->convertNumberedTablesToOrderedLists($action->getBody()));
        }
        $node = $this->actionMapper->map($action);
        $this->em->persist($node);

        $action->setCodeBookNode($node);
        $action->setStatus(ProposalActionStatus::READY_TO_INCORPORATE);

        // moves node to parent
        $this->moveToParentByAction(
            $project->getCodeBook(),
            $node,
            $parentId,
            $request->defaultNeighbor,
            $request->action
        );

        $this->em->flush();

        return $node;
    }

    /** @throws ApiException */
    public function update(
        Project                  $project,
        CdpUpdateProposalRequest $request
    ): CdpUpdateProposalResponse {
        $action = $this->em->getRepository(ProjectCodeChange::class)->find($request->id);

        if ($action === null) {
            throw new ApiException(sprintf(
                'ProjectCodeChange with id "%s" was not found.',
                $request->id
            ));
        }

        $dirty = false;

        if ($request->term !== null && $action instanceof DefinitionCodeChange) {
            $action->setTerm($request->term);
            $dirty = true;
        }
        if ($request->number !== null && method_exists($action, 'setNumber')) {
            $action->setNumber($request->number);

            $dirty = true;
        }
//        if ($request->acronym !== null && $pcc instanceof PromulgatorCodeChange) {
//            $pcc->setAcronym($request->acronym);
//            $dirty = true;
//        }

        if ($request->codesIncorporated !== null) {
            $action->setCodesIncorporated($request->codesIncorporated);
            $dirty = true;
        }
        if ($request->pubsChecked !== null) {
            $action->setPubsChecked($request->pubsChecked);
            $dirty = true;
        }

        if ($dirty) {
            $action->setNeedsEvaluation(true);
            $action->setLastEvaluatedAt(null);
        }

        $this->em->flush();

        return new CdpUpdateProposalResponse();
    }

    /** @throws ApiException */
    public function getProposalsByBookId(Project $project): CdpGetProposalsList
    {
        $sectionProposals = [];

        foreach ($project->getCodeChanges() as $codeChange) {
            $sectionProposals[] = $codeChange;
        }
        //TODO:
        // $jobDetails = $this->statusService->getDetailedJobStatus($book->getEndpoint()->getName(), JobStatusService::CYCLE_SYNC_PROPOSAL);
        //

        $volume = $project->getCodeBook()->getVolume();
        $response = new CdpGetProposalsList();
        $response->sections = $sectionProposals;
        $response->refStds = [];
        $response->lastSyncs = new CdpGetLastSync('N/A', 'N/A', null);
        return $response;
    }

    private function convertNumberedTablesToOrderedLists(string $html): string
    {
        if (trim($html) === '') {
            return $html;
        }

        $dom = new \DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        $tables = $dom->getElementsByTagName('table');

        foreach ($tables as $table) {
            if ($this->isNumberedListTable($table)) {
                $ol = $this->convertTableToOl($table);
                $table->parentNode->replaceChild($ol, $table);
            }
        }
        return $dom->saveHTML();
    }

    private function isNumberedListTable(\DOMElement $table): bool
    {
        foreach ($table->getElementsByTagName('tr') as $row) {
            $cells = $row->getElementsByTagName('td');
            if ($cells->length < 2) return false;

            $firstCell = trim($cells->item(0)->textContent);
            if (!preg_match('/^([0-9a-zA-Z]+)\.$/', $firstCell)) {
                return false;
            }
        }
        return true;
    }

    private function convertTableToOl(\DOMElement $table): \DOMElement
    {
        $dom = $table->ownerDocument;
        $ol = $dom->createElement('ol');

        foreach ($table->getElementsByTagName('tr') as $row) {
            $cells = $row->getElementsByTagName('td');
            if ($cells->length < 2) {
                continue;
            }
            $number = trim($cells->item(0)->textContent);
            $content = $dom->saveHTML($cells->item(1));

            $li = $dom->createElement('li');
            $li->appendChild($dom->createElement('label', $number));

            // Handle content with potential nested tables
            $tempDom = new \DOMDocument();
            @$tempDom->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

            $body = $tempDom->getElementsByTagName('body')->item(0);

            if ($body !== null) {
                /** @var \DOMNode $child */
                foreach ($body->childNodes as $child) {
                    $li->appendChild($dom->importNode($child, true));
                }
            } else {
                $li->appendChild($dom->createTextNode(strip_tags($content)));
            }

            $ol->appendChild($li);
        }

        return $ol;
    }
}
