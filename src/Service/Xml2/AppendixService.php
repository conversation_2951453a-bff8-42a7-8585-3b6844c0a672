<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service\Xml2;

use App\Dto\Xml2\Appendix\CreateAppendixRequest;
use App\Dto\Xml2\Appendix\UpdateAppendixRequest;
use App\Entity\CodeBook\Appendix;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\ApiException;
use App\Repository\CodeBookNodeRepository;
use App\Traits\QrCodeSetterTrait;
use App\Util\TextUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

use function ctype_alpha;
use function preg_match;
use function preg_replace;
use function strip_tags;

class AppendixService extends AbstractNodeService
{
    use QrCodeSetterTrait;

    public function __construct(
        EntityManagerInterface          $em,
        CodeBookNodeRepository          $repository,
        Security                        $security,
        private readonly SectionService $sectionService,
    ) {
        parent::__construct($em, $repository, $security);
    }

    public function list(Project $project): array
    {
        return $this->repo->findByType($project->getCodeBook(), Appendix::class);
    }

    /** @throws ApiException */
    public function create(Project $project, CreateAppendixRequest $request): Appendix
    {
        $appendix = new Appendix();
        $this->setFields($appendix, $request);
        $appendix->setNumber($request->number);
        $this->moveToParentByAction($project->getCodeBook(), $appendix, $request->parentNode, $request->neighbor, $request->action);

        $this->em->persist($appendix);
        $this->em->flush();

        return $appendix;
    }

    /**
     * @throws ApiException
     */
    public function update(Project $project, Appendix $appendix, UpdateAppendixRequest $request): Appendix
    {
        $this->setFields($appendix, $request);

        if ($request->parentNode) {
            $appendix->setRelocatedFromAttr($appendix->getNodeId());
            $this->moveToParentByAction($project->getCodeBook(), $appendix, $request->parentNode, $request->neighbor, $request->action);

            $this->sectionService->relocateAndUpdateChildren($appendix, $project->getCodeBook()->getVolume(), $request->newNumber);
        }

        $this->em->flush();

        return $appendix;
    }

    public function delete(Appendix $appendix): void
    {
        $currentUser = $this->security->getUser();
        $user = (string) $this->em->getRepository(User::class)->findOneBy(['email' => $currentUser->getEmail()]);

        $appendix->setRevisionBy($user);
        $appendix->deleteNode($user);
        $this->em->flush();
    }

    private function setFields(Appendix $appendix, UpdateAppendixRequest $request): void
    {
        $appendix->setSuperTitle($request->superTitle);
        $appendix->setCommitteeDesignation($request->committeeDesignation);
        $appendix->setLabel($request->label);
        $appendix->setNumber($request->number);
        $appendix->setCorrelated($request->correlated);
        $appendix->setTitle($request->title);
        $appendix->setSubTitle($request->subTitle);
        $appendix->setBody($request->body);
        $appendix->setHistory($request->history);
        $appendix->setObjectivesTitle($request->objectivesTitle);
        $appendix->setObjectives($request->objectives);
        $appendix->setNoteTitle($request->noteTitle);
        $appendix->setNote($request->note);
        $appendix->setAbstractTitle($request->abstractTitle);
        $appendix->setAbstract($request->abstract);
        $appendix->setKeywordsTitle($request->keywordsTitle);
        $appendix->setKeywords($request->keywords);

        // Set QrCode fields
        $this->setQrCodeFields($appendix, $request);
    }

    /** @deprecated */
    public function checkAppendixNumber($number): bool
    {
        $strippedNumber = TextUtils::trimHtml(strip_tags(preg_replace('/\([a-zA-Z]*\)/i', '', $number)));
        $validLength = preg_match('/^[a-zA-Z]{1,3}$/', $strippedNumber);

        return (!ctype_alpha($strippedNumber) || !$validLength);
    }
}
