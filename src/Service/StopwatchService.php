<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Service;

use Psr\Log\LoggerInterface;
use Symfony\Component\Stopwatch\Stopwatch;
use Symfony\Component\Stopwatch\StopwatchEvent;

class StopwatchService
{
    private Stopwatch $stopwatch;

    public function __construct(private readonly LoggerInterface $loggerStopwatch)
    {
        $this->stopwatch = new Stopwatch(true);
    }

    public function __destruct()
    {
        foreach ($this->stopwatch->getSections() as $i) {
            foreach ($i->getEvents() as $k) {
                if ($k->isStarted()) {
                    $k->stop();
                }
                $this->loggerStopwatch->debug((string) $k);
            }
        }
    }

    public function create(string $name): StopwatchEvent
    {
        $this->stopwatch->start($name);
        return $this->stopwatch->stop($name);
    }
}
