<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\EventListener\CodeBook\CreateCodeBookEvents;

use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Section;
use App\Entity\ProjectType;
use App\Event\CodeBook\CreateCodeBookEvent;
use App\Service\StopwatchService;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\Stopwatch\StopwatchEvent;

#[AsEventListener(event: CreateCodeBookEvent::class)]
class RemoveErrataListener
{
    private StopwatchEvent $stopwatch;

    public function __construct(StopwatchService $stopwatch)
    {
        $this->stopwatch = $stopwatch->create('RemoveErrataListener');
    }

    public function __invoke(CreateCodeBookEvent $event): void
    {
        $this->stopwatch->start();

        if ($event->getProject()->getProjectType()->getName() === ProjectType::PROJECT_TYPE_FIRST_PRINTING) {
            foreach ($event->getCodeBook() as $node) {
                if (($node instanceof Section || $node instanceof Chapter) && $node->getRole() === 'errata') {
                    $node->getParent()->removeChild($node);
                }
            }
        }

        $this->stopwatch->stop();
    }
}
