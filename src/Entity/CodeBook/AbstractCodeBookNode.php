<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Entity\CodeBook;

use App\Entity\CommonBook\Attribute\CommonAttributes;
use App\Entity\CommonBook\Field\QrCode;
use App\Entity\IdTrait;
use App\Entity\Project\CodeChange\ProjectCodeChange;
use App\Enum\Status;
use App\Exception\CodeBookException;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Gedmo\Tree\Entity\Repository\MaterializedPathRepository;
use IteratorAggregate;
use Symfony\Component\Serializer\Annotation as Serializer;
use Symfony\Component\Validator\Constraints as Assert;
use Traversable;
use Ulid\Ulid;

use function get_parent_class;
use function str_starts_with;

#[Gedmo\Tree(type: 'materializedPath')]
#[ORM\Entity(repositoryClass: MaterializedPathRepository::class)]
#[ORM\Table(name: 'code_book_nodes')]
#[ORM\Index(columns: ['node_id'], name: 'code_book_node_idx')]
#[ORM\Index(columns: ['path_id'], name: 'code_book_path_idx')]
#[ORM\Index(columns: ['node_id_backup'], name: 'code_book_node_backup_idx')]
#[ORM\InheritanceType('JOINED')]
#[ORM\DiscriminatorColumn(name: 'dataType', type: 'string', length: 30)]
#[ORM\DiscriminatorMap([
    'appendix'            => Appendix::class,
    'backMatter'          => BackMatter::class,
    'chapter'             => Chapter::class,
    'copyrightPage'       => CopyrightPage::class,
    'definition'          => Definition::class,
    'definitionList'      => DefinitionList::class,
    'equation'            => Equation::class,
    'figure'              => Figure::class,
    'foreword'            => Foreword::class,
    'frontMatter'         => FrontMatter::class,
    'index'               => Index::class,
    'indexDivision'       => IndexDivision::class,
    'indexEntry'          => IndexEntry::class,
    'secondaryIndexEntry' => SecondaryIndexEntry::class,
    'tertiaryIndexEntry'  => TertiaryIndexEntry::class,
    'preface'             => Preface::class,
    'promulgator'         => Promulgator::class,
    'publication'         => Publication::class,
    'publisherNote'       => PublisherNote::class,
    'referenceStandard'   => ReferenceStandard::class,
    'relocatedFrom'       => RelocatedFrom::class,
    'relocatedTo'         => RelocatedTo::class,
    'section'             => Section::class,
    'table'               => Table::class,
    'titlePage'           => TitlePage::class,
    'volume'              => Volume::class,
])]
abstract class AbstractCodeBookNode implements IteratorAggregate
{
    use IdTrait;
    use CommonAttributes;
    use Field\InternalNotes;
    use QrCode;

    /** @var Collection<ProjectCodeChange> */
    #[ORM\OneToMany(targetEntity: ProjectCodeChange::class, mappedBy: 'codeBookNode')]
    private Collection $codeChanges;

    #[ORM\ManyToOne(targetEntity: Publication::class, inversedBy: 'nodes')]
    #[ORM\JoinColumn(name: 'publication_id', referencedColumnName: 'id', onDelete: 'SET NULL', nullable: true)]
    #[Serializer\Ignore]
    protected ?Publication $publication = null;

    #[ORM\OneToMany(mappedBy: 'fromNode', targetEntity: XRefLink::class, cascade: ['persist', 'remove'])]
    private Collection $xrefLinks;

    #[ORM\OneToMany(mappedBy: 'toNode', targetEntity: XRefLink::class, cascade: ['persist'])]
    private Collection $xrefLinkedBy;

    #[ORM\OneToMany(mappedBy: 'codeBookNode', targetEntity: UrlLink::class, cascade: ['persist', 'remove'])]
    private Collection $urlLinks;

    #[ORM\OneToMany(mappedBy: 'fromNode', targetEntity: PublicationRefLink::class, cascade: ['persist', 'remove'])]
    private Collection $publicationRefLinks;

    #[ORM\Column(type: 'string', length: 36, unique: true)]
    protected string $ulid = '';

    #[ORM\Column(type: 'string', length: 700)]
    protected string $nodeId = '';

    #[ORM\Column(type: 'string', length: 700)]
    protected string $nodeIdBackup = '';

    #[Gedmo\TreePathSource]
    #[ORM\Column(type: 'string', length: 512)]
    protected string $pathId = '';

    #[Gedmo\TreePath(separator: ",", appendId: true, endsWithSeparator: false)]
    #[ORM\Column(type: 'string', length: 3000, nullable: true)]
    protected ?string $path = null;

    #[Gedmo\TreeLevel]
    #[ORM\Column(type: 'integer', nullable: true)]
    protected int $pathLevel = 0;

    #[Gedmo\TreeParent]
    #[ORM\ManyToOne(targetEntity: AbstractCodeBookNode::class, inversedBy: 'children')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    #[Serializer\Ignore]
    protected ?AbstractCodeBookNode $parent = null;

    /** @var Collection<AbstractCodeBookNode>|null */
    #[ORM\OneToMany(targetEntity: AbstractCodeBookNode::class, mappedBy: 'parent', cascade: ['all'])]
    #[ORM\OrderBy(['position' => 'ASC'])]
    protected ?Collection $children = null;

    #[ORM\Column(type: 'integer')]
    protected int $position = -1;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    protected ?string $deletedBy = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    protected ?DateTime $deletedDate = null;

    #[Assert\Choice(choices: Status::CASES, message: 'Invalid status.')]
    #[ORM\Column(type: 'string', length: 25)]
    protected string $status = Status::IN_PROGRESS;

    #[ORM\Column(type: 'datetime', nullable: true)]
    protected ?DateTime $createdAt = null;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $modified = false;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    protected bool $showDeletionMarker = false;

    public static array $dataTypeMap = [
        Appendix::class            => 'appendix',
        BackMatter::class          => 'backMatter',
        Chapter::class             => 'chapter',
        CopyrightPage::class       => 'copyrightPage',
        Definition::class          => 'definition',
        DefinitionList::class      => 'definitionList',
        Equation::class            => 'equation',
        Figure::class              => 'figure',
        Foreword::class            => 'foreword',
        FrontMatter::class         => 'frontMatter',
        Index::class               => 'index',
        IndexDivision::class       => 'indexDivision',
        IndexEntry::class          => 'indexEntry',
        SecondaryIndexEntry::class => 'secondaryIndexEntry',
        TertiaryIndexEntry::class  => 'tertiaryIndexEntry',
        Preface::class             => 'preface',
        Promulgator::class         => 'promulgator',
        Publication::class         => 'publication',
        PublisherNote::class       => 'publisherNote',
        ReferenceStandard::class   => 'referenceStandard',
        RelocatedFrom::class       => 'relocatedFrom',
        RelocatedTo::class         => 'relocatedTo',
        Section::class             => 'section',
        Table::class               => 'table',
        TitlePage::class           => 'titlePage',
        Volume::class              => 'volume',
    ];

    public function __construct()
    {
        $this->ulid = (string) Ulid::generate();
        $this->nodeId = $this->ulid;
        $this->children = new ArrayCollection();
        $this->codeChanges = new ArrayCollection();
        $this->xrefLinks = new ArrayCollection();
        $this->xrefLinkedBy = new ArrayCollection();
        $this->urlLinks = new ArrayCollection();
        $this->publicationRefLinks = new ArrayCollection();
    }

    /** @return Traversable<AbstractCodeBookNode> */
    public function getIterator(): Traversable
    {
        yield $this;
        foreach ($this->children as $i) {
            yield from $i->getIterator();
        }
    }

    public function getUlid(): string
    {
        return $this->ulid;
    }

    /**
     * warning: only used for testing
     */
    public function setUlid(string $ulid): void
    {
        $this->ulid = $ulid;
    }

    public function getPublication(): ?Publication
    {
        return $this->publication;
    }

    public function setPublication(?Publication $publication): void
    {
        $this->publication = $publication;
    }

    public function addXRefLink(XRefLink $link): void
    {
        if (!$this->xrefLinks->contains($link)) {
            $this->xrefLinks->add($link);
            $link->setFromNode($this);
        }
    }

    /** @return XRefLink[] */
    public function getXRefLinks(): array
    {
        return $this->xrefLinks->toArray();
    }

    public function removeXRefLink(XRefLink $link): void
    {
        if ($this->xrefLinks->contains($link)) {
            $this->xrefLinks->removeElement($link);
            $link->setFromNode(null);
        }
    }

    /** @return XRefLink[] */
    public function getXRefLinkedBy(): array
    {
        return $this->xrefLinkedBy->toArray();
    }

    public function addUrlLink(UrlLink $link): void
    {
        if (!$this->urlLinks->contains($link)) {
            $this->urlLinks->add($link);
            $link->setCodeBookNode($this);
        }
    }

    /** @return UrlLink[] */
    public function getUrlLinks(): array
    {
        return $this->urlLinks->toArray();
    }

    public function removeUrlLink(UrlLink $link): void
    {
        if ($this->urlLinks->contains($link)) {
            $this->urlLinks->removeElement($link);
            $link->setCodeBookNode(null);
        }
    }

    public function addPublicationRefLink(PublicationRefLink $link): void
    {
        if (!$this->publicationRefLinks->contains($link)) {
            $this->publicationRefLinks->add($link);
            $link->setFromNode($this);
        }
    }

    /** @return PublicationRefLink[] */
    public function getPublicationRefLinks(): array
    {
        return $this->publicationRefLinks->toArray();
    }

    public function removePublicationRefLink(PublicationRefLink $link): void
    {
        if ($this->publicationRefLinks->contains($link)) {
            $this->publicationRefLinks->removeElement($link);
            $link->setFromNode(null);
        }
    }

    public function getDataType(): string
    {
        $className = str_starts_with(static::class, 'Proxies\__CG__\\')
            ? get_parent_class($this)
            : static::class;

        return self::$dataTypeMap[$className] ?? 'unknown';
    }

    public function getNodeId(): string
    {
        return $this->nodeId;
    }

    public function setNodeId(string $nodeId): void
    {
        $this->nodeId = $nodeId;
    }

    public function getOriginalNodeId(): string
    {
        return $this->nodeIdBackup;
    }

    public function setOriginalNodeId(string $nodeId): void
    {
        $this->nodeIdBackup = $nodeId;
    }

    public function getPathId(): string
    {
        return $this->pathId;
    }

    public function setPathId(string $pathId): void
    {
        $this->pathId = $pathId;
    }

    public function getPath(): string
    {
        return $this->path ?? '';
    }

    public function setPath(string $path): void
    {
        $this->path = $path;
    }

    public function getPathLevel(): int
    {
        return $this->pathLevel;
    }

    public function getParent(): ?AbstractCodeBookNode
    {
        return $this->parent;
    }

    public function setParent(?AbstractCodeBookNode $parent = null): void
    {
        $this->parent = $parent;
    }

    public function addChild(AbstractCodeBookNode $child): void
    {
        if (!$this->children->contains($child)) {
            $this->children->add($child);
            $child->setPosition($this->children->count() - 1);
        }
        $child->setParent($this);
    }

    /** @return AbstractCodeBookNode[] */
    public function getChildren(bool $withDeleted = true): array
    {
        return $withDeleted
            ? $this->children->toArray()
            : $this->children->filter(fn(AbstractCodeBookNode $child) => !$child->isDeleted())->toArray();
    }

    /** @param AbstractCodeBookNode[] $children */
    public function setChildren(array $children): void
    {
        foreach ($this->getChildren() as $i) {
            $this->removeChild($i);
        }
        $this->children->clear();
        foreach ($children as $i) {
            $this->addChild($i);
        }
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function isDeleted(): bool
    {
        return $this->deletedDate !== null;
    }

    /**
     * @return AbstractCodeBookNode[]
     */
    public function getNodesByClass(string $class): array
    {
        $result = new ArrayCollection();

        if ($this instanceof $class) {
            $result->add($this);
        }

        $this->getNodesByClassRecursive($class, $this->children, $result);

        return $result->toArray();
    }

    public function getNodesByClassRecursive(string $class, iterable $nodes, ArrayCollection $result): void
    {
        foreach ($nodes as $node) {
            if ($node instanceof $class && !$result->contains($node)) {
                $result->add($node);
            }

            $this->getNodesByClassRecursive($class, $node->children, $result);
        }
    }

    public function getNodesList(): array
    {
        $processedSections = [];

        foreach ($this->children as $child) {
            if (
                $child instanceof Section ||
                $child instanceof Chapter ||
                $child instanceof Equation ||
                $child instanceof Figure ||
                $child instanceof Table ||
                $child instanceof Appendix ||
                $child instanceof Definition ||
                $child instanceof ReferenceStandard
            ) {
                $processedSections[] = $child;
            }

            if ($child instanceof AbstractCodeBookNode) {
                foreach ($child->getNodesList() as $nestedChild) {
                    $processedSections[] = $nestedChild;
                }
            }
        }

        return $processedSections;
    }

    public function getNodeById(string $nodeId): ?AbstractCodeBookNode
    {
        if (empty($nodeId)) {
            return null;
        }
        return $this->getNodeByIdRecursive($nodeId, [$this]);
    }

    public function getNodeByIdRecursive(string $nodeId, iterable $nodes): ?AbstractCodeBookNode
    {
        /** @var AbstractCodeBookNode $node */
        foreach ($nodes as $node) {
            if ($nodeId === $node->getNodeId()) {
                return $node;
            } elseif ($found = $this->getNodeByIdRecursive($nodeId, $node->getChildren())) {
                return $found;
            }
        }

        return null;
    }

    public function appendChild(AbstractCodeBookNode $node): AbstractCodeBookNode
    {
        if (null !== $node->getParent()) {
            $node->getParent()->removeChild($node);
        }

        $this->children->add($node);
        $node->setParent($this);
        $node->setPosition($this->children->count());

        return $node;
    }

    public function insertBefore(AbstractCodeBookNode $node, ?AbstractCodeBookNode $child = null): AbstractCodeBookNode
    {
        return $this->insertNode($node, $child);
    }

    public function insertAfter(AbstractCodeBookNode $node, ?AbstractCodeBookNode $child = null): AbstractCodeBookNode
    {
        return $this->insertNode($node, $child, false);
    }

    public function insertNode(
        AbstractCodeBookNode  $node,
        ?AbstractCodeBookNode $child = null,
        bool                  $before = true
    ): AbstractCodeBookNode {
        if (null === $child) {
            return $this->appendChild($node);
        }

        if (!$this->children->contains($child)) {
            throw new CodeBookException(CodeBookException::NOT_FOUND);
        }

        if (null !== $node->getParent()) {
            $node->getParent()->removeChild($node);
        }

        $children = $this->children->toArray();
        $position = 0;
        $this->children->clear();

        foreach ($children as $i) {
            if ($before && $i === $child) {
                $this->children->add($node);
                $node->setParent($this);
                $node->setPosition($position);
                $position++;
            }

            $this->children->add($i);
            $i->setParent($this);
            $i->setPosition($position);
            $position++;

            if (!$before && $i === $child) {
                $this->children->add($node);
                $node->setParent($this);
                $node->setPosition($position);
                $position++;
            }
        }

        return $node;
    }

    public function removeChild(AbstractCodeBookNode $child): void
    {
        if (false === ($this->children->contains($child) || $child->getParent() === $this)) {
            throw new CodeBookException(CodeBookException::NOT_FOUND);
        }

        $this->children->removeElement($child);
        $child->setParent(null);

        $position = 0;
        foreach ($this->children as $i) {
            $i->setPosition($position);
            $position++;
        }
    }

    public function findParentChapterOrAppendix(): ?AbstractCodeBookNode
    {
        $currentNode = $this->getParent();

        while ($currentNode !== null) {
            if ($currentNode instanceof Chapter || $currentNode instanceof Appendix) {
                return $currentNode;
            }

            $currentNode = $currentNode->getParent();
        }

        return null;
    }

    public function replaceChild(AbstractCodeBookNode $node, AbstractCodeBookNode $child): AbstractCodeBookNode
    {
        if (!$this->children->contains($child)) {
            throw new CodeBookException(CodeBookException::NOT_FOUND);
        }

        $this->insertBefore($node, $child);
        $this->removeChild($child);

        return $child;
    }

    public function deleteNode(string $user, $showDeletionMarker = false): void
    {
        $this->setDeletedBy($user);
        $this->setDeletedDate(new DateTime());
        $this->setShowDeletionMarker($showDeletionMarker);

        foreach ($this->children as $child) {
            $this->markChildrenAsDeleted($child, $user);
        }
    }

    public function getChapterParent(): AbstractCodeBookNode
    {
        $current = $this->getParent();

        $chapterTypes = [
            Chapter::class,
            Appendix::class,
            Index::class,
            Preface::class,
            TitlePage::class,
            CopyrightPage::class,
            PublisherNote::class,
        ];

        while ($current !== null) {
            foreach ($chapterTypes as $type) {
                if ($current instanceof $type) {
                    return $current;
                }
            }
            $current = $current->getParent();
        }

        return $this;
    }

    public function markChildrenAsDeleted(AbstractCodeBookNode $node, string $user): void
    {
        $node->setDeletedBy($user);
        $node->setDeletedDate(new DateTime());

        foreach ($node->children as $child) {
            $this->markChildrenAsDeleted($child, $user);
        }
    }

    public function firstDefinitionList(AbstractCodeBookNode $parent): ?DefinitionList
    {
        foreach ($parent->getChildren(false) as $child) {
            if ($child instanceof DefinitionList) {
                return $child;
            }
        }
        return null;
    }

    public function getDeletedBy(): string
    {
        return $this->deletedBy ?? '';
    }

    public function setDeletedBy(?string $username)
    {
        $this->deletedBy = $username;
        return $this;
    }

    public function getDeletedDate(): ?DateTime
    {
        return $this->deletedDate;
    }

    public function setDeletedDate(?DateTime $value): self
    {
        $this->deletedDate = $value;
        return $this;
    }

    public function getCreatedAt(): ?DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?DateTime $value): self
    {
        $this->createdAt = $value;
        return $this;
    }

    public function isModified(): bool
    {
        return $this->modified;
    }

    public function setModified(bool $modified): self
    {
        $this->modified = $modified;
        return $this;
    }

    public function getShowDeletionMarker(): bool
    {
        return $this->showDeletionMarker;
    }

    public function setShowDeletionMarker(bool $showDeletionMarker): self
    {
        $this->showDeletionMarker = $showDeletionMarker;
        return $this;
    }

    /**
     * @return Collection|ProjectCodeChange[]
     */
    public function getCodeChanges(): Collection
    {
        return $this->codeChanges;
    }
}
