<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Command\Project;

use App\DataFixtures\Project\ProjectTypeFixture;
use App\DataFixtures\Project\VersionTypeFixture;
use App\Dto\Xml2\Project\CreateProjectRequest;
use App\Entity\ProjectType;
use App\Entity\VersionType;
use App\Message\Project\CreateProjectMessage;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

use function mb_strtolower;

#[AsCommand(name: 'app:project:create', description: 'Create a new XML2 Project')]
class CreateProjectCommand extends Command
{
    public function __construct(
        private readonly MessageBusInterface    $bus,
        private readonly EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('shortCode', InputArgument::REQUIRED, 'Unique ID for new book')
            ->addArgument('baseBook', InputArgument::REQUIRED, 'Unique ID of base book')
            ->addOption('name', null, InputOption::VALUE_OPTIONAL, 'Working title for the new book')
            ->addOption('title', 't', InputOption::VALUE_OPTIONAL, 'Title for the new book')
            ->addOption('projectType', 'l', InputOption::VALUE_OPTIONAL, 'Project type (0, 1, 2, 3, subsequent)')
            ->addOption('versionType', 'p', InputOption::VALUE_OPTIONAL, 'Version type (1, 2, 3, supplement, errata');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Creating project: ' . $input->getOption('name'));

        $request = new CreateProjectRequest();
        $request->baseBook = $input->getArgument('baseBook');
        $request->shortCode = $input->getArgument('shortCode');
        $request->bookTitle = $input->getOption('name') ?? '';
        $request->workingTitle = $request->bookTitle;
        $request->category = 'Test Books';
        $request->commentaryEnabled = false;
        $request->hasCdpAccess = false;

        $projectTypeInput = mb_strtolower($input->getOption('projectType') ?? '');
        $projectTypeRef = 'firstPrinting';
        if ('1' === $projectTypeInput) {
            $projectTypeRef = 'level1';
        } elseif ('2' === $projectTypeInput) {
            $projectTypeRef = 'level2';
        } elseif ('3' === $projectTypeInput) {
            $projectTypeRef = 'level3';
        } elseif ('s' === $projectTypeInput) {
            $projectTypeRef = 'subsequent';
        }

        $projectTypeRepo = $this->em->getRepository(ProjectType::class);
        $projectType = $projectTypeRepo->findOneBy([
            'name' => ProjectTypeFixture::$types[$projectTypeRef]['name'],
        ]);
        if ($projectType) {
            $request->projectType = $projectType->getId();
        } else {
            $output->writeln('Unable to set Project Type');
            return Command::FAILURE;
        }

        $versionTypeInput = mb_strtolower($input->getOption('versionType') ?? '');
        $versionTypeRef = 'firstPrinting';
        if ('errata' === $versionTypeInput) {
            $versionTypeRef = 'errata';
        } elseif ('supplement' === $versionTypeInput) {
            $versionTypeRef = 'supplement';
        } elseif ('2' === $versionTypeInput) {
            $versionTypeRef = 'secondPrinting';
        } elseif ('3' === $versionTypeInput) {
            $versionTypeRef = 'thirdPrinting';
        }

        $versionTypeRepo = $this->em->getRepository(VersionType::class);
        $versionType = $versionTypeRepo->findOneBy([
            'name' => VersionTypeFixture::$types[$versionTypeRef]['name'],
        ]);
        if ($versionType) {
            $request->versionType = $versionType->getId();
        } else {
            $output->writeln('Unable to set Version Type');
            return Command::FAILURE;
        }

        $message = new CreateProjectMessage($request);
        $this->bus->dispatch($message);

        $output->writeln('<info>Project created!</info>');
        $output->writeln('Project ID: ' . $message->getProject()->getId());

        return Command::SUCCESS;
    }
}
