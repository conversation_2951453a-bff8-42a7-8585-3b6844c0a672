<?php

namespace App\Traits;

use Symfony\Component\HttpFoundation\Response;

trait CsvExportTrait
{
    public function convertArrayToCsv(array $csvData): string|Response
    {
        if (empty($csvData)) {
            return new Response('No data to export to CSV');
        }

        // Assume the first row contains the headers
        $headers = $csvData[0];
        array_shift($csvData);

        $stream = fopen('php://temp', 'r+');
        fputcsv($stream, $headers, escape: "\\");

        foreach ($csvData as $row) {
            fputcsv($stream, $row, escape: "\\");
        }

        rewind($stream);
        $csv = stream_get_contents($stream);
        fclose($stream);

        return $csv;
    }
}
