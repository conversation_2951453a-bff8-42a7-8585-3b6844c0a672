<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Traits;

use App\Enum\NotesIncluded;
use App\Helper\Xml2Helper;
use App\Repository\NoteAttachmentRepository;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Service\Attribute\Required;

use function method_exists;
use function sprintf;
use function trim;
use function htmlspecialchars;

trait NotesIncludedTrait
{
    protected NoteAttachmentRepository $noteAttachmentRepository;
    protected UrlGeneratorInterface $myUrlGenerator;

    #[Required]
    public function setNoteAttachmentRepository(NoteAttachmentRepository $repository): void
    {
        $this->noteAttachmentRepository = $repository;
    }

    #[Required]
    public function setUrlGenerator(UrlGeneratorInterface $urlGenerator): void
    {
        $this->myUrlGenerator = $urlGenerator;
    }

    protected function appendInternalNotesToBody(string $body, $object, string $notesType = 'all'): string
    {
        $notesIncluded = NotesIncluded::tryFrom($notesType) ?? NotesIncluded::NONE;

        if (!$notesIncluded->includesAnyNotes()) {
            return $body;
        }

        $notesHtml = '';

        if ($notesIncluded->includesCodesNotes() && method_exists($object, 'getCodesNotes') && !empty($object->getCodesNotes())) {
            $notesHtml .= $this->formatNotesIncluded('[ CODES TO CODES ]', $object->getCodesNotes());
            $notesHtml .= $this->generateAttachmentTable($object, 'internal');
        }

        if ($notesIncluded->includesPubsNotes() && method_exists($object, 'getPubsNotes') && !empty($object->getPubsNotes())) {
            $notesHtml .= $this->formatNotesIncluded('[ CODES TO PUBS ]', $object->getPubsNotes());
            $notesHtml .= $this->generateAttachmentTable($object, 'pubs');
        }

        if ($notesIncluded->includesTypesetterNotes() && method_exists($object, 'getTypesetterNotes') && !empty($object->getTypesetterNotes())) {
            $notesHtml .= $this->formatNotesIncluded('Typesetter Notes', $object->getTypesetterNotes());
            $notesHtml .= $this->generateAttachmentTable($object, 'typesetter');
        }

        if (!empty($notesHtml)) {
            $body = '<div class="internal-notes">' . $notesHtml . '</div>' . $body;
        }

        return $body;
    }

    protected function generateAttachmentTable($object, string $noteType): string
    {
        $attachments = $this->noteAttachmentRepository->findByNodeAndType($object, $noteType);
        if (!$attachments) {
            return '';
        }

        $tableHtml = '<table border="1" style="margin-top:10px;border-collapse:collapse;width:100%;">';

        $tableHtml .= '<colgroup>
        <col style="width:20%"/>
        <col style="width:12%"/>
        <col style="width:28%"/>
        <col style="width:15%"/>
        <col style="width:13%"/>
        <col style="width:12%"/>
        </colgroup>';

        $tableHtml .= '<thead>
        <tr style="background-color:#f5f5f5;">
            <th style="padding:8px;text-align:left;">Section</th>
            <th style="padding:8px;text-align:left;">Note Type</th>
            <th style="padding:8px;text-align:left;">File Name</th>
            <th style="padding:8px;text-align:left;">User</th>
            <th style="padding:8px;text-align:left;">Date Uploaded</th>
            <th style="padding:8px;text-align:left;">URL</th>
        </tr>
    </thead>
    <tbody>';

        foreach ($attachments as $attachment) {
            $section   = htmlspecialchars((string) $attachment->getSectionId());
            $note      = htmlspecialchars((string) $attachment->getNoteType());
            $filename  = htmlspecialchars((string) $attachment->getFilename());
            $uploadedBy = htmlspecialchars((string) $attachment->getUploadedBy());
            $uploadedAt = $attachment->getUploadedAt() ? $attachment->getUploadedAt()->format('m/d/Y') : '';
            $uploadedAt = htmlspecialchars($uploadedAt);

            $url = '';
            if ($attachment->getFile()) {
                $url = $this->myUrlGenerator->generate(
                    'app_note_attachment',
                    ['hash' => $attachment->getFile()->getHash()],
                    UrlGeneratorInterface::ABSOLUTE_URL
                );
            }
            $urlEsc = htmlspecialchars($url);

            $tableHtml .= '<tr>
            <td style="padding:8px;">' . $section    . '</td>
            <td style="padding:8px;">' . $note       . '</td>
            <td style="padding:8px;">' . $filename   . '</td>
            <td style="padding:8px;">' . $uploadedBy . '</td>
            <td style="padding:8px;">' . $uploadedAt . '</td>
            <td style="padding:8px;">' . ($url ? '<a href="' . $urlEsc . '" target="_blank" rel="noopener">' . $urlEsc . '</a>' : '') . '</td>
        </tr>';
        }

        $tableHtml .= '</tbody></table>';

        return $tableHtml;
    }

    protected function formatNotesIncluded(string $title, string $content): string
    {
        $escapedTitle = htmlspecialchars($title, ENT_QUOTES, 'UTF-8');
        $processedContent = $this->processNoteContent($content);

        return sprintf(
        '<div class="internal-note" style="margin:10px 0;padding:6px 0 6px 12px;border-left:4px solid #d1d5db;">
          <h4 class="note-title" style="margin:0 0 4px 0;font-size:1rem;font-weight:600;letter-spacing:.02em;color:#9b3d3d;">%s</h4>
          <div class="note-content" style="color:#9b3d3d;font-size:.95rem;line-height:1.35rem;">%s</div>
         </div>',
            $escapedTitle,
            $processedContent
        );
    }

    protected function processNoteContent(string $content): string
    {
        if (empty(trim($content))) {
            return '';
        }

        try {
            $wrappedContent = sprintf('<wrapper>%s</wrapper>', $content);
            $document = Xml2Helper::createDOMDocument($wrappedContent, false);
            $wrapper = $document->documentElement;
            if ($wrapper) {
                $innerXml = Xml2Helper::getInnerXml($wrapper);
                return Xml2Helper::stripDeclaration($innerXml);
            }

            return htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
        } catch (\Throwable $e) {
            return nl2br(htmlspecialchars($content, ENT_QUOTES, 'UTF-8'));
        }
    }
}
