<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\EventSubscriber;

use App\Exception\XmlValidationException;
use App\Service\Xml2\XmlValidationErrorFormatter;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response as HttpResponse;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelEvents;

use function str_starts_with;

#[AsEventListener(event: KernelEvents::EXCEPTION)]
class ApiExceptionListener
{
    private LoggerInterface $logger;
    private string $kernelEnv;
    private XmlValidationErrorFormatter $xmlValidationErrorFormatter;

    public function __construct(LoggerInterface $logger, string $kernelEnv, XmlValidationErrorFormatter $xmlValidationErrorFormatter)
    {
        $this->logger = $logger;
        $this->kernelEnv = $kernelEnv;
        $this->xmlValidationErrorFormatter = $xmlValidationErrorFormatter;
    }

    public function __invoke(ExceptionEvent $event): void
    {
        $request = $event->getRequest();
        $uri = $request->getRequestUri();

        if (!str_starts_with($uri, '/api')) {
            return;
        }

        if ($uri === '/api/doc') {
            return;
        }

        $exception = $event->getThrowable();
        $statusCode = (!$exception->getCode() || $exception->getCode() === 0)
            ? 199 :
            $exception->getCode();

        if ($statusCode <= 199) {
            $this->logger->warning($exception->getMessage());
            if ($statusCode <= 100) {
                $statusCode = 400;
            }
        } elseif ($statusCode < 100 || $statusCode > 599) {
            $this->logger->error('Exception produced invalid HTTP status code.', [
                'exception_class' => $exception::class,
                'provided_code'   => $statusCode,
            ]);
            $statusCode = HttpResponse::HTTP_INTERNAL_SERVER_ERROR;
        } else {
            $this->logger->error($exception->getMessage());
        }

        $data = [
            'message' => $exception->getMessage(),
            'code'    => $statusCode,
        ];

        if ($exception instanceof XmlValidationException) {
            $data['issues'] = $this->xmlValidationErrorFormatter->normalize($exception->getIssues());
        }

        if ($this->kernelEnv === 'dev') {
            $data['trace'] = $exception->getTrace();
        }

        $event->setResponse(new JsonResponse($data, $statusCode));
    }
}
