<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api\Report;

use App\Dto\Project\Report\CreateExportStatusResponse;
use App\Dto\Project\Report\Xml\CreateXmlExportRequest;
use App\Dto\Project\Report\Xml\CreateXmlExportResponse;
use App\Dto\Xml2\Validation\XmlValidationResult;
use App\Entity\Project;
use App\Entity\User\User;
use App\Exception\XmlValidationException;
use App\Message\Project\BookXMLExportMessage;
use App\Service\JobStatusService;
use App\Service\Xml2\ReportService;
use App\Service\Xml2\XmlValidationService;
use Doctrine\ORM\EntityManagerInterface;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

use function str_replace;
use function trim;

#[Route(path: '/api/v2/reports/{shortCode}/export-xml')]
#[OA\Tag(name: 'Report Queue')]
#[Nelmio\Security(name: 'Bearer')]
class XmlExportController extends AbstractController
{
    public function __construct(
        private readonly MessageBusInterface    $messageBus,
        private readonly ReportService          $reportService,
        private readonly EntityManagerInterface $entityManager,
        private readonly XmlValidationService   $xmlValidationService,
    ) {
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[Route(path: '', name: 'api_xml2_export_xml', methods: ['GET'])]
    #[OA\Get(
        summary: 'Request an XML Export',
        parameters: [
            new OA\Parameter(
                name: 'clean',
                description: 'Redline',
                in: 'query',
                required: false,
                schema: new OA\Schema(type: 'boolean')
            ),
        ],
        responses: [
            new OA\Response(
                response: 201,
                description: 'Created',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateXmlExportResponse::class))
            ),
        ]
    )]
    public function export(
        Project                $project,
        CreateXmlExportRequest $request,
    ): JsonResponse {
        $user = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getEmail()]);
        $username = trim(str_replace(' ', '_', (string) $user));

        $message = new BookXMLExportMessage($project, $username, $request->clean);
        $this->messageBus->dispatch($message);

        return $this->json(new CreateXmlExportResponse(), Response::HTTP_CREATED);
    }

    #[Route(path: '/status', name: 'api_xml2_export_xml_status', methods: ['GET'])]
    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Get XML Export status',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateExportStatusResponse::class))
            ),
        ]
    )]
    public function status(
        Project $project,
    ): JsonResponse {
        $data = $this->reportService->getStatus(
            $project->getShortCode(),
            JobStatusService::BOOK_XML_EXPORT_TYPE,
            'book-export-xml2-clean',
            'book-export-xml2');

        return $this->json(CreateExportStatusResponse::createFromResponse($data), Response::HTTP_OK);
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[Route(path: '/validate', name: 'api_xml2_export_xml_validate', methods: ['GET'])]
    #[OA\Get(
        summary: 'Run full XML validation for a book',
        responses: [
            new OA\Response(
                response: 200,
                description: 'Validation passed',
                content: new OA\JsonContent(type: 'object', properties: [
                    new OA\Property(property: 'message', type: 'string'),
                    new OA\Property(
                        property: 'issues',
                        type: 'array',
                        items: new OA\Items(ref: new Nelmio\Model(type: XmlValidationResult::class))
                    ),
                ])
            ),
            new OA\Response(
                response: 400,
                description: 'Malformed XML detected',
                content: new OA\JsonContent(type: 'object', properties: [
                    new OA\Property(property: 'message', type: 'string'),
                    new OA\Property(property: 'code', type: 'integer'),
                    new OA\Property(
                        property: 'issues',
                        type: 'array',
                        items: new OA\Items(ref: new Nelmio\Model(type: XmlValidationResult::class))
                    ),
                ])
            ),
        ]
    )]
    public function validate(
        Project $project,
    ): JsonResponse {
        $issues = $this->xmlValidationService->validateProject($project);
        if ($issues === []) {
            return $this->json([
                'message' => 'XML validation completed successfully.',
                'issues'  => [],
            ], Response::HTTP_OK);
        }

        throw new XmlValidationException(
            $issues,
            'Malformed XML detected during validation. Please resolve validation errors and try again.'
        );
    }
}
