<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Controller\Api;

use App\Dto\ErrorResponseDto;
use App\Dto\Xml2\Appendix\CreateAppendixRequest;
use App\Dto\Xml2\Appendix\UpdateAppendixRequest;
use App\Entity\CodeBook\Appendix;
use App\Entity\Project;
use App\Serializer\Dto\Book\AppendixDto;
use App\Service\Xml2\AppendixService;
use Nelmio\ApiDocBundle\Attribute as Nelmio;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route(path: '/api/v2/books/{shortCode}', format: 'json')]
#[OA\Tag(name: 'Appendix')]
#[Nelmio\Security(name: 'Bearer')]
#[OA\Parameter(
    name: 'shortCode',
    description: 'The ID of the project.',
    in: 'path',
    required: true,
    schema: new OA\Schema(type: 'string')
)]
#[OA\Response(response: 401, description: 'Unauthorized')]
class AppendixController extends AbstractController
{
    public function __construct(private readonly AppendixService $appendixService)
    {
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Retrieves a list of all appendices.',
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    type: 'array',
                    items: new OA\Items(ref: new Nelmio\Model(type: AppendixDto::class))
                )
            ),
        ]
    )]
    #[Route(path: '/appendices', methods: ['GET'])]
    #[Route(path: '/appendix', methods: ['GET'])]
    public function index(Project $project): JsonResponse
    {
        $list = $this->appendixService->list($project);
        return $this->json($list, Response::HTTP_OK);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Post(
        summary: 'Create a new Appendix',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: CreateAppendixRequest::class))
        ),
        responses: [
            new OA\Response(
                response: Response::HTTP_CREATED,
                description: 'CREATED',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: AppendixDto::class))
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
        ]
    )]
    #[Route(path: '/appendices', methods: ['POST'])]
    #[Route(path: '/appendix', methods: ['POST'])]
    public function create(Project $project, #[MapRequestPayload] CreateAppendixRequest $request): JsonResponse
    {
        $appendix = $this->appendixService->create($project, $request);
        return $this->json($appendix, Response::HTTP_CREATED);
    }

    #[IsGranted('VIEW_PROJECT', 'project')]
    #[OA\Get(
        summary: 'Retrieves details of a specific Appendix.',
        parameters: [
            new OA\Parameter(
                name: 'nodeId',
                description: 'The ID of the Appendix',
                in: 'path',
                required: true
            ),
        ],
        responses: [
            new OA\Response(
                response: Response::HTTP_OK,
                description: 'OK',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: AppendixDto::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    #[Route(path: '/appendices/{nodeId}', methods: ['GET'])]
    #[Route(path: '/appendix/{nodeId}', methods: ['GET'])]
    public function read(Project $project, Appendix $appendix): JsonResponse
    {
        return $this->json($appendix, Response::HTTP_OK);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Put(
        summary: 'Updates an existing Appendix.',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(ref: new Nelmio\Model(type: UpdateAppendixRequest::class))
        ),
        parameters: [
            new OA\Parameter(
                name: 'nodeId',
                description: 'The ID of the appendix',
                in: 'path',
                required: true
            ),
        ],
        responses: [
            new OA\Response(
                response: Response::HTTP_ACCEPTED,
                description: 'ACCEPTED',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: AppendixDto::class))
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    #[Route(path: '/appendices/{nodeId}', methods: ['PUT'])]
    #[Route(path: '/appendix/{nodeId}', methods: ['PUT'])]
    public function update(
        Project               $project,
        Appendix              $appendix,
        #[MapRequestPayload]
        UpdateAppendixRequest $request,
    ): JsonResponse {
        $this->appendixService->update($project, $appendix, $request);
        return $this->json($appendix, Response::HTTP_ACCEPTED);
    }

    #[IsGranted('EDIT_PROJECT', 'project')]
    #[OA\Delete(
        summary: 'Deletes a specific Appendix',
        parameters: [
            new OA\Parameter(
                name: 'nodeId',
                description: 'The ID of the appendix',
                in: 'path',
                required: true
            ),
        ],
        responses: [
            new OA\Response(
                response: Response::HTTP_ACCEPTED,
                description: 'ACCEPTED',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: AppendixDto::class))
            ),
            new OA\Response(
                response: Response::HTTP_BAD_REQUEST,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: new Nelmio\Model(type: ErrorResponseDto::class))
            ),
            new OA\Response(response: 404, description: 'Not Found'),
        ]
    )]
    #[Route(path: '/appendices/{nodeId}', methods: ['DELETE'])]
    #[Route(path: '/appendix/{nodeId}', methods: ['DELETE'])]
    public function delete(Project $project, Appendix $appendix): JsonResponse
    {
        $this->appendixService->delete($appendix);
        return $this->json($appendix, Response::HTTP_OK);
    }
}
