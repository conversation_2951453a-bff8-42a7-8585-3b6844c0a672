<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Transformer;

use App\Helper\Xml2Helper;
use DOMElement;
use DOMXPath;

class TableTransformer
{
    public function __construct(private readonly Xml2Helper $xml2Helper) {}

    /**
     * Process tables in XML to ensure valid structure (thead, tbody, colgroup)
     */
    public function processTables(string $xml): string
    {
        if (empty($xml)) {
            return $xml;
        }

        try {
            $dom = $this->xml2Helper->createDOMDocument($xml);
            $xpath = $this->xml2Helper->createDOMXPath($dom);

            $tables = $xpath->query('//x:table');

            foreach ($tables as $table) {
                $this->processTable($table, $xpath);
            }

            return $dom->saveXML($dom->documentElement);
        } catch (\Exception $e) {
            return $xml;
        }
    }

    /**
     * Function to ensure valid table structure
     */
    private function processTable(DOMElement $table, DOMXPath $xpath): void
    {
        $existingThead = $xpath->query('.//x:thead', $table);
        if ($existingThead->length > 0) {
            $this->processTheadStructure($table, $xpath);
            return;
        }

        $tbody = $xpath->query('.//x:tbody', $table)->item(0);
        if (!$tbody) {
            $directTrs = $xpath->query('./x:tr', $table);
            if ($directTrs->length > 0) {
                $this->wrapTrElement($table, $directTrs);
                $tbody = $xpath->query('.//x:tbody', $table)->item(0);
            }
        }

        if ($tbody) {
            $this->extractHeaderRows($table, $tbody, $xpath);
        }
    }

    private function processTheadStructure(DOMElement $table, DOMXPath $xpath): void
    {
        $tbodyThRows = $xpath->query('.//x:tbody/x:tr[x:th]', $table);

        if ($tbodyThRows->length > 0) {
            $thead = $xpath->query('.//x:thead', $table)->item(0);
            $tbody = $xpath->query('.//x:tbody', $table)->item(0);

            if ($thead && $tbody) {
                foreach ($tbodyThRows as $headerRow) {
                    $previousSiblings = $xpath->query('./preceding-sibling::x:tr[x:td]', $headerRow);
                    if ($previousSiblings->length === 0) {
                        $tbody->removeChild($headerRow);
                        $thead->appendChild($headerRow);
                    }
                }
            }
        }
    }

    private function wrapTrElement(DOMElement $table, $directTrs): void
    {
        $tbody = $table->ownerDocument->createElement('tbody');

        foreach ($directTrs as $tr) {
            $table->removeChild($tr);
            $tbody->appendChild($tr);
        }

        $table->appendChild($tbody);
    }

    private function extractHeaderRows(DOMElement $table, DOMElement $tbody, DOMXPath $xpath): void
    {
        $headerRows = [];
        $firstChild = $tbody->firstElementChild;

        while ($firstChild && $firstChild->nodeName === 'tr') {
            $thElements = $xpath->query('.//x:th', $firstChild);
            if ($thElements->length > 0) {
                $headerRows[] = $firstChild;
                $firstChild = $firstChild->nextElementSibling;
            } else {
                break;
            }
        }

        if (!empty($headerRows)) {
            $thead = $table->ownerDocument->createElement('thead');

            foreach ($headerRows as $headerRow) {
                $tbody->removeChild($headerRow);
                $thead->appendChild($headerRow);
            }

            $table->insertBefore($thead, $tbody);
            $this->ensureColgroup($table, $headerRows[0], $xpath);
        }
    }

    private function ensureColgroup(DOMElement $table, DOMElement $firstHeaderRow, DOMXPath $xpath): void
    {
        $existingColgroup = $xpath->query('./colgroup', $table);
        if ($existingColgroup->length > 0) {
            return;
        }

        $columnCount = 0;
        $headerCells = $xpath->query('.//th | .//td', $firstHeaderRow);

        foreach ($headerCells as $cell) {
            $colspan = $cell->getAttribute('colspan');
            $columnCount += $colspan ? (int)$colspan : 1;
        }

        if ($columnCount > 0) {
            $colgroup = $table->ownerDocument->createElement('colgroup');

            for ($i = 0; $i < $columnCount; $i++) {
                $col = $table->ownerDocument->createElement('col');
                $colgroup->appendChild($col);
            }

            $table->insertBefore($colgroup, $table->firstChild);
        }
    }
}
