<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Title;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class TitleMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'title';
    }

    public static function dtoClass(): string
    {
        return Title::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Title);

        if (array_key_exists('title-abbreviation', $attrs) && is_string($attrs['title-abbreviation'])) {
            $element->setTitleAbbreviation($attrs['title-abbreviation']);
        }
        if (array_key_exists('title-year', $attrs) && is_string($attrs['title-year'])) {
            $element->setTitleYear($attrs['title-year']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Title);

        if (!empty($element->getTitleAbbreviation())) {
            $attrs['title-abbreviation'] = $element->getTitleAbbreviation();
        }
        if (!empty($element->getTitleYear())) {
            $attrs['title-year'] = $element->getTitleYear();
        }
    }
}
