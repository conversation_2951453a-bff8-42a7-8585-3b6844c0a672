<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Correlated;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\SubTitle;
use App\Serializer\Encoder\Xml2\Element\SuperTitle;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function mb_trim;
use function preg_replace;
use function Sabre\Xml\Deserializer\keyValue;

class TitleGroupMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'titlegroup';
    }

    public static function dtoClass(): string
    {
        return TitleGroup::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof TitleGroup);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('super-title' === $key && $value instanceof SuperTitle) {
                $element->setSuperTitle($value);
            } elseif ('committee-desig' === $key && $value instanceof CommitteeDesignation) {
                $element->setCommitteeDesignation($value);
            } elseif ('label' === $key && $value instanceof Label) {
                $element->setLabel($value);
            } elseif ('number' === $key && $value instanceof Number) {
                $element->setNumber($value);
            } elseif ('correlated' === $key && $value instanceof Correlated) {
                $element->setCorrelated($value);
            } elseif ('title' === $key && $value instanceof Title) {
                $element->setTitle($value);
            } elseif ('sub-title' === $key && $value instanceof SubTitle) {
                $element->setSubTitle($value);
            } elseif ('history' === $key && $value instanceof History) {
                $element->setHistory($value);
            }
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof TitleGroup);

        if ($element->getSuperTitle()) {
            $writer->writeElement(self::CLARK . 'super-title', $element->getSuperTitle());
        }

        if ($element->getCommitteeDesignation()) {
            $writer->writeElement(self::CLARK . 'committee-desig', $element->getCommitteeDesignation());
        }

        if ($element->getLabel()) {
            $writer->writeElement(self::CLARK . 'label', $element->getLabel());
        }

        if ($element->getNumber()) {
            $writer->writeElement(self::CLARK . 'number', $element->getNumber());
        }

        if ($element->getCorrelated()) {
            $element->getCorrelated()->setBody($this->cleanParagraphTags($element->getCorrelated()->getBody()));
            $writer->writeElement(self::CLARK . 'correlated', $element->getCorrelated());
        }
        if ($element->getTitle()) {
            $element->getTitle()->setBody($this->cleanParagraphTags($element->getTitle()->getBody()));
            $writer->writeElement(self::CLARK . 'title', $element->getTitle());
        }

        if ($element->getSubTitle()) {
            $writer->writeElement(self::CLARK . 'sub-title', $element->getSubTitle());
        }

        if ($element->getHistory()) {
            $writer->writeElement(self::CLARK . 'history', $element->getHistory());
        }
    }

    private function cleanParagraphTags(string $content): string
    {
        $content = preg_replace('/<p[^>]*>\s*<\/p>/', '', $content);
        $content = preg_replace('/<\/?p[^>]*>/', '', $content);
        return mb_trim(preg_replace('/<(\w+)[^>]*>\s*<\/\1>/', '', $content));
    }
}
