<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Url;

use function array_key_exists;
use function assert;
use function is_string;

/**
 * @since 1.11
 */
class UrlMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'url';
    }

    public static function dtoClass(): string
    {
        return Url::class;
    }

    protected function readAttributes($element, array $attrs): void
    {
        assert($element instanceof Url);

        if (array_key_exists('href', $attrs) && is_string($attrs['href'])) {
            $element->setHref($attrs['href']);
        }
        if (array_key_exists('alt', $attrs) && is_string($attrs['alt'])) {
            $element->setAlt($attrs['alt']);
        }
    }

    protected function writeAttributes($element, array &$attrs): void
    {
        assert($element instanceof Url);

        if (!empty($element->getHref())) {
            $attrs['href'] = $element->getHref();
        }
        if (!empty($element->getAlt())) {
            $attrs['alt'] = $element->getAlt();
        }
    }
}
