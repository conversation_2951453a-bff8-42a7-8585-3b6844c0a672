<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Section;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;

/**
 * @since 1.11
 */
class BackMatterMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'backmatter';
    }

    public static function dtoClass(): string
    {
        return BackMatter::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof BackMatter);

        $children = [];
        foreach ($this->mixedContent($reader) as $key => $value) {
            if ('appendix' === $key && $value instanceof Appendix) {
                $children[] = $value;
            } elseif ('level-1' === $key && $value instanceof Level) {
                $children[] = $value;
            } elseif ('index' === $key && $value instanceof Index) {
                $children[] = $value;
            }
        }
        $element->setChildren($children);
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof BackMatter);

        foreach ($element->getChildren() as $i) {
            if ($i instanceof Appendix) {
                $writer->writeElement(self::CLARK . 'appendix', $i);
            } elseif ($i instanceof Index) {
                $writer->writeElement(self::CLARK . 'index', $i);
            } elseif ($i instanceof Level || $i instanceof Section) {
                $i->setDisplayLevel(1);
                $writer->writeElement(self::CLARK . 'level-1', $i);
            }
        }
    }
}
