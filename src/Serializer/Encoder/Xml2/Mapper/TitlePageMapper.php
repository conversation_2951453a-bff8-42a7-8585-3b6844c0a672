<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CoverImage;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use Sabre\Xml\Reader;
use Sabre\Xml\Writer;

use function assert;
use function Sabre\Xml\Deserializer\keyValue;

class TitlePageMapper extends AbstractMapper
{
    public static function localName(): string
    {
        return 'titlepage';
    }

    public static function dtoClass(): string
    {
        return TitlePage::class;
    }

    protected function readElement(Reader $reader, $element): void
    {
        assert($element instanceof TitlePage);

        foreach (keyValue($reader, self::XMLNS) as $key => $value) {
            if ('titlegroup' === $key && $value instanceof TitleGroup) {
                $element->setTitleGroup($value);
            } elseif ('cover-image' === $key && $value instanceof CoverImage) {
                $element->setCoverImage($value);
            }
        }
    }

    protected function writeElement(Writer $writer, $element): void
    {
        assert($element instanceof TitlePage);

        if ($element->getTitleGroup() instanceof TitleGroup) {
            $writer->writeElement(self::CLARK . 'titlegroup', $element->getTitleGroup());
        }
        if ($element->getCoverImage() instanceof CoverImage) {
            $writer->writeElement(self::CLARK . 'cover-image', $element->getCoverImage());
        }

        // temporary solution
        // @codeCoverageIgnoreStart
//        if (!$element->getTitleGroup() instanceof TitleGroup && !$element->getCoverImage() instanceof CoverImage) {
//            $tg = new TitleGroup();
//            $tg->setTitle('');
//            $writer->writeElement(self::CLARK . 'titlegroup', $tg);
//        }
        // @codeCoverageIgnoreEnd
    }
}
