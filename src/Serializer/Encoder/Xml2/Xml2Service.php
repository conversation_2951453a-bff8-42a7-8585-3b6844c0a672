<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2;

use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Mapper\AbstractMapper;
use App\Serializer\Encoder\Xml2\Mapper\LevelMapper;
use App\Serializer\Encoder\Xml2\Mapper\MapperInterface;
use Sabre\Xml\Reader;
use Sabre\Xml\Service;

use function preg_replace;
use function range;
use function sprintf;

use const LIBXML_BIGLINES;
use const LIBXML_COMPACT;
use const LIBXML_NONET;

class Xml2Service
{
    public Service $service;

    private array $outerXmlElements = [
        'blockquote',
        'book-icon',
        'boxed-text',
        'caution',
        'col',
        'colgroup',
        'colspec',
        'commentary',
        'copyright',
        'equation',
        'exception',
        'figure-note',
        'form',
        'img',
        'keyword',
        'informal-equation',
        'informal-figure',
        'informal-table',
        'inline-mediaobject-group',
        'inline-mediaobject',
        'isbn',
        'issn',
        'legalnotice',
        'legend',
        'margin-text',
        'mediaobject-group',
        'mediaobject',
        'nav-pointer-group',
        'ol',
        'p',
        'printing-location',
        'publication-history',
        'spanspec',
        'table',
        'table-note',
        'tbody',
        'tfoot',
        'tgroup',
        'thead',
        'trademarks',
        'ul',
        'warning',
        'where-list',
    ];
    private array $innerXmlElements = [
        'add-line',
        'orgname',
        'street',
        'city',
        'state',
        'country',
        'postalcode',
        'phone',
        'fax',
    ];

    /**
     * @param MapperInterface[] $mappers
     */
    public function __construct(iterable $mappers)
    {
        $this->service = new Service();
        $this->service->namespaceMap[Xml2Schema::XMLNS] = null;
        $this->service->namespaceMap[Xml2Schema::XMLNS_M] = 'm';
        $this->service->options = LIBXML_BIGLINES | LIBXML_COMPACT | LIBXML_NONET;

        foreach ($mappers as $mapper) {
            $clark = $mapper->elementName();
            $dtoClass = $mapper->dtoClass();

            $this->service->mapValueObject($clark, $dtoClass);
            $this->service->elementMap[$clark] = [$mapper, 'read'];
            $this->service->classMap[$dtoClass] = [$mapper, 'write'];
        }

        foreach (range(2,11) as $level) {
            $mapper = new LevelMapper($level);
            $this->service->elementMap[$mapper->elementName($level)] = [$mapper, 'read'];
        }

        // add outer/inner xml mapping
        $this->service->elementMap[sprintf('{%s}math', Xml2Schema::XMLNS_M)] = [$this, 'readOuterXml'];
        foreach ($this->outerXmlElements as $nodeName) {
            $this->service->elementMap[AbstractMapper::CLARK . $nodeName] = [$this, 'readOuterXml'];
        }
        foreach ($this->innerXmlElements as $nodeName) {
            $this->service->elementMap[AbstractMapper::CLARK . $nodeName] = [$this, 'readInnerXml'];
        }
    }

    public function read(string $xml): ?AbstractElement
    {
        return $this->service->parse($xml);
    }

    public function write($dto): string
    {
        return $this->service->writeValueObject($dto);
    }

    public function readOuterXml(Reader $reader): string
    {
        $xml = preg_replace(AbstractMapper::REGEX_XMLNS, '', $reader->readOuterXml());
        $reader->next();
        return $xml;
    }

    public function readInnerXml(Reader $reader): string
    {
        $xml = preg_replace(AbstractMapper::REGEX_XMLNS, '', $reader->readInnerXml());
        $reader->next();
        return $xml;
    }
}
