<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2;

class Xml2Schema
{
    const VERSION            = 1.16;
    const SCHEMATRON_VERSION = 1.05;
    const XMLNS              = 'https://schema.iccsafe.org/book/schema/1.0';
    const XMLNS_M            = 'http://www.w3.org/1998/Math/MathML';
}
