<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Encoder\Xml2;

use App\Exception\ApiException;
use App\Helper\Xml2Helper;
use App\Serializer\Encoder\Xml2\Element\AbstractElement;
use App\Serializer\Encoder\Xml2\Event\PostEncodeEvent;
use App\Serializer\Encoder\Xml2\Event\PreDecodeEvent;
use App\Service\StopwatchService;
use Symfony\Component\Stopwatch\StopwatchEvent;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

class Xml2Encoder
{
    private StopwatchEvent $stopwatch;

    public function __construct(
        private readonly Xml2Service              $service,
        private readonly EventDispatcherInterface $eventDispatcher,
        StopwatchService                          $stopwatch,
    ) {
        $this->stopwatch = $stopwatch->create('export.xml');
    }

    public function encode(AbstractElement $element, bool $convertToXml2Schema = true): string
    {
        $this->stopwatch->start();

        $xml = $this->service->write($element);
        $xml = $this->dispatchPostEncodeEvent($xml, $convertToXml2Schema);

        $this->stopwatch->stop();

        return $xml;
    }

    /** @throws ApiException */
    protected function dispatchPostEncodeEvent(string $xml, bool $convertToXml2Schema): string
    {
        $event = new PostEncodeEvent($xml, $convertToXml2Schema);
        $this->eventDispatcher->dispatch($event);

        $xml = Xml2Helper::stripDeclaration($event->getXml());

        if (empty($event->getXml())) {
            throw new ApiException('Document is empty');
        }

        return $xml;
    }

    public function decode(string $xml): AbstractElement
    {
        $event = new PreDecodeEvent($xml);
        $this->eventDispatcher->dispatch($event);

        $document = $event->getDocument();
        $newXml = $document->saveXML($document->documentElement);

        return $this->service->read($newXml);
    }
}
