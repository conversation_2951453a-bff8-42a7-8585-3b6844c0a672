<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\Project;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

use function assert;
use function in_array;

class ProjectNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;

    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Project::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Project
            && $format !== 'dto';
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof Project);

        if (isset($context[ObjectNormalizer::GROUPS]) && in_array('*', $context[ObjectNormalizer::GROUPS])) {
            $context[ObjectNormalizer::GROUPS] = ['project:read'];
        }

        $data = $this->normalizer->normalize($object, $format, $context);
        $data['dataType'] = 'project';

        return $data;
    }
}
