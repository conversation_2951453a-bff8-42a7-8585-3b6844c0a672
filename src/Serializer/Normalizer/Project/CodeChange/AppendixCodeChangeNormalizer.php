<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Project\CodeChange;

use App\Entity\Project\CodeChange\AppendixCodeChange;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

use function array_merge;

/**
 * Normalize ProposalAction for API
 */
class AppendixCodeChangeNormalizer extends AbstractCodeChangeNormalizer implements NormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            AppendixCodeChange::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof AppendixCodeChange;
    }

    /** @param AppendixCodeChange $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        return array_merge($this->createFromTemplate($object), [
            'dtype'          => 'appendix',
            'label'          => $object->getLabel(),
            'number'         => $this->getCleanOrdinal($object->getNumber()),
            'originalNumber' => $object->getNumber(),
            'title'          => $object->getTitle(),
            'content'        => $object->getBody(),
        ]);
    }
}
