<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Project\CodeChange;

use App\Entity\Project\CodeChange\ReferenceStandardCodeChange;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

use function array_merge;

/**
 * Normalize ProposalAction for API
 */
class ReferenceStandardCodeChangeNormalizer extends AbstractCodeChangeNormalizer implements NormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            ReferenceStandardCodeChange::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof ReferenceStandardCodeChange;
    }

    /** @param ReferenceStandardCodeChange $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        return array_merge($this->createFromTemplate($object), [
            'dtype'  => 'refStandard',
            'number' => $this->getCleanOrdinal($object->getNumber() ?? ''),
            'title'  => $object->getTitle() ?? '',
        ]);
    }
}
