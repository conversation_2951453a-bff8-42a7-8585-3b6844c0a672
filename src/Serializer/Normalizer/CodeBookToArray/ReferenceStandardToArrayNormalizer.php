<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\ReferenceStandard;
use App\Serializer\Dto\Book\ReferenceStandardDto;

class ReferenceStandardToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            ReferenceStandard::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof ReferenceStandard
            && 'json' === $format;
    }

    /** @param ReferenceStandard $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new ReferenceStandardDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapQrCode($object, $dto);
        $this->mapCodeChanges($object, $dto);

        $dto->setNumber($object->getNumber());
        $dto->setNumberYear($object->getNumberYear());
        $dto->setTitle($object->getTitle());
        $dto->setTitleYear($object->getTitleYear());
        $dto->setNavPointerGroup($object->getNavPointerGroup());

        return $dto->asArray();
    }
}
