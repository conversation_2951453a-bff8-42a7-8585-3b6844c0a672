<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Appendix;
use App\Serializer\Dto\Book\AppendixDto;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class AppendixToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            Appendix::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Appendix
            && 'json' === $format;
    }

    /** @param Appendix $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new AppendixDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapTitleGroup($object, $dto);
        $this->mapQrCode($object, $dto);
        $this->mapCodeChanges($object, $dto);

        $dto->setIndexNumber($object->getIndexNumber());
        $dto->setTocEntry($object->hasTocEntry());
        $dto->setTocAutoAdd($object->isTocAutoAdd());

        $dto->setHistory($object->getHistory());
        $dto->setObjectivesTitle($object->getObjectivesTitle());
        $dto->setObjectives($object->getObjectives());
        $dto->setNoteTitle($object->getNoteTitle());
        $dto->setNote($object->getNote());
        $dto->setAbstractTitle($object->getAbstractTitle());
        $dto->setAbstract($object->getAbstract());
        $dto->setKeywordsTitle($object->getKeywordsTitle());
        $dto->setKeywords($object->getKeywords());

        $dto->setBody($object->getBody());

        try {
            $skip = $context['bookContents'] ?? false;
            if (!$skip) {
                $children = $this->normalizer->normalize($object->getChildren(), $format, $context);
                $dto->setChildren($children);
            }
        } catch (ExceptionInterface $exception) {
        }

        return $dto->asArray();
    }
}
