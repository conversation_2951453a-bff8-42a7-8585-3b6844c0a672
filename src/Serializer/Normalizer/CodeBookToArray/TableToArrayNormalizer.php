<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Table;
use App\Serializer\Dto\Book\TableDto;

class TableToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            Table::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Table
            && 'json' === $format;
    }

    /** @param Table $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new TableDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapTitleGroup($object, $dto);
        $this->mapQrCode($object, $dto);
        $this->mapCodeChanges($object, $dto);

        $dto->setOrientation($object->getOrientation());
        $dto->setFloat($object->getFloat());
        $dto->setTocEntry($object->hasTocEntry());
        $dto->setPageWide($object->isPageWide());
        $dto->setFrame($object->getFrame());
        $dto->setColumnSeparator($object->isColumnSeparator());
        $dto->setRowSeparator($object->isRowSeparator());
        $dto->setBackgroundColor($object->getBackgroundColor());
        $dto->setTableStyle($object->getTableStyle());
        $dto->setClass($object->getClass());
        $dto->setTitleAttr($object->getTitleAttr());
        $dto->setSummary($object->getSummary());
        $dto->setWidth($object->getWidth());
        $dto->setBorder($object->getBorder());
        $dto->setCellSpacing($object->getCellSpacing());
        $dto->setCellPadding($object->getCellPadding());
        $dto->setRules($object->getRules());

        $dto->setTable($object->getTable());
        $dto->setCaption($object->getCaption());
        $dto->setTableNotesTitle($object->getTableNotesTitle());
        $dto->setTableNotes($object->getTableNotes());
        $dto->setLegend($object->getLegend());
        $dto->setSource($object->getSource());
        $dto->setCreditTitle($object->getCreditTitle());
        $dto->setCredit($object->getCredit());

        return $dto->asArray();
    }
}
