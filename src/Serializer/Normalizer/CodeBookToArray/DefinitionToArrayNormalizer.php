<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Definition;
use App\Serializer\Dto\Book\DefinitionItemDto;

class DefinitionToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            Definition::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Definition
            && 'json' === $format;
    }

    /** @param Definition $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new DefinitionItemDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapQrCode($object, $dto);
        $this->mapCodeChanges($object, $dto);

        $dto->setIndexNumber($object->getIndexNumber());

        $dto->setCommitteeDesignation($object->getCommitteeDesignation());
        $dto->setTerm($object->getTerm());
        $dto->setDefinition($object->getDefinition());

        return $dto->asArray();
    }
}
