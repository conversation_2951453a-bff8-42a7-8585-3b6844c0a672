<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CommonBook\Attribute\RevisionAttributes;
use App\Entity\CommonBook\Field\QrCode;
use App\Entity\CommonBook\Field\TitleGroup;
use App\Repository\NoteAttachmentRepository;
use App\Serializer\Dto\Book\Dto;
use App\Serializer\Dto\Book\Attribute\RevisionAttributes as DtoRevisionAttributes;
use App\Serializer\Dto\Book\Field\QrCode as DtoQrCode;
use App\Serializer\Dto\Book\Field\TitleGroup as DtoTitleGroup;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\Service\Attribute\Required;

use function array_reverse;
use function implode;
use function mb_trim;
use function method_exists;
use function str_replace;

abstract class AbstractCodeBookToArrayNormalizer implements NormalizerInterface, NormalizerAwareInterface
{
    use NormalizerAwareTrait;

    private ?UrlGeneratorInterface $urlGenerator;
    private ?NoteAttachmentRepository $attachmentRepository;

    protected function mapCommon(AbstractCodeBookNode $from, Dto $to): void
    {
        $to->setId($from->getNodeId());
        $to->setUuid($from->getUlid());
        $to->setCtXmlId($this->buildCtXmlIdFromNode($from));
        $to->setDataType($from->getDataType());

        // common attributes
        $to->setRole($from->getRole());
        $to->setDisplay($from->getDisplay());
        $to->setVerbatim($from->isVerbatim());
        $to->setLanguage($from->getLanguage());
        $to->setAdditionalInfo($from->getAdditionalInfo());

        // internal fields
        $to->setStatus($from->getStatus());
        $to->setShowDeletionMarker($from->getShowDeletionMarker());
        $to->setCodesNotes($from->getCodesNotes());
        $to->setPubsNotes($from->getPubsNotes());
        $to->setTypesetterNotes($from->getTypesetterNotes());
        $to->setHasCodesNotes($this->hasNotes($from->getCodesNotes(), $from->getNodeId(), 'internal'));
        $to->setHasPubsNotes($this->hasNotes($from->getPubsNotes(), $from->getNodeId(), 'pubs'));
        $to->setHasTypesetterNotes(!empty($from->getTypesetterNotes()));
    }

    protected function mapRevisionAttributes(AbstractCodeBookNode $from, Dto $to): void
    {
        /** @var RevisionAttributes $from */
        /** @var DtoRevisionAttributes $to */
        $to->setRevisionBy($from->getRevisionBy());
        $to->setRevisionDateTime($from->getRevisionDateTime());
        $to->setRevision($from->getRevision());
        $to->setRevisionGroup($from->getRevisionGroup());
        $to->setDataChanged($from->getDataChanged());
        $to->setDataChangedIn($from->getDataChangedIn());
        $to->setRelocatedFromAttr($from->getRelocatedFromAttr());

        // internal fields
        $to->setDeletedBy($from->getDeletedBy());
        $to->setDeletedDate($from->getDeletedDate());
    }

    protected function mapTitleGroup(AbstractCodeBookNode $from, Dto $to): void
    {
        /** @var TitleGroup $from */
        /** @var DtoTitleGroup $to */
        $to->setSuperTitle($from->getSuperTitle());
        $to->setCommitteeDesignation($from->getCommitteeDesignation());
        $to->setLabel($from->getLabel());
        $to->setNumber($from->getNumber());
        $to->setCorrelated($from->getCorrelated());
        $to->setTitle($from->getTitle());
        $to->setTitleAbbreviation($from->getTitleAbbreviation());
        $to->setTitleYear($from->getTitleYear());
        $to->setSubTitle($from->getSubTitle());
    }

    protected function mapQrCode(AbstractCodeBookNode $from, Dto $to): void
    {
        /** @var QrCode $from */
        /** @var DtoQrCode $to */
        $to->setQrActive($from->isQrActive());
        $to->setQrId($from->getQrId());
        $to->setQrDisplay($from->isQrDisplay());
        $to->setQrLevelReference($from->getQrLevelReference());
        $to->setQrPurpose($from->getQrPurpose());
        $to->setQrImage($from->getQrImage());
        $to->setQrShortUrl($from->getQrShortUrl());
        $to->setQrBookIcon($from->getQrBookIcon());
        $to->setQrUrl($from->getQrUrl());
        $to->setQrIcon($from->getQrIcon());
        $to->setQrDepartment($from->getQrDepartment());
        $to->setQrBusinessUnit($from->getQrBusinessUnit());
    }

    protected function mapCodeChanges(AbstractCodeBookNode $from, Dto $to): void
    {
        $to->setHasApprovedChanges($this->hasApprovedChanges($from));
        $to->setHasFullyIncorporatedChanges($this->hasFullyIncorporatedChanges($from));
    }

    private function buildCtXmlIdFromNode(AbstractCodeBookNode $node): string
    {
        $segments = [];
        $current = $node;
        while ($current instanceof AbstractCodeBookNode) {
            $pathId = mb_trim($current->getPathId());
            if ($pathId !== '') {
                $segments[] = $this->formatSegment($pathId);
            }
            $current = $current->getParent();
        }

        if ($segments !== []) {
            return implode('_', array_reverse($segments));
        }

        $fallback = mb_trim($node->getNodeId());
        if ($fallback === '' && method_exists($node, 'getUlid')) {
            $fallback = mb_trim($node->getUlid());
        }

        return $fallback === '' ? '' : $this->formatSegment($fallback);
    }

    private function formatSegment(string $value): string
    {
        $value = mb_trim($value);
        if ($value === '') {
            return '';
        }

        return str_replace(' ', '_', $value);
    }

    private function hasNotes(?string $noteText, string $nodeId, string $attachmentType): bool
    {
        return $this->hasTextNotes($noteText) || $this->hasAttachmentNotes($nodeId, $attachmentType);
    }

    private function hasTextNotes(?string $noteText): bool
    {
        return !empty($noteText);
    }

    private function hasAttachmentNotes(string $nodeId, string $attachmentType): bool
    {
        return $this->attachmentRepository->countBySectionAndType($nodeId, $attachmentType) > 0;
    }


    private function hasApprovedChanges(AbstractCodeBookNode $node): bool
    {
        return !$node->getCodeChanges()->isEmpty();
    }

    private function hasFullyIncorporatedChanges(AbstractCodeBookNode $node): bool
    {
        $codeChanges = $node->getCodeChanges();

        if ($codeChanges->isEmpty()) {
            return false;
        }

        foreach ($codeChanges as $codeChange) {
            if (!$codeChange->isCodesIncorporated()) {
                return false;
            }
        }

        return true;
    }

    #[Required]
    public function setRouter(UrlGeneratorInterface $router): void
    {
        $this->urlGenerator = $router;
    }

    #[Required]
    public function setNoteAttachmentRepository(NoteAttachmentRepository $repository): void
    {
        $this->attachmentRepository = $repository;
    }
}
