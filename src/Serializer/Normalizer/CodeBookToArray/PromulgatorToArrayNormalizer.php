<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Promulgator;
use App\Serializer\Dto\Book\PromulgatorDto;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class PromulgatorToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            Promulgator::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Promulgator
            && 'json' === $format;
    }

    /** @param Promulgator $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new PromulgatorDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapQrCode($object, $dto);
        $this->mapCodeChanges($object, $dto);

        $dto->setAcronym($object->getAcronym());
        $dto->setAddressLine($object->getAddressLine());
        $dto->setOrganizationName($object->getOrganizationName());
        $dto->setStreet($object->getStreet());
        $dto->setCity($object->getCity());
        $dto->setState($object->getState());
        $dto->setPostalCode($object->getPostalCode());
        $dto->setCountry($object->getCountry());
        $dto->setEmail($object->getEmail());
        $dto->setUrl($object->getUrl());
        $dto->setUrlHref($object->getUrlHref());
        $dto->setUrlAlt($object->getUrlAlt());
        $dto->setPhone($object->getPhone());
        $dto->setFax($object->getFax());

        try {
            $skip = $context['bookContents'] ?? false;
            if (!$skip) {
                $children = $this->normalizer->normalize($object->getChildren(), $format, $context);
                $dto->setChildren($children);
            }
        } catch (ExceptionInterface $exception) {
        }

        return $dto->asArray();
    }
}
