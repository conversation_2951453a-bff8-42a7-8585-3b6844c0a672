<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Chapter;
use App\Serializer\Dto\Book\ChapterDto;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class ChapterToArrayNormalizer extends AbstractCodeBookToArrayNormalizer
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            Chapter::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof Chapter
            && 'json' === $format;
    }

    /** @param Chapter $object */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        $dto = new ChapterDto();
        $this->mapCommon($object, $dto);
        $this->mapRevisionAttributes($object, $dto);
        $this->mapQrCode($object, $dto);
        $this->mapTitleGroup($object, $dto);
        $this->mapCodeChanges($object, $dto);

        $dto->setIndexNumber($object->getIndexNumber());
        $dto->setTocEntry($object->hasTocEntry());
        $dto->setTocAutoAdd($object->isTocAutoAdd());
        $dto->setReserveCount($object->getReserveCount());
        $dto->setDisplayLevel($object->getDisplayLevel());

        $dto->setRelocatedFrom($object->getRelocatedFrom());
        $dto->setRelocatedFromId($object->getRelocatedFromId());

        $dto->setHistory($object->getHistory());
        $dto->setObjectivesTitle($object->getObjectivesTitle());
        $dto->setObjectives($object->getObjectives());
        $dto->setAbstractTitle($object->getAbstractTitle());
        $dto->setAbstract($object->getAbstract());
        $dto->setKeywordsTitle($object->getKeywordsTitle());
        $dto->setKeywords($object->getKeywords());
        $dto->setPdfFormat($object->getPdfFormat());

        $dto->setBody($object->getBody());

        try {
            $skip = $context['bookContents'] ?? false;
            if (!$skip) {
                $children = $this->normalizer->normalize($object->getChildren(), $format, $context);
                $dto->setChildren($children);
            }
        } catch (ExceptionInterface $exception) {
        }

        return $dto->asArray();
    }
}
