<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp;

use App\Entity\Cdp\CdpGroup;
use App\Entity\Cdp\CdpProposal;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Exception\ApiException;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\SerializerAwareInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;

use function array_filter;
use function array_map;
use function is_array;

class CdpProposalNormalizer implements DenormalizerInterface, SerializerAwareInterface
{
    use SerializerAwareTrait;

    private EntityRepository $proposalRepo;
    private EntityRepository $actionRepo;

    public function __construct(private readonly EntityManagerInterface $em)
    {
        $this->proposalRepo = $em->getRepository(CdpProposal::class);
        $this->actionRepo = $em->getRepository(CdpProposalAction::class);
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            CdpProposal::class => true,
        ];
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        return $type === CdpProposal::class
            && is_array($data);
    }

    /** @throws ApiException */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): CdpProposal
    {
        $endpoint = $context['endpoint'] ?? null;
        if (!$endpoint) {
            throw new ApiException('Endpoint not found in context.');
        }

        $entity = $this->em->getRepository(CdpProposal::class)->findOneBy([
            'endpoint' => $endpoint,
            'cdpId'    => (string) $data['id'],
        ]);
        if (!$entity) {
            $entity = new CdpProposal();
            $entity->setEndpoint($endpoint);
            $entity->setCdpId((string) $data['id']);
        }

        $entity->setTrackingNumber($data['tracking_number']);
        $entity->setFinalAction($data['final_action']);
        $entity->setCommitteeActions($data['committee_actions']);
        $entity->setCah1Action($data['cah1_action']);
        $entity->setCah2Action($data['cah2_action']);
        $entity->setPchAction($data['pch_action']);
        $entity->setReasonStatement($data['reason_statement']);
        $entity->setProposalStatus($data['proposal_status']);

        if ($group = $this->em->getrepository(CdpGroup::class)->findOneBy([
            'endpoint' => $endpoint,
            'name'     => $data['group'],
        ])) {
            $entity->setGroup($group);
        }

        try {
            $sections = $data['sections'] ?? [];
            $changes = $this->serializer->denormalize($sections, CdpProposalAction::class . '[]', $format, $context);
            array_map([$entity, 'addCode'], $changes);
        } catch (ExceptionInterface $exception) {
        }

        return $entity;
    }
}
