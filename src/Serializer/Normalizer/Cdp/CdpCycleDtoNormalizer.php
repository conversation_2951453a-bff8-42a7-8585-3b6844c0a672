<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp;

use App\Entity\Cdp\CdpCycle;
use App\Serializer\Dto\Project\CdpCycleDto;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

use function assert;
use function json_decode;
use function json_encode;

class CdpCycleDtoNormalizer implements NormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            CdpCycle::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof CdpCycle
            && 'dto' === $format;
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof CdpCycle);

        $dto = new CdpCycleDto();
        $dto->id = $object->getId() ?? 0;
        $dto->cdpId = $object->getCdpId() ?? '';
        $dto->name = $object->getName() ?? '';

        return json_decode(json_encode($dto), true);
    }
}
