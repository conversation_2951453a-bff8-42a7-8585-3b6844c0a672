<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp\ProposalAction;

use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Entity\Cdp\ProposalAction\CdpReferenceStandardAction;
use App\Exception\ApiException;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

use function array_key_exists;
use function in_array;
use function is_array;

/**
 * Only denormalize json array to cdpXML 1.0.
 *
 * @version cdpXML 1.0
 */
class CdpReferenceStandardActionDenormalizer extends CdpProposalActionDenormalizer implements DenormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            CdpReferenceStandardAction::class => true,
            CdpProposalAction::class          => true,
        ];
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []
    ): bool {
        return in_array($type, [CdpReferenceStandardAction::class, CdpProposalAction::class], true)
            && is_array($data)
            && array_key_exists('dtype', $data)
            && in_array($data['dtype'], ['refStandard', 'refStandardGroup'], true);
    }

    /**
     * @throws ApiException
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []
    ): CdpReferenceStandardAction {
        /** @var CdpReferenceStandardAction $entity */
        $entity = $this->getEntity(CdpReferenceStandardAction::class, $data, $context);

        $entity->setNumber($data['number']);
        $entity->setTitle($data['title']);
        $entity->setPromulgatorAcronym($data['promulgatorAcronym']);

        return $entity;
    }
}
