<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp\ProposalAction;

use App\Entity\Cdp\ProposalAction\CdpDefinitionAction;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Exception\ApiException;
use <PERSON>ymfony\Component\Serializer\Normalizer\DenormalizerInterface;

use function array_key_exists;
use function in_array;
use function is_array;

/**
 * Only denormalize json array to cdpXML 1.0.
 *
 * @version cdpXML 1.0
 */
class CdpDefinitionActionDenormalizer extends CdpProposalActionDenormalizer implements DenormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            CdpDefinitionAction::class => true,
            CdpProposalAction::class   => true,
        ];
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []
    ): bool {
        return in_array($type, [CdpDefinitionAction::class, CdpProposalAction::class], true)
            && is_array($data)
            && array_key_exists('dtype', $data)
            && 'definition' === $data['dtype'];
    }

    /**
     * @throws ApiException
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []
    ): CdpDefinitionAction {
        /** @var CdpDefinitionAction $entity */
        $entity = $this->getEntity(CdpDefinitionAction::class, $data, $context);

        $entity->setLabel($data['label']);
        $entity->setTerm($data['term']);
        $entity->setDefinition($data['definition']);
        $entity->setExtendedDefinition($data['extended']);

        return $entity;
    }
}
