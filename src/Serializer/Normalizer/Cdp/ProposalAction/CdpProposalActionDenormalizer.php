<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp\ProposalAction;

use App\Entity\Cdp\CdpBook;
use App\Entity\Cdp\CdpEndpoint;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Exception\ApiException;
use Doctrine\ORM\EntityManagerInterface;

abstract class CdpProposalActionDenormalizer
{
    public function __construct(private readonly EntityManagerInterface $em)
    {
    }

    /** @throws ApiException */
    protected function getEntity(string $class, array $data, array $context): CdpProposalAction
    {
        $endpoint = $context['endpoint'] ?? null;
        if (!$endpoint) {
            throw new ApiException('Endpoint not found in context.');
        }

        /** @var CdpProposalAction $entity */
        $entity = $this->em->getRepository($class)->findOneBy([
            'endpoint' => $endpoint,
            'cdpId'    => (string) $data['id'],
        ]);
        if (!$entity) {
            $entity = new $class();
            $entity->setEndpoint($endpoint);
            $entity->setCdpId((string) $data['id']);
        }

        $entity->setBook($this->getBook($endpoint, $data['book']));
        $entity->setXmlId($data['xml_id']);
        $entity->setInstruction($data['status']);

        // ['original_section']
        $entity->setOriginalId((string) $data['original_section']['id']);
        $entity->setOriginalXmlId($data['original_section']['xmlId']);
        $entity->setOriginalLabel($data['original_section']['label']);
        $entity->setOriginalOrdinal($data['original_section']['ordinal']);
        $entity->setOriginalTitle($data['original_section']['title']);

        // ['links']
        $entity->setLinkPreview($data['links']['preview']);
        $entity->setLinkPdf($data['links']['pdf']);

        return $entity;
    }

    protected function getBook(CdpEndpoint $endpoint, string $cdpId): ?CdpBook
    {
        $book = $this->em->getRepository(CdpBook::class)->findOneBy([
            'endpoint' => $endpoint,
            'cdpId'    => $cdpId,
        ]);

        if (!$book) {
            $book = new CdpBook();
            $book->setEndpoint($endpoint);
            $book->setCdpId($cdpId);
            $book->setTitle($cdpId);

            $this->em->persist($book);
            $this->em->flush();
        }

        return $book;
    }
}
