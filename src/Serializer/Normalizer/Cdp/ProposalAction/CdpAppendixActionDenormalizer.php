<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp\ProposalAction;

use App\Entity\Cdp\ProposalAction\CdpAppendixAction;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Exception\ApiException;
use <PERSON>ymfony\Component\Serializer\Normalizer\ContextAwareDenormalizerInterface;

use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

use function in_array;
use function is_array;

/**
 * Only denormalize json array to cdpXML 1.0.
 *
 * @version cdpXML 1.0
 */
class CdpAppendixActionDenormalizer extends CdpProposalActionDenormalizer implements DenormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            CdpAppendixAction::class => true,
            CdpProposalAction::class => true,
        ];
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []): bool
    {
        return in_array($type, [CdpAppendixAction::class, CdpProposalAction::class], true)
            && is_array($data)
            && isset($data['dtype'])
            && 'appendix' === $data['dtype'];
    }

    /** @throws ApiException */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): CdpAppendixAction
    {
        /** @var CdpAppendixAction $entity */
        $entity = $this->getEntity(CdpAppendixAction::class, $data, $context);

        $entity->setLabel($data['label']);
        $entity->setNumber($data['ordinal']);
        $entity->setTitle($data['title']);

        return $entity;
    }
}
