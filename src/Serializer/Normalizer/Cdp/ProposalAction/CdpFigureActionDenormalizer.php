<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp\ProposalAction;

use App\Entity\Cdp\ProposalAction\CdpFigureAction;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Exception\ApiException;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;

use function array_key_exists;
use function in_array;
use function is_array;

/**
 * Only denormalize json array to cdpXML 1.0.
 *
 * @version cdpXML 1.0
 */
class CdpFigureActionDenormalizer extends CdpProposalActionDenormalizer implements DenormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            CdpFigureAction::class   => true,
            CdpProposalAction::class => true,
        ];
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []
    ): bool {
        return in_array($type, [CdpFigureAction::class, CdpProposalAction::class], true)
            && is_array($data)
            && array_key_exists('dtype', $data)
            && 'figure' === $data['dtype'];
    }

    /**
     * @throws ApiException
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): CdpFigureAction
    {
        /** @var CdpFigureAction $entity */
        $entity = $this->getEntity(CdpFigureAction::class, $data, $context);

        $entity->setLabel($data['label']);
        $entity->setNumber($data['ordinal']);
        $entity->setTitle($data['title']);
        $entity->setFigure($data['figure']);
        $entity->setFootnote($data['footnote']);

        return $entity;
    }
}
