<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\Cdp\ProposalAction;

use App\Entity\Cdp\ProposalAction\CdpPromulgatorAction;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Exception\ApiException;
use <PERSON>ymfony\Component\Serializer\Normalizer\DenormalizerInterface;

use function array_key_exists;
use function in_array;
use function is_array;

/**
 * Only denormalize json array to cdpXML 1.0.
 *
 * @version cdpXML 1.0
 */
class CdpPromulgatorActionDenormalizer extends CdpProposalActionDenormalizer implements DenormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            CdpPromulgatorAction::class => true,
            CdpProposalAction::class    => true,
        ];
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []
    ): bool {
        return in_array($type, [CdpPromulgatorAction::class, CdpProposalAction::class], true)
            && is_array($data)
            && array_key_exists('dtype', $data)
            && in_array($data['dtype'], ['promulgator', 'promulgatorGroup'], true);
    }

    /**
     * @throws ApiException
     */
    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []
    ): CdpPromulgatorAction {
        /** @var CdpPromulgatorAction $entity */
        $entity = $this->getEntity(CdpPromulgatorAction::class, $data, $context);

        $entity->setNumber($data['ordinal']);
        $entity->setAcronym($data['acronym']);
        $entity->setName($data['name']);
        $entity->setUrl($data['url']);
        $entity->setAddressee($data['addressee']);
        $entity->setLocation($data['location']);
        $entity->setStreetAddress($data['streetAddress']);
        $entity->setCity($data['city']);
        $entity->setState($data['state']);
        $entity->setPostalCode($data['postalCode']);
        $entity->setCountry($data['country']);

        return $entity;
    }
}
