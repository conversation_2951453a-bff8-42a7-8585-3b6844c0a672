<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use DateTimeInterface;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\DateTimeNormalizer;
use S<PERSON>fony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

use function assert;
use function is_string;

/**
 * Decorates the DateTimeNormalizer to allow null values on denormalization.
 */
class DateTimeNullNormalizer implements NormalizerInterface, DenormalizerInterface
{
    private DateTimeNormalizer $dateTimeNormalizer;

    public function __construct(DateTimeNormalizer $dateTimeNormalizer)
    {
        $this->dateTimeNormalizer = $dateTimeNormalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            DateTimeInterface::class => true,
            null                     => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $this->dateTimeNormalizer->supportsNormalization($data, $format);
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): string
    {
        assert($object instanceof DateTimeInterface || null === $object);
        return $this->dateTimeNormalizer->normalize($object, $format, $context);
    }

    public function supportsDenormalization(mixed $data, string $type, ?string $format = null, array $context = []
    ): bool {
        return $this->dateTimeNormalizer->supportsDenormalization($data, $type, $format);
    }

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []
    ): ?DateTimeInterface {
        assert(is_string($data) || null === $data);
        if (empty($data) || !is_string($data)) {
            return null;
        }

        return $this->dateTimeNormalizer->denormalize($data, $type, $format, $context);
    }
}
