<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\Project;
use App\Serializer\Dto\Project\ProjectDto;
use S<PERSON>fony\Component\Serializer\Exception\ExceptionInterface;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\NormalizerInterface;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\SerializerAwareInterface;
use Symfony\Component\Serializer\SerializerAwareTrait;

use function assert;
use function in_array;
use function json_decode;
use function json_encode;

class ProjectDtoNormalizer implements NormalizerInterface, SerializerAwareInterface
{
    use SerializerAwareTrait;

    private ObjectNormalizer $normalizer;

    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            Project::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        $groups = $context['groups'] ?? [];
        return $data instanceof Project
            && in_array('project:read', $groups, true);
    }

    /** @throws ExceptionInterface */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof Project);

        $data = $this->normalizer->normalize($object, null, $context);
        $groups = ['groups' => 'project:read'];

        $dto = new ProjectDto();
        $dto->id = $data['id'] ?? 0;
        $dto->shortCode = $data['shortCode'];
        $dto->baseBook = $data['baseBook'];
        $dto->title = $data['bookTitle'] ?? $data['title'] ?? '';
        $dto->workingTitle = $data['workingTitle'];
        $dto->projectType = $this->serializer->normalize($object->getProjectType(), 'dto', $groups);
        $dto->versionType = $this->serializer->normalize($object->getVersionType(), 'dto', $groups);
        $dto->category = $object->getProjectCategory()
            ? $object->getProjectCategory()->getName()
            : '';
        $dto->createdDate = $data['createdDate'];
        $dto->lastModified = $data['lastModifiedDate'];
        $dto->commentaryEnabled = $data['commentaryEnabled'];
        $dto->hasCdpAccess = $data['hasCdpAccess'];
        $dto->cycle = $this->serializer->normalize($object->getCycle(), 'dto', $groups);;
        $dto->cdpAccessBookId = $data['cdpAccessBookId'];
        $dto->status = $data['status'];
        $dto->manualImagePath = $data['manualImagePath'] ?? '';
        $dto->blueLineUser = $data['blueLineUser'] ?? '';
        $dto->blueLineDate = $data['blueLineDate'] ?? '';

        // janus carryover:
        $dto->errorMessage = $data['errorMessage'] ?? '';
        $dto->lifeCycle = $object->isBlueLine()
            ? 'BlueLine'
            : ($object->isActive() ? 'Active' : 'InActive');
        $dto->categoryUsers = $object->getProjectCategory()
            ? $object->getProjectCategory()->getUsersArray()
            : [];
        $dto->projectUsers = $object->getUsersArray();
        $dto->userHasCategories = false;
        $dto->xml2 = !empty($object->getCodeBook());
        $dto->contentHubDocumentId = $data['contentHubDocumentId'] ?? 0;
        $dto->contentHubUploadDate = $data['contentHubUploadDate'] ?? '';
        $dto->proposalsEvaluationStatus = $data['proposalsEvaluationStatus'] ?? 'idle';
        $dto->lastProposalsEvaluationAt = $data['lastProposalsEvaluationAt']  ??  null;

        return json_decode(json_encode($dto), true);
    }
}
