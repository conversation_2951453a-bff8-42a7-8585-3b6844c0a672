<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\User\User;
use App\Serializer\Dto\UserDto;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

use function assert;
use function json_decode;
use function json_encode;

class UserDtoNormalizer implements NormalizerInterface
{
    public function getSupportedTypes(?string $format): array
    {
        return [
            User::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof User
            && 'dto' === $format;
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof User);

        $dto = new UserDto();
        $dto->id = $object->getId() ?? 0;
        $dto->email = $object->getEmail();
        $dto->firstName = $object->getFirstName();
        $dto->lastName = $object->getLastName();

        return json_decode(json_encode($dto), true);
    }
}
