<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer\CodeBook;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Section;
use App\Repository\NoteAttachmentRepository;
use App\Serializer\Dto\Book\SectionDto;
use App\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use App\Service\Xml2\EquationService;
use Symfony\Contracts\Service\Attribute\Required;

use function in_array;

class SectionNormalizer extends AbstractNormalizer
{
    #[Required]
    public EquationService $equationService;

    public function supportsNormalization($data, ?string $format = null): bool
    {
        return $data instanceof Section;
    }

    /**
     * @param Section $object
     */
    public function normalize($object, ?string $format = null, array $context = []): SectionDto
    {
        $dto = new SectionDto();
        $this->copyAttributes($object, $dto);
        $this->copyQrCode($object, $dto);
        $this->copyTitleGroup($object, $dto);
        $this->copyInternalNotes($object, $dto);
        $dto->setId($object->getNodeId());
        $dto->setHistory($object->getHistory());
        $dto->setObjectivesTitle($object->getObjectivesTitle());
        $dto->setObjectives($object->getObjectives());
        $dto->setAbstractTitle($object->getAbstractTitle());
        $dto->setAbstract($object->getAbstract());
        $dto->setKeywordsTitle($object->getKeywordsTitle());
        $dto->setKeywords($object->getKeywords());

        $body = $this->equationService->processEquationEntityInSection($object->getBody());

        if (isset($context['notesType'])) {
            $body = $this->appendInternalNotesToBody($body, $object, $context['notesType']);
        }
        $dto->setBody($body);
        $dto->setHasCodesNotes(
            strlen($object->getCodesNotes()) > 0 ||
                $this->attachmentRepository->countBySectionAndType($object->getNodeId(), 'internal') > 0
        );

        $dto->setHasPubsNotes(
            strlen($object->getPubsNotes()) > 0 ||
                $this->attachmentRepository->countBySectionAndType($object->getNodeId(), 'pubs') > 0
        );

        $dto->setHasTypesetterNotes(strlen($object->getTypesetterNotes()) > 0);

        try {
            $children = $this->normalizer->normalize($object->getChildren(), $format, $context);
            $dto->setChildren($children);
        } catch (ExceptionInterface $exception) { // @codeCoverageIgnore
        }

        return $dto;
    }

    public function supportsDenormalization($data, string $type, ?string $format = null): bool
    {
        return $data instanceof SectionDto
            && in_array($type, [Section::class, AbstractCodeBookNode::class], true);
    }

    /** @param SectionDto $data */
    public function denormalize($data, string $type, ?string $format = null, array $context = []): Section
    {
        $entity = new Section();
        $this->copyAttributes($data, $entity);
        $this->copyQrCode($data, $entity);
        $this->copyTitleGroup($data, $entity);
        $this->copyInternalNotes($data, $entity);

        $entity->setOriginalNodeId($data->getId());
        $entity->setHistory($data->getHistory());
        $entity->setObjectivesTitle($data->getObjectivesTitle());
        $entity->setObjectives($data->getObjectives());
        $entity->setAbstractTitle($data->getAbstractTitle());
        $entity->setAbstract($data->getAbstract());
        $entity->setKeywordsTitle($data->getKeywordsTitle());
        $entity->setKeywords($data->getKeywords());
        $entity->setBody($data->getBody());

        try {
            $children = $this->normalizer->denormalize(
                $data->getChildren(),
                AbstractCodeBookNode::class . '[]',
                $format,
                $context
            );
            $entity->setChildren($children);
        } catch (ExceptionInterface $exception) { // @codeCoverageIgnore
        }

        return $entity;
    }
}
