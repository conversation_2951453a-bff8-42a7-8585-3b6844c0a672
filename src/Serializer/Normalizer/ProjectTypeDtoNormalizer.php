<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\ProjectType;
use App\Serializer\Dto\Project\ProjectTypeDto;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\NormalizerInterface;
use <PERSON><PERSON><PERSON>ny\Component\Serializer\Normalizer\ObjectNormalizer;

use function assert;
use function json_decode;
use function json_encode;

class ProjectTypeDtoNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;

    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            ProjectType::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof ProjectType
            && 'dto' === $format;
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof ProjectType);

        $data = $this->normalizer->normalize($object, null, $context);

        $dto = new ProjectTypeDto();
        $dto->id = $data['id'] ?? 0;
        $dto->name = $data['name'];

        return json_decode(json_encode($dto), true);
    }
}
