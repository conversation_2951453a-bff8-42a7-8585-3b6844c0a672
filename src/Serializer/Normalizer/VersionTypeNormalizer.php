<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\VersionType;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\NormalizerInterface;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\ObjectNormalizer;

use function assert;

class VersionTypeNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;

    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            VersionType::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof VersionType;
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof VersionType);

        $data = $this->normalizer->normalize($object, $format, $context);
        $data['dataType'] = 'versionType';

        return $data;
    }
}
