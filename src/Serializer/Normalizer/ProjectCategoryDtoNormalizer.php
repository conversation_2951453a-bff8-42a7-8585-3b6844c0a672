<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\ProjectCategory;
use App\Serializer\Dto\Project\ProjectCategoryDto;
use <PERSON><PERSON>fony\Component\Serializer\Normalizer\NormalizerInterface;

use function assert;
use function json_decode;
use function json_encode;

class ProjectCategoryDtoNormalizer implements NormalizerInterface
{
    private ProjectCategoryNormalizer $normalizer;

    public function __construct(ProjectCategoryNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            ProjectCategory::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof ProjectCategory
            && 'dto' === $format;
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof ProjectCategory);
        $data = $this->normalizer->normalize($object, null, ['groups' => ['category:read']]);

        $dto = new ProjectCategoryDto();
        $dto->id = $data['id'] ?? 0;
        $dto->name = $data['name'];

        return json_decode(json_encode($dto), true);
    }
}
