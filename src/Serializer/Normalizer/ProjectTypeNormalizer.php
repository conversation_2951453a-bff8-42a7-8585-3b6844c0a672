<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\ProjectType;
use <PERSON>ymfony\Component\Serializer\Normalizer\NormalizerInterface;
use <PERSON><PERSON><PERSON>ny\Component\Serializer\Normalizer\ObjectNormalizer;

use function assert;

class ProjectTypeNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;

    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            ProjectType::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof ProjectType
            && 'dto' !== $format;
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof ProjectType);

        $key = ObjectNormalizer::IGNORED_ATTRIBUTES;
        $context[$key] = array_merge($context[$key] ?? [], ['level']);

        $data = $this->normalizer->normalize($object, $format, $context);
        $data['dataType'] = 'projectType';

        return $data;
    }
}
