<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\User\User;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use S<PERSON>fony\Component\Serializer\Normalizer\ObjectNormalizer;

use function assert;

class UserNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;

    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            User::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof User;
    }

    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof User);

        $key = ObjectNormalizer::IGNORED_ATTRIBUTES;
        $context[$key] = array_merge($context[$key] ?? [], [
            'username',
            'userIdentifier',
            'salt',
            'password',
        ]);

        if (isset($context[ObjectNormalizer::GROUPS]) && in_array('*', (array) $context[ObjectNormalizer::GROUPS])) {
            $context[ObjectNormalizer::GROUPS] = ['user:read'];
        }

        $data = $this->normalizer->normalize($object, $format, $context);
        $data['dataType'] = 'user';

        return $data;
    }
}
