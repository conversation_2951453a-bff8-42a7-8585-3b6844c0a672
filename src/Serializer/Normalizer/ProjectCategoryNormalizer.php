<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Normalizer;

use App\Entity\ProjectCategory;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use <PERSON><PERSON><PERSON>ny\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

use function array_merge;
use function assert;

class ProjectCategoryNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;

    public function __construct(ObjectNormalizer $normalizer)
    {
        $this->normalizer = $normalizer;
    }

    public function getSupportedTypes(?string $format): array
    {
        return [
            ProjectCategory::class => true,
        ];
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof ProjectCategory
            && 'dto' !== $format;
    }

    /** @throws ExceptionInterface */
    public function normalize(mixed $object, ?string $format = null, array $context = []): array
    {
        assert($object instanceof ProjectCategory);

        $key = ObjectNormalizer::IGNORED_ATTRIBUTES;
        $context[$key] = array_merge($context[$key] ?? [], ['usersArray']);

        if (isset($context[ObjectNormalizer::GROUPS]) && in_array('*', $context[ObjectNormalizer::GROUPS])) {
            $context[ObjectNormalizer::GROUPS] = ['category:read'];
        }

        $data = $this->normalizer->normalize($object, $format, $context);
        $data['dataType'] = 'projectCategory';

        return $data;
    }
}
