<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book;

/**
 * @since 1.13
 */
class DefinitionItemDto extends Dto
{
    use Attribute\RevisionAttributes;
    use Attribute\IndexNumber;

    use Field\CommitteeDesignation;
    use Field\Term;
    use Field\Definition;
    use Field\Caption;
    use Field\Legend;
    use Field\Source;
    use Field\Credit;

    public function asArray(): array
    {
        return [
            '_type'                => 'definition',
            'dataType'             => 'definition',

            // attributes
            'id'                   => $this->getId(),
            'uuid'                 => $this->getUuid(),
            'ctXmlId'              => $this->getCtXmlId(),
            'role'                 => $this->getRole(),
            'display'              => $this->getDisplay(),
            'verbatim'             => $this->isVerbatim(),
            'language'             => $this->getLanguage(),
            'additionalInfo'       => $this->getAdditionalInfo(),
            'revisionBy'           => $this->getRevisionBy(),
            'revisionDateTime'     => $this->getRevisionDateTime() ? (array) $this->getRevisionDateTime() : null,
            'revision'             => $this->getRevision(),
            'revisionGroup'        => $this->getRevisionGroup(),
            'dataChanged'          => $this->getDataChanged(),
            'dataChangedIn'        => $this->getDataChangedIn(),
            'relocatedFromAttr'    => $this->getRelocatedFromAttr(),
            'deletedBy'            => $this->getDeletedBy(),
            'deletedDate'          => $this->getDeletedDate() ? (array) $this->getDeletedDate() : null,
            'indexNumber'          => $this->getIndexNumber(),

            // qr code
            'qrActive'             => $this->isQrActive(),
            'qrId'                 => $this->getQrId(),
            'qrDisplay'            => $this->isQrDisplay(),
            'qrLevelReference'     => $this->getQrLevelReference(),
            'qrPurpose'            => $this->getQrPurpose(),
            'qrImage'              => $this->getQrImage(),
            'qrShortUrl'           => $this->getQrShortUrl(),
            'qrBookIcon'           => $this->getQrBookIcon(),
            'qrUrl'                => $this->getQrUrl(),
            'qrIcon'               => $this->getQrIcon(),
            'qrDepartment'         => $this->getQrDepartment(),
            'qrBusinessUnit'       => $this->getQrBusinessUnit(),

            // fields
            'committeeDesignation' => $this->getCommitteeDesignation(),
            'term'                 => $this->getTerm(),
            'definition'           => $this->getDefinition(),
            'children'             => [],

            // internal fields
            'status'               => $this->getStatus(),
            'showDeletionMarker'   => $this->getShowDeletionMarker(),
            'codesNotes'           => $this->getCodesNotes(),
            'pubsNotes'            => $this->getPubsNotes(),
            'typesetterNotes'      => $this->getTypesetterNotes(),
            'hasCodesNotes'        => $this->getHasCodesNotes(),
            'hasPubsNotes'         => $this->getHasPubsNotes(),
            'hasTypesetterNotes'   => $this->getHasTypesetterNotes(),
            'hasApprovedChanges'   => $this->getHasApprovedChanges(),
            'hasFullyIncorporatedChanges' => $this->getHasFullyIncorporatedChanges(),
        ];
    }
}
