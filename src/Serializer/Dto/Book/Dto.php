<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book;

use App\Enum\Status;
use Symfony\Component\Serializer\Annotation\Groups;

use function in_array;

abstract class Dto
{
    use Attribute\CommonAttributes;
    use Field\InternalNotes;
    use Field\QrCode;

    #[Groups(['list:contents'])]
    public string $id = '';
    #[Groups(['list:contents'])]
    public string $uuid = '';
    #[Groups(['list:contents'])]
    public string $ctXmlId = '';
    public string $ctUuid = '';
    private int $internalId = 0;
    #[Groups(['list:contents'])]
    public string $dataType = '';
    #[Groups(['list:contents'])]
    public bool $showDeletionMarker = false;
    #[Groups(['list:contents'])]
    public string $status = Status::IN_PROGRESS;
    /**
     * @var Dto[]
     */
    #[Groups(['list:contents'])]
    public array $children = [];

    public bool $hasApprovedChanges = false;
    public bool $hasFullyIncorporatedChanges = false;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getUuid(): string
    {
        return $this->uuid;
    }

    public function setUuid(string $uuid): void
    {
        $this->uuid = $uuid;
    }

    public function getCtXmlId(): string
    {
        return $this->ctXmlId;
    }

    public function setCtXmlId(string $ctXmlId): void
    {
        $this->ctXmlId = $ctXmlId;
    }

    public function getCtUuid(): string
    {
        return $this->ctUuid;
    }

    public function setCtUuid(string $ctUuid): void
    {
        $this->ctUuid = $ctUuid;
    }

    public function getInternalId(): int
    {
        return $this->internalId;
    }

    public function setInternalId(int $internalId): void
    {
        $this->internalId = $internalId;
    }

    public function getDataType(): string
    {
        return $this->dataType;
    }

    public function setDataType(string $dataType): self
    {
        $this->dataType = $dataType;
        return $this;
    }

    public function getChildren(): array
    {
        return $this->children;
    }

    public function setChildren(array $children): self
    {
        $this->children = $children;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        if (in_array($status, Status::cases())) {
            $this->status = $status;
        }
        return $this;
    }

    public function getShowDeletionMarker(): bool
    {
        return $this->showDeletionMarker;
    }

    public function setShowDeletionMarker(bool $showDeletionMarker): self
    {
        $this->showDeletionMarker = $showDeletionMarker;
        return $this;
    }

    public function getHasApprovedChanges(): bool
    {
        return $this->hasApprovedChanges;
    }

    public function setHasApprovedChanges(bool $hasApprovedChanges): self
    {
        $this->hasApprovedChanges = $hasApprovedChanges;
        return $this;
    }

    public function getHasFullyIncorporatedChanges(): bool
    {
        return $this->hasFullyIncorporatedChanges;
    }

    public function setHasFullyIncorporatedChanges(bool $hasFullyIncorporatedChanges): self
    {
        $this->hasFullyIncorporatedChanges = $hasFullyIncorporatedChanges;
        return $this;
    }
}
