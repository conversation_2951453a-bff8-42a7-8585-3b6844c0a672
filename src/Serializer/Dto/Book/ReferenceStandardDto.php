<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book;

/**
 * @since 1.11
 */
class ReferenceStandardDto extends Dto
{
    use Attribute\RevisionAttributes;

    use Field\Number;
    use Field\Title;

    public string $navPointerGroup = '';
    public string $numberYear = '';

    public function getNavPointerGroup(): string
    {
        return $this->navPointerGroup;
    }

    public function setNavPointerGroup(string $navPointerGroup): self
    {
        $this->navPointerGroup = $navPointerGroup;
        return $this;
    }

    public function getNumberYear(): string
    {
        return $this->numberYear;
    }

    public function setNumberYear(string $numberYear): self
    {
        $this->numberYear = $numberYear;
        return $this;
    }

    public function asArray(): array
    {
        return [
            '_type'              => 'referenceStandard',
            'dataType'           => 'referenceStandard',

            // attributes
            'id'                 => $this->getId(),
            'uuid'               => $this->getUuid(),
            'ctXmlId'            => $this->getCtXmlId(),
            'role'               => $this->getRole(),
            'display'            => $this->getDisplay(),
            'verbatim'           => $this->isVerbatim(),
            'language'           => $this->getLanguage(),
            'additionalInfo'     => $this->getAdditionalInfo(),
            'revisionBy'         => $this->getRevisionBy(),
            'revisionDateTime'   => $this->getRevisionDateTime() ? (array) $this->getRevisionDateTime() : null,
            'revision'           => $this->getRevision(),
            'revisionGroup'      => $this->getRevisionGroup(),
            'dataChanged'        => $this->getDataChanged(),
            'dataChangedIn'      => $this->getDataChangedIn(),
            'relocatedFromAttr'  => $this->getRelocatedFromAttr(),
            'deletedBy'          => $this->getDeletedBy(),
            'deletedDate'        => $this->getDeletedDate() ? (array) $this->getDeletedDate() : null,

            // qr code
            'qrActive'           => $this->isQrActive(),
            'qrId'               => $this->getQrId(),
            'qrDisplay'          => $this->isQrDisplay(),
            'qrLevelReference'   => $this->getQrLevelReference(),
            'qrPurpose'          => $this->getQrPurpose(),
            'qrImage'            => $this->getQrImage(),
            'qrShortUrl'         => $this->getQrShortUrl(),
            'qrBookIcon'         => $this->getQrBookIcon(),
            'qrUrl'              => $this->getQrUrl(),
            'qrIcon'             => $this->getQrIcon(),
            'qrDepartment'       => $this->getQrDepartment(),
            'qrBusinessUnit'     => $this->getQrBusinessUnit(),

            // fields
            'number'             => $this->getNumber(),
            'numberYear'         => $this->getNumberYear(),
            'title'              => $this->getTitle(),
            'titleYear'          => $this->getTitleYear(),
            'navPointerGroup'    => $this->getNavPointerGroup(),

            // internal fields
            'status'             => $this->getStatus(),
            'showDeletionMarker' => $this->getShowDeletionMarker(),
            'codesNotes'         => $this->getCodesNotes(),
            'pubsNotes'          => $this->getPubsNotes(),
            'typesetterNotes'    => $this->getTypesetterNotes(),
            'hasCodesNotes'      => $this->getHasCodesNotes(),
            'hasPubsNotes'       => $this->getHasPubsNotes(),
            'hasTypesetterNotes' => $this->getHasTypesetterNotes(),
            'hasApprovedChanges' => $this->getHasApprovedChanges(),
            'hasFullyIncorporatedChanges' => $this->getHasFullyIncorporatedChanges(),
        ];
    }
}
