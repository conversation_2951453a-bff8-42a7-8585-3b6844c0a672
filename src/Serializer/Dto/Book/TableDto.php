<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book;

/**
 * @since 1.13
 */
class TableDto extends Dto
{
    use Attribute\RevisionAttributes;
    use Attribute\CalsTableAttributes;
    use Attribute\HtmlTableAttributes;
    use Attribute\FloatAttribute;
    use Attribute\Orientation;

    use Field\TitleGroup;
    use Field\Caption;
    use Field\Legend;
    use Field\Source;
    use Field\Credit;

    public string $table = '';
    public string $tableNotesTitle = '';
    public string $tableNotes = '';

    public function getTable(): string
    {
        return $this->table;
    }

    public function setTable(string $table): self
    {
        $this->table = $table;
        return $this;
    }

    public function getTableNotesTitle(): string
    {
        return $this->tableNotesTitle;
    }

    public function setTableNotesTitle(string $tableNotesTitle): self
    {
        $this->tableNotesTitle = $tableNotesTitle;
        return $this;
    }

    public function getTableNotes(): string
    {
        return $this->tableNotes;
    }

    public function setTableNotes(string $tableNotes): self
    {
        $this->tableNotes = $tableNotes;
        return $this;
    }

    public function asArray(): array
    {
        return [
            '_type'                => 'table',
            'dataType'             => 'table',

            // attributes
            'id'                   => $this->getId(),
            'uuid'                 => $this->getUuid(),
            'ctXmlId'              => $this->getCtXmlId(),
            'role'                 => $this->getRole(),
            'display'              => $this->getDisplay(),
            'verbatim'             => $this->isVerbatim(),
            'language'             => $this->getLanguage(),
            'additionalInfo'       => $this->getAdditionalInfo(),
            'revisionBy'           => $this->getRevisionBy(),
            'revisionDateTime'     => $this->getRevisionDateTime() ? (array) $this->getRevisionDateTime() : null,
            'revision'             => $this->getRevision(),
            'revisionGroup'        => $this->getRevisionGroup(),
            'dataChanged'          => $this->getDataChanged(),
            'dataChangedIn'        => $this->getDataChangedIn(),
            'relocatedFromAttr'    => $this->getRelocatedFromAttr(),
            'deletedBy'            => $this->getDeletedBy(),
            'deletedDate'          => $this->getDeletedDate() ? (array) $this->getDeletedDate() : null,
            // cals
            'orientation'          => $this->getOrientation(),
            'float'                => $this->getFloat(),
            'tocEntry'             => $this->hasTocEntry(),
            'pageWide'             => $this->isPageWide(),
            'frame'                => $this->getFrame(),
            'columnSeparator'      => $this->isColumnSeparator(),
            'rowSeparator'         => $this->isRowSeparator(),
            'backgroundColor'      => $this->getBackgroundColor(),
            'tableStyle'           => $this->getTableStyle(),
            // html
            'class'                => $this->getClass(),
            'titleAttr'            => $this->getTitleAttr(),
            'summary'              => $this->getSummary(),
            'width'                => $this->getWidth(),
            'border'               => $this->getBorder(),
            'cellSpacing'          => $this->getCellSpacing(),
            'cellPadding'          => $this->getCellPadding(),
            'rules'                => $this->getRules(),

            // title group
            'superTitle'           => $this->getSuperTitle(),
            'committeeDesignation' => $this->getCommitteeDesignation(),
            'label'                => $this->getLabel(),
            'number'               => $this->getNumber(),
            'correlated'           => $this->getCorrelated(),
            'title'                => $this->getTitle(),
            'titleAbbreviation'    => $this->getTitleAbbreviation(),
            'titleYear'            => $this->getTitleYear(),
            'subTitle'             => $this->getSubTitle(),

            // qr code
            'qrActive'             => $this->isQrActive(),
            'qrId'                 => $this->getQrId(),
            'qrDisplay'            => $this->isQrDisplay(),
            'qrLevelReference'     => $this->getQrLevelReference(),
            'qrPurpose'            => $this->getQrPurpose(),
            'qrImage'              => $this->getQrImage(),
            'qrShortUrl'           => $this->getQrShortUrl(),
            'qrBookIcon'           => $this->getQrBookIcon(),
            'qrUrl'                => $this->getQrUrl(),
            'qrIcon'               => $this->getQrIcon(),
            'qrDepartment'         => $this->getQrDepartment(),
            'qrBusinessUnit'       => $this->getQrBusinessUnit(),

            // fields
            'table'                => $this->getTable(),
            'caption'              => $this->getCaption(),
            'tableNotesTitle'      => $this->getTableNotesTitle(),
            'tableNotes'           => $this->getTableNotes(),
            'legend'               => $this->getLegend(),
            'source'               => $this->getSource(),
            'creditTitle'          => $this->getCreditTitle(),
            'credit'               => $this->getCredit(),

            // internal fields
            'status'               => $this->getStatus(),
            'showDeletionMarker'   => $this->getShowDeletionMarker(),
            'codesNotes'           => $this->getCodesNotes(),
            'pubsNotes'            => $this->getPubsNotes(),
            'typesetterNotes'      => $this->getTypesetterNotes(),
            'hasCodesNotes'        => $this->getHasCodesNotes(),
            'hasPubsNotes'         => $this->getHasPubsNotes(),
            'hasTypesetterNotes'   => $this->getHasTypesetterNotes(),
            'hasApprovedChanges'   => $this->getHasApprovedChanges(),
            'hasFullyIncorporatedChanges' => $this->getHasFullyIncorporatedChanges(),
        ];
    }
}
