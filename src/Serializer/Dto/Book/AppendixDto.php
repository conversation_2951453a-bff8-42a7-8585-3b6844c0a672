<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book;

class AppendixDto extends Dto
{
    use Attribute\RevisionAttributes;
    use Attribute\IndexNumber;
    use Attribute\TocEntry;
    use Attribute\TocAutoAdd;

    use Field\TitleGroup;
    use Field\History;
    use Field\Objectives;
    use Field\Note;
    use Field\AbstractField;
    use Field\Keywords;
    use Field\Body;

    public function asArray(): array
    {
        return [
            '_type'                => 'appendix',
            'dataType'             => 'appendix',

            // attributes
            'id'                   => $this->getId(),
            'uuid'                 => $this->getUuid(),
            'ctXmlId'              => $this->getCtXmlId(),
            'role'                 => $this->getRole(),
            'display'              => $this->getDisplay(),
            'verbatim'             => $this->isVerbatim(),
            'language'             => $this->getLanguage(),
            'additionalInfo'       => $this->getAdditionalInfo(),
            'revisionBy'           => $this->getRevisionBy(),
            'revisionDateTime'     => $this->getRevisionDateTime() ? (array) $this->getRevisionDateTime() : null,
            'revision'             => $this->getRevision(),
            'revisionGroup'        => $this->getRevisionGroup(),
            'dataChanged'          => $this->getDataChanged(),
            'dataChangedIn'        => $this->getDataChangedIn(),
            'relocatedFromAttr'    => $this->getRelocatedFromAttr(),
            'deletedBy'            => $this->getDeletedBy(),
            'deletedDate'          => $this->getDeletedDate() ? (array) $this->getDeletedDate() : null,
            'indexNumber'          => $this->getIndexNumber(),
            'tocEntry'             => $this->hasTocEntry(),
            'tocAutoAdd'           => $this->isTocAutoAdd(),

            // title group
            'superTitle'           => $this->getSuperTitle(),
            'committeeDesignation' => $this->getCommitteeDesignation(),
            'label'                => $this->getLabel(),
            'number'               => $this->getNumber(),
            'correlated'           => $this->getCorrelated(),
            'title'                => $this->getTitle(),
            'titleAbbreviation'    => $this->getTitleAbbreviation(),
            'titleYear'            => $this->getTitleYear(),
            'subTitle'             => $this->getSubTitle(),

            // qr code
            'qrActive'             => $this->isQrActive(),
            'qrId'                 => $this->getQrId(),
            'qrDisplay'            => $this->isQrDisplay(),
            'qrLevelReference'     => $this->getQrLevelReference(),
            'qrPurpose'            => $this->getQrPurpose(),
            'qrImage'              => $this->getQrImage(),
            'qrShortUrl'           => $this->getQrShortUrl(),
            'qrBookIcon'           => $this->getQrBookIcon(),
            'qrUrl'                => $this->getQrUrl(),
            'qrIcon'               => $this->getQrIcon(),
            'qrDepartment'         => $this->getQrDepartment(),
            'qrBusinessUnit'       => $this->getQrBusinessUnit(),

            // fields
            'history'              => $this->getHistory(),
            'objectivesTitle'      => $this->getObjectivesTitle(),
            'objectives'           => $this->getObjectives(),
            'noteTitle'            => $this->getNoteTitle(),
            'note'                 => $this->getNote(),
            'abstractTitle'        => $this->getAbstractTitle(),
            'abstract'             => $this->getAbstract(),
            'keywordsTitle'        => $this->getKeywordsTitle(),
            'keywords'             => $this->getKeywords(),
            'body'                 => $this->getBody(),
            'children'             => $this->getChildren(),

            // internal fields
            'status'               => $this->getStatus(),
            'showDeletionMarker'   => $this->getShowDeletionMarker(),
            'codesNotes'           => $this->getCodesNotes(),
            'pubsNotes'            => $this->getPubsNotes(),
            'typesetterNotes'      => $this->getTypesetterNotes(),
            'hasCodesNotes'        => $this->getHasCodesNotes(),
            'hasPubsNotes'         => $this->getHasPubsNotes(),
            'hasTypesetterNotes'   => $this->getHasTypesetterNotes(),
            'hasApprovedChanges'   => $this->getHasApprovedChanges(),
            'hasFullyIncorporatedChanges' => $this->getHasFullyIncorporatedChanges(),
        ];
    }
}
