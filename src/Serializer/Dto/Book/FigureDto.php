<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Serializer\Dto\Book;

/**
 * @since 1.11
 */
class FigureDto extends Dto
{
    use Attribute\RevisionAttributes;
    use Attribute\TocEntry;
    use Attribute\FloatAttribute;
    use Attribute\Orientation;

    use Field\TitleGroup;
    use Field\Caption;
    use Field\FigureNotes;
    use Field\Legend;
    use Field\Source;
    use Field\Credit;

    public string $media = '';
    public string $imageSrc = '';
    public string $imageAlt = '';
    public bool $qaCheck = false;

    public function isQaCheck(): bool
    {
        return $this->qaCheck;
    }

    public function setQaCheck(bool $qaCheck): self
    {
        $this->qaCheck = $qaCheck;
        return $this;
    }

    public function getMedia(): string
    {
        return $this->media;
    }

    public function setMedia(string $media): self
    {
        $this->media = $media;
        return $this;
    }

    public function getImageSrc(): string
    {
        return $this->imageSrc;
    }

    public function setImageSrc(string $imageSrc): self
    {
        $this->imageSrc = $imageSrc;
        return $this;
    }

    public function getImageAlt(): string
    {
        return $this->imageAlt;
    }

    public function setImageAlt(string $imageAlt): self
    {
        $this->imageAlt = $imageAlt;
        return $this;
    }

    public function asArray(): array
    {
        return [
            '_type'                => 'figure',
            'dataType'             => 'figure',

            // attributes
            'id'                   => $this->getId(),
            'uuid'                 => $this->getUuid(),
            'ctXmlId'              => $this->getCtXmlId(),
            'role'                 => $this->getRole(),
            'display'              => $this->getDisplay(),
            'verbatim'             => $this->isVerbatim(),
            'language'             => $this->getLanguage(),
            'additionalInfo'       => $this->getAdditionalInfo(),
            'revisionBy'           => $this->getRevisionBy(),
            'revisionDateTime'     => $this->getRevisionDateTime() ? (array) $this->getRevisionDateTime() : null,
            'revision'             => $this->getRevision(),
            'revisionGroup'        => $this->getRevisionGroup(),
            'dataChanged'          => $this->getDataChanged(),
            'dataChangedIn'        => $this->getDataChangedIn(),
            'relocatedFromAttr'    => $this->getRelocatedFromAttr(),
            'deletedBy'            => $this->getDeletedBy(),
            'deletedDate'          => $this->getDeletedDate() ? (array) $this->getDeletedDate() : null,
            'tocEntry'             => $this->hasTocEntry(),
            'float'                => $this->getFloat(),
            'orientation'          => $this->getOrientation(),

            // title group
            'superTitle'           => $this->getSuperTitle(),
            'committeeDesignation' => $this->getCommitteeDesignation(),
            'label'                => $this->getLabel(),
            'number'               => $this->getNumber(),
            'correlated'           => $this->getCorrelated(),
            'title'                => $this->getTitle(),
            'titleAbbreviation'    => $this->getTitleAbbreviation(),
            'titleYear'            => $this->getTitleYear(),
            'subTitle'             => $this->getSubTitle(),

            // qr code
            'qrActive'             => $this->isQrActive(),
            'qrId'                 => $this->getQrId(),
            'qrDisplay'            => $this->isQrDisplay(),
            'qrLevelReference'     => $this->getQrLevelReference(),
            'qrPurpose'            => $this->getQrPurpose(),
            'qrImage'              => $this->getQrImage(),
            'qrShortUrl'           => $this->getQrShortUrl(),
            'qrBookIcon'           => $this->getQrBookIcon(),
            'qrUrl'                => $this->getQrUrl(),
            'qrIcon'               => $this->getQrIcon(),
            'qrDepartment'         => $this->getQrDepartment(),
            'qrBusinessUnit'       => $this->getQrBusinessUnit(),

            // fields
            'media'                => $this->getMedia(),
            'caption'              => $this->getCaption(),
            'figureNotesTitle'     => $this->getFigureNotesTitle(),
            'figureNotes'          => $this->getFigureNotes(),
            'legend'               => $this->getLegend(),
            'source'               => $this->getSource(),
            'creditTitle'          => $this->getCreditTitle(),
            'credit'               => $this->getCredit(),
            'qaCheck'              => $this->isQaCheck(),
            'imageSrc'             => $this->getImageSrc(),
            'imageAlt'             => $this->getImageAlt(),

            // internal fields
            'status'               => $this->getStatus(),
            'showDeletionMarker'   => $this->getShowDeletionMarker(),
            'codesNotes'           => $this->getCodesNotes(),
            'pubsNotes'            => $this->getPubsNotes(),
            'typesetterNotes'      => $this->getTypesetterNotes(),
            'hasCodesNotes'        => $this->getHasCodesNotes(),
            'hasPubsNotes'         => $this->getHasPubsNotes(),
            'hasTypesetterNotes'   => $this->getHasTypesetterNotes(),
            'hasApprovedChanges'   => $this->getHasApprovedChanges(),
            'hasFullyIncorporatedChanges' => $this->getHasFullyIncorporatedChanges(),
        ];
    }
}
