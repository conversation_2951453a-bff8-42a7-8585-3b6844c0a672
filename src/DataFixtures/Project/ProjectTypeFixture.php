<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\DataFixtures\Project;

use App\Entity\ProjectType;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

/** @codeCoverageIgnore */
class ProjectTypeFixture extends Fixture implements FixtureGroupInterface
{
    public static array $types = [
        'firstPrinting'      => ['name' => 'Level 0 - 1st Printing'],
        'subsequentPrinting' => ['name' => 'Level 0 - Subsequent Printing'],
        'level1'             => ['name' => 'Level 1'],
        'level2'             => ['name' => 'Level 2'],
        'level3'             => ['name' => 'Level 3'],
    ];

    public function load(ObjectManager $manager): void
    {
        $repo = $manager->getRepository(ProjectType::class);

        foreach (self::$types as $reference => $data) {
            $entity = $repo->findOneBy(['name' => $data['name']]);
            if (!$entity) {
                $entity = new ProjectType();
                $manager->persist($entity);
            }
            $entity->setName($data['name']);
            $this->addReference('project.projectType.' . $reference, $entity);
        }

        $manager->flush();
    }

    public static function getGroups(): array
    {
        return ['prod', 'test'];
    }
}
