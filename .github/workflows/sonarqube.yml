name: SonarQube

on:
  push:
    branches:
      - main
      - develop
      - 'release/**'
  pull_request:
    types: [ opened, synchronize, reopened ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: false

jobs:
  sonarqube:
    runs-on: self-hosted
    steps:
      - name: Set up Git user
        shell: bash
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

      - name: Reset workspace permissions
        if: runner.os != 'Windows'
        shell: bash
        run: |
          echo "Resetting permissions for $GITHUB_WORKSPACE"
          sudo chmod -R u+rwX "$GITHUB_WORKSPACE"
          sudo chown -R "$(id -u):$(id -g)" "$GITHUB_WORKSPACE"

      - name: Checkout code
        uses: actions/checkout@v5

      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@v6.0.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

      - name: SonarQube Quality Gate check
        id: sonarqube-quality-gate-check
        uses: sonarsource/sonarqube-quality-gate-action@v1.2.0
        with:
          pollingTimeoutSec: 600
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

      - name: SonarQube output
        if: steps.sonarqube-quality-gate-check.outputs.quality-gate-status != 'PASSED'
        uses: actions/github-script@v7
        with:
          script: |
            // Add a comment about the new addition
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
              body: `SonarQube Quality Gate has ${{ steps.sonarqube-quality-gate-check.outputs.quality-gate-status }}.

              Detailed information can be found at: https://sonarqube.iccsafe.org/dashboard?id=InternationalCodeCouncil_correlation-tool_AYlP7DmcCNQbF6fyRD-4`
            });
