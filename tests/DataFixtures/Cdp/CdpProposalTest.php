<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\DataFixtures\Cdp;

use App\Entity\Cdp\CdpProposal;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CdpProposalTest extends KernelTestCase
{
    public function testProposalCount(): void
    {
        $em = static::getContainer()->get(EntityManagerInterface::class);
        $proposals = $em->getRepository(CdpProposal::class)->findAll();
        $this->assertCount(707, $proposals);
    }

    public function testActionCount(): void
    {
        $em = static::getContainer()->get(EntityManagerInterface::class);
        $actions = $em->getRepository(CdpProposalAction::class)->findAll();
        $this->assertGreaterThanOrEqual(5732, count($actions));
    }
}
