<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\ObjectivesMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\ObjectivesMapper
 */
class ObjectivesMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Objectives::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Objectives::class]);
        $this->assertInstanceOf(ObjectivesMapper::class, $service->classMap[Objectives::class][0]);

        $this->assertArrayHasKey(ObjectivesMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[ObjectivesMapper::elementName()]);
        $this->assertInstanceOf(ObjectivesMapper::class, $service->elementMap[ObjectivesMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Objectives $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<objectives %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Objectives();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Objectives $element, string $innerXml): void
    {
        $xml = sprintf('<objectives xmlns="%s" xmlns:m="%s">%s</objectives>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Objectives();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setBody(new Body());
        $innerXml .= '<body/>';
        yield 'body' => [deep_copy($element), $innerXml];
    }

    /** @dataProvider realCases */
    public function testRealCases(Objectives $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $note = <<<XMLFRAG
<note>
<titlegroup>
    <title><emphasis type="bold">About this chapter: </emphasis></title>
</titlegroup>
<p>...</p>
</note>
XMLFRAG;
        $xml = <<<XMLFRAG
<objectives %s>
    <titlegroup>
        <title>User notes:</title>
    </titlegroup>
    <body>
    $note
    </body>
</objectives>
XMLFRAG;
        $title = new Title();
        $title->setBody('User notes:');
        $tg = new TitleGroup();
        $tg->setTitle($title);
        $body = new Body();
        $body->setBody($note);

        $element = new Objectives();
        $element->setTitleGroup($tg);
        $element->setBody($body);
        yield 'IBC2024V2.0_Ch02' => [$element, $xml];
    }
}
