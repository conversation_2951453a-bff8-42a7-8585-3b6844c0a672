<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\GoverningType;
use App\Enum\TitleType;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use App\Serializer\Encoder\Xml2\Element\Volume;
use App\Serializer\Encoder\Xml2\Mapper\VolumeMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\VolumeMapper
 */
class VolumeMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Volume::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Volume::class]);
        $this->assertInstanceOf(VolumeMapper::class, $service->classMap[Volume::class][0]);

        $this->assertArrayHasKey(VolumeMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[VolumeMapper::elementName()]);
        $this->assertInstanceOf(VolumeMapper::class, $service->elementMap[VolumeMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Volume $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<volume %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Volume();
        $attrs = [];
        yield 'attrs / baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);

        $element->setCustomerId('customer-id');
        $attrs['customer-id'] = 'customer-id';
        yield 'attrs / @customer-id' => [deep_copy($element), $attrs];

        $element->setTitleType(TitleType::MANUAL);
        $attrs['title-type'] = TitleType::MANUAL;
        yield 'attrs / @title-type' => [deep_copy($element), $attrs];

        $element->setGoverningType(GoverningType::MUNICIPALITY);
        $attrs['governing-type'] = GoverningType::MUNICIPALITY;
        yield 'attrs / @governing-type' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Volume $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<volume %s>%s</volume>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Volume();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setMetadata(new Metadata());
        $innerXml = '<metadata/>';
        yield 'metadata' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setFrontMatter(new FrontMatter());
        $innerXml .= '<frontmatter/>';
        yield 'frontMatter' => [deep_copy($element), $innerXml];

        $chapter1 = new Level();
        $chapter1->setId('chapter1');
        $element->setChildren([$chapter1]);
        $innerXml .= '<level-1 id="chapter1"/>';
        yield 'chapter 1' => [deep_copy($element), $innerXml];

        $chapter2 = new Level();
        $chapter2->setId('chapter2');
        $element->setChildren([$chapter1, $chapter2]);
        $innerXml .= '<level-1 id="chapter2"/>';
        yield 'chapter 2' => [deep_copy($element), $innerXml];

        $backMatter = new BackMatter();
        $element->setBackMatter($backMatter);
        $innerXml .= '<backmatter/>';
        yield 'backMatter' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<volume
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
    customer-id="customer"
    title-type="code"
    governing-type="authority"
>
    <metadata>
        <meta name="schematron-ruleset">I-Code</meta>
        <meta name="title">2024 International Building Code</meta>
        <meta name="edition">2024 Edition</meta>
        <meta name="parent-document">IBC2024</meta>
        <meta name="publication-id">IBC2024V2.0</meta>
        <meta name="year">2024</meta>
        <meta name="publication-abbrev">IBC</meta>
        <meta name="version">Version 1.0</meta>
        <meta name="output-type">Print PDF</meta>
        <meta name="origin">IB</meta>
        <meta name="date-origin">2024-5-29</meta>
        <meta name="modified-by">Jeff Wight</meta>
        <meta name="date-updated">2025-06-10</meta>
    </metadata>
    <titlegroup>
        <title>2024 International Building Code</title>
    </titlegroup>
    <frontmatter>
        <titlepage/>
    </frontmatter>
    <level-1 id="chapter1" />
    <level-1 id="chapter2" />
    <backmatter>
        <index id="index"/>
    </backmatter>
</volume>
XML;
        // elements
        $element = new Volume();
        $element->setId('id');
        $element->setCtUuid('uuid');
        $element->setCustomerId('customer');
        $element->setTitleType(TitleType::CODE);
        $element->setGoverningType(GoverningType::AUTHORITY);

        $metadata = new Metadata();
        $metadata->addMeta('schematron-ruleset', 'I-Code');
        $metadata->addMeta('title', '2024 International Building Code');
        $metadata->addMeta('edition', '2024 Edition');
        $metadata->addMeta('parent-document', 'IBC2024');
        $metadata->addMeta('publication-id', 'IBC2024V2.0');
        $metadata->addMeta('year', '2024');
        $metadata->addMeta('publication-abbrev', 'IBC');
        $metadata->addMeta('version', 'Version 1.0');
        $metadata->addMeta('output-type', 'Print PDF');
        $metadata->addMeta('origin', 'IB');
        $metadata->addMeta('date-origin', '2024-5-29');
        $metadata->addMeta('modified-by', 'Jeff Wight');
        $metadata->addMeta('date-updated', '2025-06-10');
        $element->setMetadata($metadata);

        $title = new Title();
        $title->setBody('2024 International Building Code');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $frontMatter = new FrontMatter();
        $frontMatter->setTitlePage(new TitlePage());
        $element->setFrontMatter($frontMatter);

        $chapter1 = new Level();
        $chapter1->setId('chapter1');
        $element->addChild($chapter1);

        $chapter2 = new Level();
        $chapter2->setId('chapter2');
        $element->addChild($chapter2);

        $index = new Index();
        $index->setId('index');
        $backMatter = new BackMatter();
        $backMatter->setChildren([$index]);
        $element->setBackMatter($backMatter);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
