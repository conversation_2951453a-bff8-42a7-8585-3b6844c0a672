<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Meta;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Mapper\MetadataMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\MetadataMapper
 */
class MetadataMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Metadata::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Metadata::class]);
        $this->assertInstanceOf(MetadataMapper::class, $service->classMap[Metadata::class][0]);

        $this->assertArrayHasKey(MetadataMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[MetadataMapper::elementName()]);
        $this->assertInstanceOf(MetadataMapper::class, $service->elementMap[MetadataMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Metadata $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<metadata %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Metadata();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Metadata $element, string $innerXml): void
    {
        $xml = sprintf('<metadata xmlns="%s" xmlns:m="%s">%s</metadata>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Metadata();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $meta1 = new Meta();
        $meta1->setName('schematron-ruleset');
        $meta1->setBody('I-Code');
        $element->setChildren([$meta1]);
        $innerXml .= '<meta name="schematron-ruleset">I-Code</meta>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $meta2 = new Meta();
        $meta2->setName('title');
        $meta2->setBody('2024 International Building Code');
        $element->setChildren([$meta1, $meta2]);
        $innerXml .= '<meta name="title">2024 International Building Code</meta>';
        yield '2 children' => [deep_copy($element), $innerXml];

        $meta3 = new Meta();
        $meta3->setName('edition');
        $meta3->setBody('2024 Edition');
        $element->setChildren([$meta1, $meta2, $meta3]);
        $innerXml .= '<meta name="edition">2024 Edition</meta>';
        yield '3 children' => [deep_copy($element), $innerXml];
    }
}
