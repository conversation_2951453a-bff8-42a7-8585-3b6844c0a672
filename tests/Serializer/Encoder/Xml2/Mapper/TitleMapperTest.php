<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Mapper\TitleMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\TitleMapper
 */
class TitleMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Title::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Title::class]);
        $this->assertInstanceOf(TitleMapper::class, $service->classMap[Title::class][0]);

        $this->assertArrayHasKey(TitleMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[TitleMapper::elementName()]);
        $this->assertInstanceOf(TitleMapper::class, $service->elementMap[TitleMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Title $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<title %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Title();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTitleAbbreviation('abbr');
        $attrs['title-abbreviation'] = 'abbr';
        yield 'title-abbreviation' => [deep_copy($element), $attrs];

        $element->setTitleYear('year');
        $attrs['title-year'] = 'year';
        yield 'title-year' => [deep_copy($element), $attrs];
    }

    /** @dataProvider bodyCases */
    public function testBody(Title $element, string $innerXml): void
    {
        $xml = sprintf('<title xmlns="%s" xmlns:m="%s">%s</title>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new Title();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('Applicability');
        $innerXml = 'Applicability';
        yield 'IBC2024V2.0_Ch01_SubCh01_Sec102' => [deep_copy($element), $innerXml];
    }
}
