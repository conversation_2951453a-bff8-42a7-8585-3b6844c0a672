<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Source;
use App\Serializer\Encoder\Xml2\Mapper\SourceMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SourceMapper
 */
class SourceMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Source::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Source::class]);
        $this->assertInstanceOf(SourceMapper::class, $service->classMap[Source::class][0]);

        $this->assertArrayHasKey(SourceMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SourceMapper::elementName()]);
        $this->assertInstanceOf(SourceMapper::class, $service->elementMap[SourceMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Source $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<source %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Source();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Source $element, string $innerXml): void
    {
        $xml = sprintf('<source xmlns="%s" xmlns:m="%s">%s</source>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Source();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('<insert>source</insert>');
        $innerXml .= '<insert>source</insert>';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
