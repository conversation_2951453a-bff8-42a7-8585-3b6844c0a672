<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Mapper\BodyMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\BodyMapper
 */
class BodyMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Body::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Body::class]);
        $this->assertInstanceOf(BodyMapper::class, $service->classMap[Body::class][0]);

        $this->assertArrayHasKey(BodyMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[BodyMapper::elementName()]);
        $this->assertInstanceOf(BodyMapper::class, $service->elementMap[BodyMapper::elementName()][0]);
    }

    /** @dataProvider fieldCases */
    public function testFields(Body $element, string $innerXml): void
    {
        $xml = sprintf('<body xmlns="%s" xmlns:m="%s">%s</body>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Body();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('<p>Body</p>');
        $innerXml .= '<p>Body</p>';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
