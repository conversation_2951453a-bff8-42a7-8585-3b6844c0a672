<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\SuperTitle;
use App\Serializer\Encoder\Xml2\Mapper\SuperTitleMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SuperTitleMapper
 */
class SuperTitleMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(SuperTitle::class, $service->classMap);
        $this->assertIsCallable($service->classMap[SuperTitle::class]);
        $this->assertInstanceOf(SuperTitleMapper::class, $service->classMap[SuperTitle::class][0]);

        $this->assertArrayHasKey(SuperTitleMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SuperTitleMapper::elementName()]);
        $this->assertInstanceOf(SuperTitleMapper::class, $service->elementMap[SuperTitleMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(SuperTitle $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<super-title %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new SuperTitle();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider bodyCases */
    public function testBody(SuperTitle $element, string $innerXml): void
    {
        $xml = sprintf('<super-title xmlns="%s" xmlns:m="%s">%s</super-title>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new SuperTitle();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('Part 2—Administration and Enforcement');
        $innerXml = 'Part 2—Administration and Enforcement';
        yield 'IBC2024V2.0_Ch01_SubCh02_Sec103' => [deep_copy($element), $innerXml];
    }
}
