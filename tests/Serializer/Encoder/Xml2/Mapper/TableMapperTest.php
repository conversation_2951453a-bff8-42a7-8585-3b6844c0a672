<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\Enum\Rules;
use App\Serializer\Encoder\Xml2\Element\Caption;
use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Source;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\TableNotes;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\TableMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\TableMapper
 */
class TableMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Table::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Table::class]);
        $this->assertInstanceOf(TableMapper::class, $service->classMap[Table::class][0]);

        $this->assertArrayHasKey(TableMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[TableMapper::elementName()]);
        $this->assertInstanceOf(TableMapper::class, $service->elementMap[TableMapper::elementName()][0]);
    }

    /**
     * @dataProvider attributeCases
     * @dataProvider calsAttributeCases
     * @dataProvider htmlAttributeCases
     */
    public function testAttributes(Table $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<formal-table %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Table();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        $this->commonAttributeCases($element, $attrs);
        $this->revisionAttributeCases($element, $attrs);
    }

    public function calsAttributeCases(): iterable
    {
        $element = new Table();
        $attrs = [];
        yield 'cals / baseline' => [deep_copy($element), $attrs];

        $element->setFrame(Frame::SIDES);
        $attrs['frame'] = Frame::SIDES;
        yield 'cals / frame' => [deep_copy($element), $attrs];

        $element->setOrientation(Orientation::LANDSCAPE);
        $attrs['orient'] = Orientation::LANDSCAPE;
        yield 'cals / orient' => [deep_copy($element), $attrs];

        $element->setColumnSeparator(true);
        $attrs['colsep'] = '1';
        yield 'cals / colsep' => [deep_copy($element), $attrs];

        $element->setRowSeparator(true);
        $attrs['rowsep'] = '1';
        yield 'cals / rowsep' => [deep_copy($element), $attrs];

        $element->setFloat(FloatEnum::TOP);
        $attrs['float'] = FloatEnum::TOP;
        yield 'cals / float' => [deep_copy($element), $attrs];

        $element->setBackgroundColor('#fff');
        $attrs['background-color'] = '#fff';
        yield 'cals / background-color' => [deep_copy($element), $attrs];

        $element->setTableStyle('tabstyle');
        $attrs['tabstyle'] = 'tabstyle';
        yield 'cals / tabstyle' => [deep_copy($element), $attrs];

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'cals / tocentry = yes' => [deep_copy($element), $attrs];

        $element->setPageWide(true);
        $attrs['pgwide'] = 'yes';
        yield 'cals / pgwide = yes' => [deep_copy($element), $attrs];
    }

    public function htmlAttributeCases(): iterable
    {
        $element = new Table();
        $attrs = [];
        yield 'html / baseline' => [deep_copy($element), $attrs];

        $element->setFrame(Frame::LEFT_HAND_SIDE);
        $attrs['frame'] = Frame::LEFT_HAND_SIDE;
        yield 'html / frame' => [deep_copy($element), $attrs];

        $element->setClass('class');
        $attrs['class'] = 'class';
        yield 'html / class' => [deep_copy($element), $attrs];

        $element->setTitleAttr('title');
        $attrs['title'] = 'title';
        yield 'html / title' => [deep_copy($element), $attrs];

        $element->setSummary('summary');
        $attrs['summary'] = 'summary';
        yield 'html / summary' => [deep_copy($element), $attrs];

        $element->setWidth('123');
        $attrs['width'] = '123';
        yield 'html / width' => [deep_copy($element), $attrs];

        $element->setBorder('123');
        $attrs['border'] = '123';
        yield 'html / border' => [deep_copy($element), $attrs];

        $element->setCellSpacing(123);
        $attrs['cellspacing'] = '123';
        yield 'html / cellspacing' => [deep_copy($element), $attrs];

        $element->setCellPadding(123);
        $attrs['cellpadding'] = '123';
        yield 'html / cellpadding' => [deep_copy($element), $attrs];

        $element->setRules(Rules::ALL);
        $attrs['rules'] = Rules::ALL;
        yield 'html / rules' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Table $element, string $innerXml): void
    {
        $xml = sprintf('<formal-table xmlns="%s" xmlns:m="%s">%s</formal-table>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Table();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setQrCode(new QrCode());
        $innerXml .= '<QR-code display="no" levelref="" purpose="change"><short-code short-url=""/></QR-code>';
        yield 'QR-code' => [deep_copy($element), $innerXml];

        $element->setBody('<table/>');
        $innerXml .= '<table/>';
        yield 'table' => [deep_copy($element), $innerXml];

        $element->setCaption(new Caption());
        $innerXml .= '<caption/>';
        yield 'caption' => [deep_copy($element), $innerXml];

        $element->setLegend('<legend>1</legend><legend>2</legend>');
        $innerXml .= '<legend>1</legend><legend>2</legend>';
        yield 'legend' => [deep_copy($element), $innerXml];

        $element->setTableNotes(new TableNotes());
        $innerXml .= '<table-notes/>';
        yield 'table-notes' => [deep_copy($element), $innerXml];

        $element->setSource(new Source());
        $innerXml .= '<source/>';
        yield 'source' => [deep_copy($element), $innerXml];

        $innerXml .= '<credit/>';
        $element->setCredit(new Credit());
        yield 'credit' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<formal-table
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
    frame="all"
    orient="land"
    colsep="1"
    rowsep="1"
    float="margin"
    background-color="background-color"
    tabstyle="tabstyle"
    tocentry="yes"
    pgwide="yes"
    class="class"
    title="title"
    summary="summary"
    width="width"
    border="border"
    cellspacing="10"
    cellpadding="10"
    rules="cols"
>
    <titlegroup>
        <committee-desig>[F]</committee-desig>
        <label>TABLE</label>
        <number>307.1(2)</number>
        <title>MAXIMUM ALLOWABLE QUANTITY PER CONTROL AREA OF HAZARDOUS MATERIALS POSING A HEALTH HAZARD<sup>a, c, h, i</sup></title>
    </titlegroup>
    <QR-code display="no" levelref="" purpose="change"><short-code short-url=""/></QR-code>
    <table/>
    <table/>
    <mediaobject/>
    <mediaobject-group/>
    <caption/>
    <legend>1</legend><legend>2</legend><legend>3</legend>
    <table-notes/>
    <source/>
    <credit/>
</formal-table>
XML;
        $element = new Table();
        $element->setId('id');
        $element->setCtUuid('uuid');
        $element->setFrame(Frame::ALL);
        $element->setOrientation(Orientation::LANDSCAPE);
        $element->setColumnSeparator(true);
        $element->setRowSeparator(true);
        $element->setFloat(FloatEnum::MARGIN);
        $element->setBackgroundColor('background-color');
        $element->setTableStyle('tabstyle');
        $element->setTocEntry(true);
        $element->setPageWide(true);
        $element->setClass('class');
        $element->setTitleAttr('title');
        $element->setSummary('summary');
        $element->setWidth('width');
        $element->setBorder('border');
        $element->setCellSpacing(10);
        $element->setCellPadding(10);
        $element->setRules(Rules::COLS);

        $cd = new CommitteeDesignation();
        $cd->setBody('[F]');
        $label = new Label();
        $label->setBody('TABLE');
        $number = new Number();
        $number->setBody('307.1(2)');
        $title = new Title();
        $title->setBody('MAXIMUM ALLOWABLE QUANTITY PER CONTROL AREA OF HAZARDOUS MATERIALS POSING A HEALTH HAZARD<sup>a, c, h, i</sup>');
        $titleGroup = new TitleGroup();
        $titleGroup->setCommitteeDesignation($cd);
        $titleGroup->setLabel($label);
        $titleGroup->setNumber($number);
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $element->setQrCode(new QrCode());
        $element->setBody('<table/><table/><mediaobject/><mediaobject-group/>');
        $element->setCaption(new Caption());
        $element->setLegend('<legend>1</legend><legend>2</legend><legend>3</legend>');
        $element->setTableNotes(new TableNotes());
        $element->setSource(new Source());
        $element->setCredit(new Credit());

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function testTableInSection(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<level-1
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
    role="legis-section"
>
    <section-body>
        <formal-table
            id="table1"
            ct-uuid="table1.uuid"
        ></formal-table>
        <formal-table
            id="table2"
            ct-uuid="table2.uuid"
        ></formal-table>
    </section-body>
</level-1>
XML;
        // elements
        $element = new Level();
        $element->setId('id');
        $element->setCtUuid('uuid');
        $element->setRole('legis-section');

        $body = new SectionBody();
        $element->setBody($body);

        $table1 = new Table();
        $table1->setId('table1');
        $table1->setCtUuid('table1.uuid');
        $element->addChild($table1);
        $body->addChild($table1);

        $table2 = new Table();
        $table2->setId('table2');
        $table2->setCtUuid('table2.uuid');
        $element->addChild($table2);
        $body->addChild($table2);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
