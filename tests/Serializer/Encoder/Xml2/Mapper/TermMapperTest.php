<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Mapper\TermMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\TermMapper
 */
class TermMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Term::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Term::class]);
        $this->assertInstanceOf(TermMapper::class, $service->classMap[Term::class][0]);

        $this->assertArrayHasKey(TermMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[TermMapper::elementName()]);
        $this->assertInstanceOf(TermMapper::class, $service->elementMap[TermMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Term $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<term %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Term();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Term $element, string $innerXml): void
    {
        $xml = sprintf('<term xmlns="%s" xmlns:m="%s">%s</term>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Term();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('body');
        $innerXml .= 'body';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
