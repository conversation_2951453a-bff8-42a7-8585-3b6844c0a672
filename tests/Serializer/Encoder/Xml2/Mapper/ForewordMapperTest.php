<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\ForewordMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\ForewordMapper
 */
class ForewordMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Foreword::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Foreword::class]);
        $this->assertInstanceOf(ForewordMapper::class, $service->classMap[Foreword::class][0]);

        $this->assertArrayHasKey(ForewordMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[ForewordMapper::elementName()]);
        $this->assertInstanceOf(ForewordMapper::class, $service->elementMap[ForewordMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Foreword $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<foreword %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Foreword();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Foreword $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<foreword %s>%s</foreword>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Foreword();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setBody('<p>Body</p>');
        $innerXml .= '<p>Body</p>';
        yield 'body' => [deep_copy($element), $innerXml];

        $section1 = new Section();
        $section1->setId('section1');
        $element->addChild($section1);
        $innerXml .= '<section disp-level="1" id="section1"/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $section2 = new Section();
        $section2->setId('section2');
        $element->addChild($section2);
        $innerXml .= '<section disp-level="1" id="section2"/>';
        yield '2 children' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<foreword
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
>
    <titlegroup>
        <title>Foreword</title>
    </titlegroup>
    <p>Lorem ipsum dolor sit amet, ...</p>
</foreword>
XML;
        // elements
        $element = new Foreword();
        $element->setId('id');
        $element->setCtUuid('uuid');

        $title = new Title();
        $title->setBody('Foreword');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $element->setBody('<p>Lorem ipsum dolor sit amet, ...</p>');

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
