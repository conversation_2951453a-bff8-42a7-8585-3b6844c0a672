<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\QrCode;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\AppendixMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AppendixMapper
 */
class AppendixMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Appendix::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Appendix::class]);
        $this->assertInstanceOf(AppendixMapper::class, $service->classMap[Appendix::class][0]);

        $this->assertArrayHasKey(AppendixMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[AppendixMapper::elementName()]);
        $this->assertInstanceOf(AppendixMapper::class, $service->elementMap[AppendixMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Appendix $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<appendix %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Appendix();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setIndexNumber('indexnum');
        $attrs['indexnum'] = 'indexnum';
        yield 'indexnum' => [deep_copy($element), $attrs];

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];

        $element->setTocAutoAdd(true);
        $attrs['tocautoadd'] = 'yes';
        yield 'tocautoadd' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Appendix $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<appendix %s>%s</appendix>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Appendix();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setMetadata(new Metadata());
        $innerXml .= '<metadata/>';
        yield 'metadata' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setQrCode(new QrCode());
        $innerXml .= '<QR-code display="no" levelref="" purpose="change"><short-code short-url=""/></QR-code>';
        yield 'QR-code' => [deep_copy($element), $innerXml];

        $element->setHistory(new History());
        $innerXml .= '<history/>';
        yield 'history' => [deep_copy($element), $innerXml];

        $element->setObjectives(new Objectives());
        $innerXml .= '<objectives/>';
        yield 'objectives' => [deep_copy($element), $innerXml];

        $element->setNote(new Note());
        $innerXml .= '<note/>';
        yield 'note' => [deep_copy($element), $innerXml];

        $element->setAbstract(new AbstractField());
        $innerXml .= '<abstract/>';
        yield 'abstract' => [deep_copy($element), $innerXml];

        $element->setKeywords(new Keywords());
        $innerXml .= '<keywords/>';
        yield 'keywords' => [deep_copy($element), $innerXml];

        $element->setBody(new SectionBody());
        $innerXml .= '<section-body/>';
        yield 'body' => [deep_copy($element), $innerXml];

        $child1 = new Level();
        $child1->setDisplayLevel(2);
        $element->addChild($child1);
        $innerXml .= '<level-2/>';
        yield '1 child' => [deep_copy($element), $innerXml];

        $child2 = new Level();
        $child2->setDisplayLevel(2);
        $element->addChild($child2);
        $innerXml .= '<level-2/>';
        yield '2 children' => [deep_copy($element), $innerXml];

        $child3 = new Section();
        $child3->setDisplayLevel(2);
        $element->addChild($child3);
        $innerXml .= '<section disp-level="2"/>';
        yield 'section child' => [deep_copy($element), $innerXml];

        $child4 = new RelocatedTo();
        $element->addChild($child4);
        $innerXml .= '<relocated-to/>';
        yield 'relocated-to child' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<appendix
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
    indexnum="App A"
    tocentry="yes"
    tocautoadd="yes"
>
    <metadata/>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <QR-code display="no" levelref="" purpose="change"><short-code short-url=""/></QR-code>
    <history>History</history>
    <objectives>
        <titlegroup>
            <title>Title</title>
        </titlegroup>
        <body>
            <p>Objectives</p>
        </body>
    </objectives>
    <note>
        <titlegroup>
            <title>Title</title>
        </titlegroup>
        <p>Note</p>
    </note>
    <abstract>
        <titlegroup>
            <title>Title</title>
        </titlegroup>
        <p>Abstract</p>
    </abstract>
    <keywords>
        <titlegroup>
            <title>Title</title>
        </titlegroup>
        <keyword>Keywords</keyword>
    </keywords>
    <level-2/>
    <section disp-level="2"/>
    <relocated-to/>
</appendix>
XML;
        // elements
        $element = new Appendix();
        $element->setId('id');
        $element->setCtUuid('uuid');
        $element->setIndexNumber('App A');
        $element->setTocEntry(true);
        $element->setTocAutoAdd(true);

        $element->setMetadata(new Metadata());

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $element->setQrCode(new QrCode());

        $history = new History();
        $history->setBody('History');
        $element->setHistory($history);

        $objectives = new Objectives();
        $objectives->setTitleGroup($titleGroup);
        $body = new Body();
        $body->setBody('<p>Objectives</p>');
        $objectives->setBody($body);
        $element->setObjectives($objectives);

        $note = new Note();
        $note->setTitleGroup($titleGroup);
        $note->setBody('<p>Note</p>');
        $element->setNote($note);

        $abstract = new AbstractField();
        $abstract->setTitleGroup($titleGroup);
        $abstract->setBody('<p>Abstract</p>');
        $element->setAbstract($abstract);

        $keywords = new Keywords();
        $keywords->setTitleGroup($titleGroup);
        $keywords->setBody('<keyword>Keywords</keyword>');
        $element->setKeywords($keywords);

        $level = new Level();
        $level->setDisplayLevel(2);
        $element->addChild($level);

        $section = new Section();
        $section->setDisplayLevel(2);
        $element->addChild($section);

        $element->addChild(new RelocatedTo());

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
