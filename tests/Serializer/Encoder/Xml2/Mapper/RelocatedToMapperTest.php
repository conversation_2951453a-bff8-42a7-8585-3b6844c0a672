<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\Align;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Mapper\RelocatedToMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\RelocatedToMapper
 */
class RelocatedToMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(RelocatedTo::class, $service->classMap);
        $this->assertIsCallable($service->classMap[RelocatedTo::class]);
        $this->assertInstanceOf(RelocatedToMapper::class, $service->classMap[RelocatedTo::class][0]);

        $this->assertArrayHasKey(RelocatedToMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[RelocatedToMapper::elementName()]);
        $this->assertInstanceOf(RelocatedToMapper::class, $service->elementMap[RelocatedToMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(RelocatedTo $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<relocated-to %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new RelocatedTo();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        $this->commonAttributeCases($element, $attrs);
        $this->revisionAttributeCases($element, $attrs);

        $element->setIndent(true);
        $attrs['indent'] = 'yes';
        yield 'indent' => [deep_copy($element), $attrs];

        $element->setAlign(Align::JUSTIFY);
        $attrs['align'] = Align::JUSTIFY;
        yield 'align' => [deep_copy($element), $attrs];

        $element->setReferenceId('rid');
        $attrs['rid'] = 'rid';
        yield 'rid' => [deep_copy($element), $attrs];

        $element->setRelocatedTo('relocated-to');
        $attrs['relocated-to'] = 'relocated-to';
        yield 'relocated-to' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(RelocatedTo $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<relocated-to %s>%s</relocated-to>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new RelocatedTo();
        yield 'baseline' => [deep_copy($element), ''];

        $element->setBody('body');
        yield 'body' => [deep_copy($element), 'body'];
    }

    /** @dataProvider realCases */
    public function testRealCases(RelocatedTo $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $element = new RelocatedTo();
        $element->setId('id');
        $element->setRelocatedTo('relocated-to');
        $element->setIndent(true);
        $element->setAlign('justify');
        $element->setReferenceId('rid');
        $element->setBody('body only');
        $xml = <<<XMLFRAG
<relocated-to
    id="id"
    relocated-to="relocated-to"
    indent="yes"
    align="justify"
    rid="rid"
    %s
    >body only</relocated-to>
XMLFRAG;
        yield 'all fields' => [$element, $xml];

        $element = new RelocatedTo();
        $element->setId('Original-IMC2024V1.0_Ch05_Sec506.3.2');
        $element->setRelocatedTo('Relocated-from-IMC2024V1.0_Ch05_Sec506.3.2');
        $element->setReferenceId('IMC2024V1.0_Ch05_Sec508.1.2');
        $element->setBody('Section before 506.3.2 Joints, seams, and penetrations of grease ducts, relocated to 508.1.2');
        $xml = <<<XMLFRAG
<relocated-to
    id="Original-IMC2024V1.0_Ch05_Sec506.3.2"
    relocated-to="Relocated-from-IMC2024V1.0_Ch05_Sec506.3.2"
    rid="IMC2024V1.0_Ch05_Sec508.1.2"
    %s
>Section before 506.3.2 Joints, seams, and penetrations of grease ducts, relocated to 508.1.2</relocated-to>
XMLFRAG;
        yield 'IMC2024V1.0_Ch05_Sec508.1.2' => [$element, $xml];
    }
}
