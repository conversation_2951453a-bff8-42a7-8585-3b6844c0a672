<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Email;
use App\Serializer\Encoder\Xml2\Mapper\EmailMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\EmailMapper
 */
class EmailMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Email::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Email::class]);
        $this->assertInstanceOf(EmailMapper::class, $service->classMap[Email::class][0]);

        $this->assertArrayHasKey(EmailMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[EmailMapper::elementName()]);
        $this->assertInstanceOf(EmailMapper::class, $service->elementMap[EmailMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Email $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<email %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Email();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setMailTo('mailto');
        $attrs['mailto'] = 'mailto';
        yield 'mailto' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Email $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<email %s>%s</email>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Email();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('<EMAIL>');
        $innerXml .= '<EMAIL>';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
