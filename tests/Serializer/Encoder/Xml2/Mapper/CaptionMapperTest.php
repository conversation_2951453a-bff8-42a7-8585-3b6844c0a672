<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Caption;
use App\Serializer\Encoder\Xml2\Mapper\CaptionMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\CaptionMapper
 */
class CaptionMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Caption::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Caption::class]);
        $this->assertInstanceOf(CaptionMapper::class, $service->classMap[Caption::class][0]);

        $this->assertArrayHasKey(CaptionMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[CaptionMapper::elementName()]);
        $this->assertInstanceOf(CaptionMapper::class, $service->elementMap[CaptionMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Caption $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<caption %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Caption();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Caption $element, string $innerXml): void
    {
        $xml = sprintf('<caption xmlns="%s" xmlns:m="%s">%s</caption>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Caption();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('<p>caption</p>');
        $innerXml .= '<p>caption</p>';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
