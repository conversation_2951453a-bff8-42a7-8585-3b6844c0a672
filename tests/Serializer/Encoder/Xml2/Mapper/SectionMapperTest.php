<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\SectionMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;
use function str_replace;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SectionMapper
 */
class SectionMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Section::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Section::class]);
        $this->assertInstanceOf(SectionMapper::class, $service->classMap[Section::class][0]);

        $this->assertArrayHasKey(SectionMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SectionMapper::elementName()]);
        $this->assertInstanceOf(SectionMapper::class, $service->elementMap[SectionMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Section $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<section %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Section();
        $attrs = ['disp-level' => '1'];
        yield 'baseline' => [deep_copy($element), $attrs];

        // commented out due to forced 'role'
        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setIndexNumber('index number');
        $attrs['indexnum'] = 'index number';
        yield 'indexnum' => [deep_copy($element), $attrs];

        $element->setDisplayLevel(3);
        $attrs['disp-level'] = '3';
        yield 'disp-level' => [deep_copy($element), $attrs];

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];

        $element->setReserveCount(10);
        $attrs['reservecount'] = '10';
        yield 'reservecount' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Section $element, string $innerXml): void
    {
        $xml = sprintf('<section disp-level="1" xmlns="%s" xmlns:m="%s">%s</section>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Section();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setAbstract(new AbstractField());
        $innerXml .= '<abstract/>';
        yield 'abstract' => [deep_copy($element), $innerXml];

        $element->setKeywords(new Keywords());
        $innerXml .= '<keywords/>';
        yield 'keywords' => [deep_copy($element), $innerXml];

        $body = new SectionBody();
        $element->setBody($body);
        $innerXml .= '<section-body></section-body>';
        yield 'body' => [deep_copy($element), $innerXml];

        $figure = new Figure();
        $figure->setId('figure');
        $element->addChild($figure);
        $body->addChild($figure);
        $innerXml = str_replace('</section-body>', '<figure id="figure"/></section-body>', $innerXml);
        yield 'figure' => [deep_copy($element), $innerXml];

        $table = new Table();
        $table->setId('table');
        $element->addChild($table);
        $body->addChild($table);
        $innerXml = str_replace('</section-body>', '<formal-table id="table"/></section-body>', $innerXml);
        yield 'table' => [deep_copy($element), $innerXml];

        $child1 = new Section();
        $child1->setId('section1');
        $element->addChild($child1);
        $innerXml .= '<section id="section1" disp-level="1"/>';
        yield 'child 1' => [deep_copy($element), $innerXml];

        $child2 = new Section();
        $child2->setId('section2');
        $element->addChild($child2);
        $innerXml .= '<section id="section2" disp-level="1"/>';
        yield 'child 2' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<section
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
    role="legis-section"
    indexnum="Ch 1"
    tocentry="yes"
    reservecount="1"
    disp-level="1"
>
    <titlegroup>
        <label>Chapter</label>
        <number>1</number>
        <title>Scope and Administration</title>
    </titlegroup>
    <section-body>
        <p>Body</p>
        <figure id="figure"/>
        <formal-table id="table"/>
    </section-body>
    <section role="legis-section" id="child1" disp-level="2"/>
    <section role="legis-section" id="child2" disp-level="2"/>
</section>
XML;
        $element = new Section();
        $element->setId('id');
        $element->setCtUuid('uuid');
        $element->setRole('legis-section');
        $element->setIndexNumber('Ch 1');
        $element->setTocEntry(true);
        $element->setReserveCount(1);
        $element->setDisplayLevel(1);

        $label = new Label();
        $label->setBody('Chapter');
        $number = new Number();
        $number->setBody('1');
        $title = new Title();
        $title->setBody('Scope and Administration');
        $titleGroup = new TitleGroup();
        $titleGroup->setLabel($label);
        $titleGroup->setNumber($number);
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $body = new SectionBody();
        $body->setBody('<p>Body</p>');
        $element->setBody($body);

        $figure = new Figure();
        $figure->setId('figure');
        $element->addChild($figure);
        $body->addChild($figure);

        $table = new Table();
        $table->setId('table');
        $element->addChild($table);
        $body->addChild($table);

        $child1 = new Section();
        $child1->setRole('legis-section');
        $child1->setId('child1');
        $child1->setDisplayLevel(2);
        $element->addChild($child1);

        $child2 = new Section();
        $child2->setRole('legis-section');
        $child2->setId('child2');
        $child2->setDisplayLevel(2);
        $element->addChild($child2);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
