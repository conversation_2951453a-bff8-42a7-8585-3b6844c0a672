<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Correlated;
use App\Serializer\Encoder\Xml2\Mapper\CorrelatedMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\CorrelatedMapper
 */
class CorrelatedMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Correlated::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Correlated::class]);
        $this->assertInstanceOf(CorrelatedMapper::class, $service->classMap[Correlated::class][0]);

        $this->assertArrayHasKey(CorrelatedMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[CorrelatedMapper::elementName()]);
        $this->assertInstanceOf(CorrelatedMapper::class, $service->elementMap[CorrelatedMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Correlated $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<correlated %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Correlated();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
        yield from $this->errataAttributeCases($element, $attrs);
    }

    /** @dataProvider bodyCases */
    public function testBody(Correlated $element, string $innerXml): void
    {
        $xml = sprintf('<correlated xmlns="%s" xmlns:m="%s">%s</correlated>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new Correlated();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('body');
        $innerXml = 'body';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
