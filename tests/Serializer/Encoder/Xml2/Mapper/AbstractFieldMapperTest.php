<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\AbstractFieldMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractFieldMapper
 */
class AbstractFieldMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(AbstractField::class, $service->classMap);
        $this->assertIsCallable($service->classMap[AbstractField::class]);
        $this->assertInstanceOf(AbstractFieldMapper::class, $service->classMap[AbstractField::class][0]);

        $this->assertArrayHasKey(AbstractFieldMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[AbstractFieldMapper::elementName()]);
        $this->assertInstanceOf(AbstractFieldMapper::class, $service->elementMap[AbstractFieldMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(AbstractField $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<abstract %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new AbstractField();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        $this->commonAttributeCases($element, $attrs);
        $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(AbstractField $element, string $innerXml): void
    {
        $xml = sprintf('<abstract xmlns="%s" xmlns:m="%s">%s</abstract>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new AbstractField();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $body = <<<XMLFRAG
<p>Body</p>
<exception><p>Exception</p></exception>
XMLFRAG;
        $element->setBody($body);
        $innerXml .= $body;
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
