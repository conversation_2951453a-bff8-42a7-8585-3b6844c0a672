<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\FigureNotes;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\FigureNotesMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\FigureNotesMapper
 */
class FigureNotesMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(FigureNotes::class, $service->classMap);
        $this->assertIsCallable($service->classMap[FigureNotes::class]);
        $this->assertInstanceOf(FigureNotesMapper::class, $service->classMap[FigureNotes::class][0]);

        $this->assertArrayHasKey(FigureNotesMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[FigureNotesMapper::elementName()]);
        $this->assertInstanceOf(FigureNotesMapper::class, $service->elementMap[FigureNotesMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(FigureNotes $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<figure-notes %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new FigureNotes();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(FigureNotes $element, string $innerXml): void
    {
        $xml = sprintf('<figure-notes xmlns="%s" xmlns:m="%s">%s</figure-notes>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new FigureNotes();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $body = <<<XMLFRAG
<figure-note><p>note 1</p></figure-note>
<figure-note><p>note 2</p></figure-note>
<figure-note><p>note 3</p></figure-note>
XMLFRAG;
        $element->setBody($body);
        $innerXml .= $body;
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
