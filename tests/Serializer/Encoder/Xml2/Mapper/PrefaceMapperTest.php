<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\PrefaceMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\PrefaceMapper
 */
class PrefaceMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Preface::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Preface::class]);
        $this->assertInstanceOf(PrefaceMapper::class, $service->classMap[Preface::class][0]);

        $this->assertArrayHasKey(PrefaceMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[PrefaceMapper::elementName()]);
        $this->assertInstanceOf(PrefaceMapper::class, $service->elementMap[PrefaceMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Preface $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<preface %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Preface();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'tocentry' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Preface $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<preface %s>%s</preface>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Preface();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setBody('<p>Body</p>');
        $innerXml .= '<p>Body</p>';
        yield 'body' => [deep_copy($element), $innerXml];

        $section1 = new Section();
        $element->addChild($section1);
        $innerXml .= '<section disp-level="1"/>';
        yield 'section1' => [deep_copy($element), $innerXml];

        $section2 = new Section();
        $element->addChild($section2);
        $innerXml .= '<section disp-level="1"/>';
        yield 'section2' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<preface
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
>
    <titlegroup>
        <title>Preface</title>
    </titlegroup>
    <p>Body</p>
    <section disp-level="1" id="IBC2024V2.0_PREFACE_FC"/>
    <section disp-level="1" id="IBC2024V2.0_PREFACE_MM"/>
</preface>
XML;
        // elements
        $element = new Preface();
        $element->setId('id');
        $element->setCtUuid('uuid');

        $title = new Title();
        $title->setBody('Preface');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $element->setBody('<p>Body</p>');

        $section1 = new Section();
        $section1->setId('IBC2024V2.0_PREFACE_FC');
        $element->addChild($section1);

        $section1 = new Section();
        $section1->setId('IBC2024V2.0_PREFACE_MM');
        $element->addChild($section1);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
