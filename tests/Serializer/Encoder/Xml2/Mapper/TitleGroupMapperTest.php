<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Element\Correlated;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Element\SubTitle;
use App\Serializer\Encoder\Xml2\Element\SuperTitle;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\TitleGroupMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\TitleGroupMapper
 */
class TitleGroupMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(TitleGroup::class, $service->classMap);
        $this->assertIsCallable($service->classMap[TitleGroup::class]);
        $this->assertInstanceOf(TitleGroupMapper::class, $service->classMap[TitleGroup::class][0]);

        $this->assertArrayHasKey(TitleGroupMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[TitleGroupMapper::elementName()]);
        $this->assertInstanceOf(TitleGroupMapper::class, $service->elementMap[TitleGroupMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(TitleGroup $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<titlegroup %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new TitleGroup();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(TitleGroup $element, string $innerXml): void
    {
        $xml = sprintf('<titlegroup xmlns="%s" xmlns:m="%s">%s</titlegroup>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new TitleGroup();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setSuperTitle(new SuperTitle());
        $innerXml .= '<super-title/>';
        yield 'super-title' => [deep_copy($element), $innerXml];

        $element->setCommitteeDesignation(new CommitteeDesignation());
        $innerXml .= '<committee-desig/>';
        yield 'committee-desig' => [deep_copy($element), $innerXml];

        $element->setLabel(new Label());
        $innerXml .= '<label/>';
        yield 'label' => [deep_copy($element), $innerXml];

        $element->setNumber(new Number());
        $innerXml .= '<number/>';
        yield 'number' => [deep_copy($element), $innerXml];

        $element->setCorrelated(new Correlated());
        $innerXml .= '<correlated/>';
        yield 'correlated' => [deep_copy($element), $innerXml];

        $element->setTitle(new Title());
        $innerXml .= '<title/>';
        yield 'title' => [deep_copy($element), $innerXml];

        $element->setSubTitle(new SubTitle());
        $innerXml .= '<sub-title/>';
        yield 'sub-title' => [deep_copy($element), $innerXml];

        $element->setHistory(new History());
        $innerXml .= '<history/>';
        yield 'history' => [deep_copy($element), $innerXml];
    }

    /** @dataProvider realCases */
    public function testRealCases(TitleGroup $element, string $innerXml): void
    {
        $xml = sprintf('<titlegroup xmlns="%s" xmlns:m="%s">%s</titlegroup>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $innerXml = <<<XMLFRAG
<committee-desig>[A]</committee-desig>
<number>102.4</number>
<title>Referenced codes and standards.</title>
XMLFRAG;
        $cd = new CommitteeDesignation();
        $cd->setBody('[A]');
        $number = new Number();
        $number->setBody('102.4');
        $title = new Title();
        $title->setBody('Referenced codes and standards.');

        $element = new TitleGroup();
        $element->setCommitteeDesignation($cd);
        $element->setNumber($number);
        $element->setTitle($title);
        yield 'IBC2024V2.0_Ch01_SubCh01_Sec102.4' => [$element, $innerXml];

        $innerXml = <<<XMLFRAG
<super-title>Part 2—Administration and Enforcement</super-title>
<label>Section</label>
<number>103</number>
<title>Code Compliance Agency</title>
XMLFRAG;
        $superTitle = new SuperTitle();
        $superTitle->setBody('Part 2—Administration and Enforcement');
        $label = new Label();
        $label->setBody('Section');
        $number = new Number();
        $number->setBody('103');
        $title = new Title();
        $title->setBody('Code Compliance Agency');

        $element = new TitleGroup();
        $element->setSuperTitle($superTitle);
        $element->setLabel($label);
        $element->setNumber($number);
        $element->setTitle($title);
        yield 'IBC2024V2.0_Ch01_SubCh02_Sec103' => [$element, $innerXml];

        $innerXml = <<<XMLFRAG
<title>2024 INTERNATIONAL CODE COUNCIL<br/>PERFORMANCE CODE<sup>®</sup><br/>FOR BUILDINGS AND FACILITIES</title>
<sub-title>USER’S GUIDE</sub-title>
XMLFRAG;
        $title = new Title();
        $title->setBody('2024 INTERNATIONAL CODE COUNCIL<br/>PERFORMANCE CODE<sup>®</sup><br/>FOR BUILDINGS AND FACILITIES');
        $subTitle = new SubTitle();
        $subTitle->setBody('USER’S GUIDE');

        $element = new TitleGroup();
        $element->setTitle($title);
        $element->setSubTitle($subTitle);
        yield 'ICPCC2024V2.0_TitlePage' => [$element, $innerXml];
    }
}
