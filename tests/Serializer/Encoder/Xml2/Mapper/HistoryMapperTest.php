<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Mapper\HistoryMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\HistoryMapper
 */
class HistoryMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(History::class, $service->classMap);
        $this->assertIsCallable($service->classMap[History::class]);
        $this->assertInstanceOf(HistoryMapper::class, $service->classMap[History::class][0]);

        $this->assertArrayHasKey(HistoryMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[HistoryMapper::elementName()]);
        $this->assertInstanceOf(HistoryMapper::class, $service->elementMap[HistoryMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(History $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<history %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new History();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(History $element, string $innerXml): void
    {
        $xml = sprintf('<history xmlns="%s" xmlns:m="%s">%s</history>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new History();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('body');
        $innerXml = 'body';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
