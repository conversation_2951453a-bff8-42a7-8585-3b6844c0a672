<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Definition;
use App\Serializer\Encoder\Xml2\Mapper\DefinitionMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\DefinitionMapper
 */
class DefinitionMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Definition::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Definition::class]);
        $this->assertInstanceOf(DefinitionMapper::class, $service->classMap[Definition::class][0]);

        $this->assertArrayHasKey(DefinitionMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[DefinitionMapper::elementName()]);
        $this->assertInstanceOf(DefinitionMapper::class, $service->elementMap[DefinitionMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Definition $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<def %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Definition();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Definition $element, string $innerXml): void
    {
        $xml = sprintf('<def xmlns="%s" xmlns:m="%s">%s</def>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Definition();
        yield 'baseline' => [deep_copy($element), ''];

        $body = '<p>def</p>';
        $element->setBody($body);
        yield 'body' => [deep_copy($element), $body];

        $body = <<<XMLFRAG
<p>definition</p>
<def-list>
    <def-item>
        <term>sub term</term>
        <definition>sub def</definition>
    </def-item>
</def-list>
XMLFRAG;
        $element->setBody($body);
        yield 'body w/ subterm' => [deep_copy($element), $body];
    }
}
