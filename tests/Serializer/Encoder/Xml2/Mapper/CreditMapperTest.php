<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Credit;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\CreditMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\CreditMapper
 */
class CreditMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Credit::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Credit::class]);
        $this->assertInstanceOf(CreditMapper::class, $service->classMap[Credit::class][0]);

        $this->assertArrayHasKey(CreditMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[CreditMapper::elementName()]);
        $this->assertInstanceOf(CreditMapper::class, $service->elementMap[CreditMapper::elementName()][0]);
    }

    /** @dataProvider fieldCases */
    public function testFields(Credit $element, string $innerXml): void
    {
        $xml = sprintf('<credit xmlns="%s" xmlns:m="%s">%s</credit>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Credit();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $body = <<<XMLFRAGMENT
<p>credit 1</p>
<p>credit 2</p>
<p>credit 3</p>
XMLFRAGMENT;
        $element->setBody($body);
        $innerXml .= $body;
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
