<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CommitteeDesignation;
use App\Serializer\Encoder\Xml2\Mapper\CommitteeDesignationMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\CommitteeDesignationMapper
 */
class CommitteeDesignationMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(CommitteeDesignation::class, $service->classMap);
        $this->assertIsCallable($service->classMap[CommitteeDesignation::class]);
        $this->assertInstanceOf(CommitteeDesignationMapper::class, $service->classMap[CommitteeDesignation::class][0]);

        $this->assertArrayHasKey(CommitteeDesignationMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[CommitteeDesignationMapper::elementName()]);
        $this->assertInstanceOf(CommitteeDesignationMapper::class, $service->elementMap[CommitteeDesignationMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(CommitteeDesignation $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<committee-desig %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new CommitteeDesignation();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setResponsibleFor('responsible-for');
        $attrs['responsible-for'] = 'responsible-for';
        yield 'responsible-for' => [deep_copy($element), $attrs];
    }

    /** @dataProvider bodyCases */
    public function testBody(CommitteeDesignation $element, string $innerXml): void
    {
        $xml = sprintf('<committee-desig xmlns="%s" xmlns:m="%s">%s</committee-desig>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new CommitteeDesignation();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('[A]');
        $innerXml = '[A]';
        yield 'IBC2024V2.0_Ch01_SubCh01_Sec101.1' => [deep_copy($element), $innerXml];
    }
}
