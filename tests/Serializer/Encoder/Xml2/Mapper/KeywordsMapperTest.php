<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\KeywordsMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\KeywordsMapper
 */
class KeywordsMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Keywords::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Keywords::class]);
        $this->assertInstanceOf(KeywordsMapper::class, $service->classMap[Keywords::class][0]);

        $this->assertArrayHasKey(KeywordsMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[KeywordsMapper::elementName()]);
        $this->assertInstanceOf(KeywordsMapper::class, $service->elementMap[KeywordsMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Keywords $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<keywords %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Keywords();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Keywords $element, string $innerXml): void
    {
        $xml = sprintf('<keywords xmlns="%s" xmlns:m="%s">%s</keywords>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Keywords();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $body = <<<XMLFRAG
<keyword>keyword</keyword>
<keyword>keyword2</keyword>
XMLFRAG;
        $element->setBody($body);
        $innerXml .= $body;
        yield 'body' => [deep_copy($element), $innerXml];
    }

    /** @dataProvider realCases */
    public function testRealCases(Keywords $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $xml = <<<XMLFRAG
<keywords %s>
    <keyword>...</keyword>
</keywords>
XMLFRAG;
        $element = new Keywords();
        $element->setBody('<keyword>...</keyword>');
        yield 'fake real' => [deep_copy($element), $xml];
    }
}
