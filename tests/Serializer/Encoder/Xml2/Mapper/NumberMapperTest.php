<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Number;
use App\Serializer\Encoder\Xml2\Mapper\NumberMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\NumberMapper
 */
class NumberMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Number::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Number::class]);
        $this->assertInstanceOf(NumberMapper::class, $service->classMap[Number::class][0]);

        $this->assertArrayHasKey(NumberMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[NumberMapper::elementName()]);
        $this->assertInstanceOf(NumberMapper::class, $service->elementMap[NumberMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Number $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<number %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Number();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider bodyCases */
    public function testBody(Number $element, string $innerXml): void
    {
        $xml = sprintf('<number xmlns="%s" xmlns:m="%s">%s</number>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new Number();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('102');
        $innerXml = '102';
        yield 'IBC2024V2.0_Ch01_SubCh01_Sec102' => [deep_copy($element), $innerXml];
    }
}
