<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\IndexDivisionMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\IndexDivisionMapper
 */
class IndexDivisionMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(IndexDivision::class, $service->classMap);
        $this->assertIsCallable($service->classMap[IndexDivision::class]);
        $this->assertInstanceOf(IndexDivisionMapper::class, $service->classMap[IndexDivision::class][0]);

        $this->assertArrayHasKey(IndexDivisionMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[IndexDivisionMapper::elementName()]);
        $this->assertInstanceOf(IndexDivisionMapper::class, $service->elementMap[IndexDivisionMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(IndexDivision $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<indexdiv %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new IndexDivision();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(IndexDivision $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<indexdiv %s>%s</indexdiv>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new IndexDivision();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->addChild(new IndexEntry());
        $innerXml .= '<index-entry/>';
        yield 'index-entry' => [deep_copy($element), $innerXml];

        $element->addChild(new IndexEntry());
        $innerXml .= '<index-entry/>';
        yield 'index-entry2' => [deep_copy($element), $innerXml];
    }
}
