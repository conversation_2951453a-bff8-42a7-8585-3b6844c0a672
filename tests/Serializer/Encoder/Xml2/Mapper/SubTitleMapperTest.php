<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\SubTitle;
use App\Serializer\Encoder\Xml2\Mapper\SubTitleMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SubTitleMapper
 */
class SubTitleMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(SubTitle::class, $service->classMap);
        $this->assertIsCallable($service->classMap[SubTitle::class]);
        $this->assertInstanceOf(SubTitleMapper::class, $service->classMap[SubTitle::class][0]);

        $this->assertArrayHasKey(SubTitleMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SubTitleMapper::elementName()]);
        $this->assertInstanceOf(SubTitleMapper::class, $service->elementMap[SubTitleMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(SubTitle $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<sub-title %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new SubTitle();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider bodyCases */
    public function testBody(SubTitle $element, string $innerXml): void
    {
        $xml = sprintf('<sub-title xmlns="%s" xmlns:m="%s">%s</sub-title>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new SubTitle();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('USER’S GUIDE');
        $innerXml = 'USER’S GUIDE';
        yield 'ICCPC2024V1.0' => [deep_copy($element), $innerXml];
    }
}
