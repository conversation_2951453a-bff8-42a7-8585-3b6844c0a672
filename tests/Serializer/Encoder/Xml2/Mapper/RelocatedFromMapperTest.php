<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Enum\Align;
use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;
use App\Serializer\Encoder\Xml2\Mapper\RelocatedFromMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\RelocatedFromMapper
 */
class RelocatedFromMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(RelocatedFrom::class, $service->classMap);
        $this->assertIsCallable($service->classMap[RelocatedFrom::class]);
        $this->assertInstanceOf(RelocatedFromMapper::class, $service->classMap[RelocatedFrom::class][0]);

        $this->assertArrayHasKey(RelocatedFromMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[RelocatedFromMapper::elementName()]);
        $this->assertInstanceOf(RelocatedFromMapper::class, $service->elementMap[RelocatedFromMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(RelocatedFrom $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<relocated-from %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new RelocatedFrom();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        $this->commonAttributeCases($element, $attrs);
        $this->revisionAttributeCases($element, $attrs);

        $element->setIndent(true);
        $attrs['indent'] = 'yes';
        yield 'indent' => [deep_copy($element), $attrs];

        $element->setAlign(Align::JUSTIFY);
        $attrs['align'] = Align::JUSTIFY;
        yield 'align' => [deep_copy($element), $attrs];

        $element->setReferenceId('rid');
        $attrs['rid'] = 'rid';
        yield 'rid' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(RelocatedFrom $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<relocated-from %s>%s</relocated-from>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new RelocatedFrom();
        yield 'baseline' => [deep_copy($element), ''];

        $element->setBody('body');
        yield 'body' => [deep_copy($element), 'body'];
    }

    /** @dataProvider realCases */
    public function testRealCases(RelocatedFrom $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $element = new RelocatedFrom();
        $element->setId('Relocated-from-IMC2024V1.0_Ch02_Sec202_DefREFRIGERANT_SAFETY_GROUP_CLASSIFICATION');
        $element->setReferenceId('IMC2024V1.0_Ch02_Sec202_DefFLAMMABLE_LIQUIDS');
        $element->setRelocatedFromAttr('Original-IMC2024P1_Ch02_Sec202_DefFLAMMABLE_LIQUIDS');
        $element->setBody('Definition REFRIGERANT SAFETY GROUP CLASSIFICATION. relocated from before FLAMMABLE LIQUIDS.');
        $xml = <<<XMLFRAG
<relocated-from
    rid="IMC2024V1.0_Ch02_Sec202_DefFLAMMABLE_LIQUIDS"
    id="Relocated-from-IMC2024V1.0_Ch02_Sec202_DefREFRIGERANT_SAFETY_GROUP_CLASSIFICATION"
    relocated-from="Original-IMC2024P1_Ch02_Sec202_DefFLAMMABLE_LIQUIDS"
    %s
>Definition REFRIGERANT SAFETY GROUP CLASSIFICATION. relocated from before FLAMMABLE LIQUIDS.</relocated-from>
XMLFRAG;
        yield 'IMC2024V1.0_Ch02_Sec202_DefFLAMMABLE_LIQUIDS' => [$element, $xml];
    }
}
