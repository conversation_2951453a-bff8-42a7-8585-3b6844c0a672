<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\BackMatterMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\BackMatterMapper
 */
class BackMatterMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(BackMatter::class, $service->classMap);
        $this->assertIsCallable($service->classMap[BackMatter::class]);
        $this->assertInstanceOf(BackMatterMapper::class, $service->classMap[BackMatter::class][0]);

        $this->assertArrayHasKey(BackMatterMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[BackMatterMapper::elementName()]);
        $this->assertInstanceOf(BackMatterMapper::class, $service->elementMap[BackMatterMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(BackMatter $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<backmatter %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new BackMatter();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(BackMatter $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<backmatter %s>%s</backmatter>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new BackMatter();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->addChild(new Appendix());
        $innerXml .= '<appendix/>';
        yield 'appendix 1' => [deep_copy($element), $innerXml];

        $element->addChild(new Appendix());
        $innerXml .= '<appendix/>';
        yield 'appendix 2' => [deep_copy($element), $innerXml];

        $element->addChild(new Level());
        $innerXml .= '<level-1/>';
        yield 'level 1' => [deep_copy($element), $innerXml];

        $element->addChild(new Level());
        $innerXml .= '<level-1/>';
        yield 'level 2' => [deep_copy($element), $innerXml];

        $element->addChild(new Index());
        $innerXml .= '<index/>';
        yield 'index' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<backmatter
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
>
    <appendix id="appendix1" ct-uuid="appendix1.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </appendix>
    <appendix id="appendix2" ct-uuid="appendix2.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </appendix>
    <level-1 id="level1" ct-uuid="level1.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </level-1>
    <level-1 id="level2" ct-uuid="level2.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </level-1>
    <index id="index" ct-uuid="index.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </index>
</backmatter>
XML;
        // elements
        $element = new BackMatter();
        $element->setId('id');
        $element->setCtUuid('uuid');

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $appendix1 = new Appendix();
        $appendix1->setId('appendix1');
        $appendix1->setCtUuid('appendix1.uuid');
        $appendix1->setTitleGroup($titleGroup);
        $element->addChild($appendix1);

        $appendix2 = new Appendix();
        $appendix2->setId('appendix2');
        $appendix2->setCtUuid('appendix2.uuid');
        $appendix2->setTitleGroup($titleGroup);
        $element->addChild($appendix2);

        $level1 = new Level();
        $level1->setId('level1');
        $level1->setCtUuid('level1.uuid');
        $level1->setTitleGroup($titleGroup);
        $element->addChild($level1);

        $level2 = new Level();
        $level2->setId('level2');
        $level2->setCtUuid('level2.uuid');
        $level2->setTitleGroup($titleGroup);
        $element->addChild($level2);

        $index = new Index();
        $index->setId('index');
        $index->setCtUuid('index.uuid');
        $index->setTitleGroup($titleGroup);
        $element->addChild($index);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
