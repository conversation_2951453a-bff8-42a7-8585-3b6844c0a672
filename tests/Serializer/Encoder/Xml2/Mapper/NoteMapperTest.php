<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\NoteMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\NoteMapper
 */
class NoteMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Note::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Note::class]);
        $this->assertInstanceOf(NoteMapper::class, $service->classMap[Note::class][0]);

        $this->assertArrayHasKey(NoteMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[NoteMapper::elementName()]);
        $this->assertInstanceOf(NoteMapper::class, $service->elementMap[NoteMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Note $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<note %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Note();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(Note $element, string $innerXml): void
    {
        $xml = sprintf('<note xmlns="%s" xmlns:m="%s">%s</note>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Note();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->setBody('<p>Body</p>');
        $innerXml .= '<p>Body</p>';
        yield 'body' => [deep_copy($element), $innerXml];
    }

    /** @dataProvider realCases */
    public function testRealCases(Note $element, string $xml): void
    {
        $namespaces = sprintf('xmlns="%s" xmlns:m="%s"', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $xml = sprintf($xml, $namespaces);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function realCases(): iterable
    {
        $xml = <<<XMLFRAG
<note %s>
    <titlegroup>
        <title><emphasis type="bold">Code development reminder: </emphasis></title>
    </titlegroup>
    <p><emphasis>...</emphasis></p>
</note>
XMLFRAG;
        $title = new Title();
        $title->setBody('<emphasis type="bold">Code development reminder: </emphasis>');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $element = new Note();
        $element->setTitleGroup($titleGroup);
        $element->setBody('<p><emphasis>...</emphasis></p>');
        yield 'IBC2024V2.0_AppxF' => [$element, $xml];
    }
}
