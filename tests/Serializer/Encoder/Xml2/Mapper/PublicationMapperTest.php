<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Metadata;
use App\Serializer\Encoder\Xml2\Element\Publication;
use App\Serializer\Encoder\Xml2\Element\Volume;
use App\Serializer\Encoder\Xml2\Mapper\PublicationMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\PublicationMapper
 */
class PublicationMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Publication::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Publication::class]);
        $this->assertInstanceOf(PublicationMapper::class, $service->classMap[Publication::class][0]);

        $this->assertArrayHasKey(PublicationMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[PublicationMapper::elementName()]);
        $this->assertInstanceOf(PublicationMapper::class, $service->elementMap[PublicationMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Publication $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<publication %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Publication();
        $attrs = [
            'schema-version'     => Xml2Schema::VERSION,
            'schematron-version' => Xml2Schema::SCHEMATRON_VERSION,
        ];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);

        $element->setDocumentVersion('3.1');
        $attrs['document-version'] = '3.1';
        yield 'document-version' => [deep_copy($element), $attrs];

        $element->setid('volumeId');
        $attrs['id'] = 'volumeId';
        yield 'id' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Publication $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'              => Xml2Schema::XMLNS,
            'xmlns:m'            => Xml2Schema::XMLNS_M,
            'schema-version'     => Xml2Schema::VERSION,
            'schematron-version' => Xml2Schema::SCHEMATRON_VERSION,
        ];
        $xml = sprintf('<publication %s>%s</publication>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Publication();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $metadata = new Metadata();
        $element->setMetadata($metadata);
        $innerXml .= '<metadata/>';
        yield 'metadata' => [deep_copy($element), $innerXml];

        $volume1 = new Volume();
        $volume1->setId('volume1');
        $volume2 = new Volume();
        $volume2->setId('volume2');
        $element->setChildren([$volume1, $volume2]);
        $innerXml .= <<<XMLFRAG
<volume id="volume1"/>
<volume id="volume2"/>
XMLFRAG;
        yield 'volumes' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $schemaVersion = Xml2Schema::VERSION;
        $schematronVersion = Xml2Schema::SCHEMATRON_VERSION;
        $xml = <<<XML
<publication
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    schema-version="$schemaVersion"
    schematron-version="$schematronVersion"
    document-version="1.1"
    id="id"
    ct-uuid="uuid"
>
    <volume id="volume1" ct-uuid="volume1.uuid"/>
    <volume id="volume2" ct-uuid="volume2.uuid"/>
</publication>
XML;
        $element = new Publication();
        $element->setId('id');
        $element->setCtUuid('uuid');
        $element->setDocumentVersion('1.1');

        $volume1 = new Volume();
        $volume1->setId('volume1');
        $volume1->setCtUuid('volume1.uuid');

        $volume2 = new Volume();
        $volume2->setId('volume2');
        $volume2->setCtUuid('volume2.uuid');

        $element->setChildren([$volume1, $volume2]);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
