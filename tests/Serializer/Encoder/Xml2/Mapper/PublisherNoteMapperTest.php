<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\PublisherNoteMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\PublisherNoteMapper
 */
class PublisherNoteMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(PublisherNote::class, $service->classMap);
        $this->assertIsCallable($service->classMap[PublisherNote::class]);
        $this->assertInstanceOf(PublisherNoteMapper::class, $service->classMap[PublisherNote::class][0]);

        $this->assertArrayHasKey(PublisherNoteMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[PublisherNoteMapper::elementName()]);
        $this->assertInstanceOf(PublisherNoteMapper::class, $service->elementMap[PublisherNoteMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(PublisherNote $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<publisher-note %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new PublisherNote();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'attrs / @tocentry' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(PublisherNote $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<publisher-note %s>%s</publisher-note>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new PublisherNote();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setBody('<p>Body</p>');
        $innerXml .= '<p>Body</p>';
        yield 'body' => [deep_copy($element), $innerXml];

        $child1 = new Section();
        $element->addChild($child1);
        $innerXml .= '<section disp-level="1"/>';
        yield 'child 1' => [deep_copy($element), $innerXml];

        $child2 = new Section();
        $element->addChild($child2);
        $innerXml .= '<section disp-level="1"/>';
        yield 'child 2' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<publisher-note
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
>
    <titlegroup>
        <title>New Design for the 2024 International Codes</title>
    </titlegroup>
    <mediaobject><img src="Images/IBC2024V2.0_NEW_DESIGN.jpg" alt="NEW DESIGN"/></mediaobject>
</publisher-note>
XML;
        // elements
        $element = new PublisherNote();
        $element->setId('id');
        $element->setCtUuid('uuid');

        $title = new Title();
        $title->setBody('New Design for the 2024 International Codes');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $element->setBody('<mediaobject><img src="Images/IBC2024V2.0_NEW_DESIGN.jpg" alt="NEW DESIGN"/></mediaobject>');

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
