<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\IndexMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\IndexMapper
 */
class IndexMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Index::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Index::class]);
        $this->assertInstanceOf(IndexMapper::class, $service->classMap[Index::class][0]);

        $this->assertArrayHasKey(IndexMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[IndexMapper::elementName()]);
        $this->assertInstanceOf(IndexMapper::class, $service->elementMap[IndexMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Index $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<index %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Index();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield '@tocentry' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Index $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<index %s>%s</index>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Index();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titlegroup' => [deep_copy($element), $innerXml];

        $element->addChild(new IndexDivision());
        $innerXml .= '<indexdiv/>';
        yield 'index-div' => [deep_copy($element), $innerXml];

        $element->addChild(new IndexEntry());
        $innerXml .= '<index-entry/>';
        yield 'index-entry' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<index
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <index-entry/>
    <index-entry/>
    <indexdiv/>
    <indexdiv/>
</index>
XML;
        // elements
        $element = new Index();
        $element->setId('id');
        $element->setCtUuid('uuid');

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);
        $element->setTitleGroup($titleGroup);

        $entry1 = new IndexEntry();
        $element->addChild($entry1);

        $entry2 = new IndexEntry();
        $element->addChild($entry2);

        $div1 = new IndexDivision();
        $element->addChild($div1);

        $div2 = new IndexDivision();
        $element->addChild($div2);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
