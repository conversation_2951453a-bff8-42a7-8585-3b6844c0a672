<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\SeeIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Mapper\SeeIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SeeIndexEntryMapper
 */
class SeeIndexEntryMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(SeeIndexEntry::class, $service->classMap);
        $this->assertIsCallable($service->classMap[SeeIndexEntry::class]);
        $this->assertInstanceOf(SeeIndexEntryMapper::class, $service->classMap[SeeIndexEntry::class][0]);

        $this->assertArrayHasKey(SeeIndexEntryMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SeeIndexEntryMapper::elementName()]);
        $this->assertInstanceOf(SeeIndexEntryMapper::class, $service->elementMap[SeeIndexEntryMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(SeeIndexEntry $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<seeie %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new SeeIndexEntry();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setPrefix('prefix');
        $attrs['prefix'] = 'prefix';
        yield 'prefix' => [deep_copy($element), $attrs];

        $element->setReferenceId('rid');
        $attrs['rid'] = 'rid';
        yield 'rid' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(SeeIndexEntry $element, string $innerXml): void
    {
        $xml = sprintf('<seeie xmlns="%s" xmlns:m="%s">%s</seeie>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new SeeIndexEntry();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTerm(new Term());
        $innerXml .= '<term/>';
        yield 'term' => [deep_copy($element), $innerXml];

        $element->setNavPointerGroup('<nav-pointer-group/>');
        $innerXml .= '<nav-pointer-group/>';
        yield 'nav-pointer-group' => [deep_copy($element), $innerXml];
    }
}
