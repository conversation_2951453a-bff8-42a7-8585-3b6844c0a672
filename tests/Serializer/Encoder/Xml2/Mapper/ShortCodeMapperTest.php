<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\ShortCode;
use App\Serializer\Encoder\Xml2\Mapper\ShortCodeMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\ShortCodeMapper
 */
class ShortCodeMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(ShortCode::class, $service->classMap);
        $this->assertIsCallable($service->classMap[ShortCode::class]);
        $this->assertInstanceOf(ShortCodeMapper::class, $service->classMap[ShortCode::class][0]);

        $this->assertArrayHasKey(ShortCodeMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[ShortCodeMapper::elementName()]);
        $this->assertInstanceOf(ShortCodeMapper::class, $service->elementMap[ShortCodeMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(ShortCode $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<short-code %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new ShortCode();
        $attrs = ['short-url' => ''];
        yield 'baseline' => [deep_copy($element), $attrs];

        $element->setShortUrl('short-url');
        $attrs['short-url'] = 'short-url';
        yield 'short-url' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(ShortCode $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'     => Xml2Schema::XMLNS,
            'xmlns:m'   => Xml2Schema::XMLNS_M,
            'short-url' => '',
        ];
        $xml = sprintf('<short-code %s>%s</short-code>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new ShortCode();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('body');
        $innerXml .= 'body';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
