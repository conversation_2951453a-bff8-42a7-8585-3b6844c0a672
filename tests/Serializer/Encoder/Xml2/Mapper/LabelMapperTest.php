<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Label;
use App\Serializer\Encoder\Xml2\Mapper\LabelMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\LabelMapper
 */
class LabelMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Label::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Label::class]);
        $this->assertInstanceOf(LabelMapper::class, $service->classMap[Label::class][0]);

        $this->assertArrayHasKey(LabelMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[LabelMapper::elementName()]);
        $this->assertInstanceOf(LabelMapper::class, $service->elementMap[LabelMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Label $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<label %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Label();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider bodyCases */
    public function testBody(Label $element, string $innerXml): void
    {
        $xml = sprintf('<label xmlns="%s" xmlns:m="%s">%s</label>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new Label();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('Section');
        $innerXml = 'Section';
        yield 'IBC2024V2.0_Ch01_SubCh01_Sec102' => [deep_copy($element), $innerXml];
    }
}
