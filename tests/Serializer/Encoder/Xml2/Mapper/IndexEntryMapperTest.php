<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\PrimaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\SecondaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Element\TertiaryIndexEntry;
use App\Serializer\Encoder\Xml2\Mapper\IndexEntryMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\IndexEntryMapper
 */
class IndexEntryMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(IndexEntry::class, $service->classMap);
        $this->assertIsCallable($service->classMap[IndexEntry::class]);
        $this->assertInstanceOf(IndexEntryMapper::class, $service->classMap[IndexEntry::class][0]);

        $this->assertArrayHasKey(IndexEntryMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[IndexEntryMapper::elementName()]);
        $this->assertInstanceOf(IndexEntryMapper::class, $service->elementMap[IndexEntryMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(IndexEntry $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<index-entry %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new IndexEntry();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(IndexEntry $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<index-entry %s>%s</index-entry>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new IndexEntry();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $primaryIE = new PrimaryIndexEntry();
        $primaryIE->setId('id');
        $element->setPrimaryIndexEntry($primaryIE);
        $innerXml .= '<primaryie id="id"/>';
        yield 'primaryie' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<index-entry
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    ct-uuid="uuid"
>
    <primaryie id="IBC2024V2.0_Index_AAC_MASONRY">
        <term>
            <insert role="insert" data-changed="changed_level0" data-changed-in="IBC2024V1.0">AAC Masonry</insert>
        </term>
<nav-pointer-group>
    <nav-pointer rid="IBC2024V2.0_Ch02_Sec202">202</nav-pointer>
    <nav-pointer rid="IBC2024V2.0_Ch21_Sec2103.1">2103.1</nav-pointer>
    <nav-pointer rid="IBC2024V2.0_Ch21_Sec2108.1">2108.1</nav-pointer>
</nav-pointer-group>
    </primaryie>
    <secondaryie id="IBC2024V2.0_Index_ACCESS_OPENINGS_SubIndexATTIC">
        <term>Attic</term>
        <nav-pointer-group><nav-pointer rid="IBC2024V2.0_Ch12_Sec1209.2">1209.2</nav-pointer></nav-pointer-group>
    </secondaryie>
    <tertiaryie id="IFGC2021P2_Index_ACCESS_APPLIANCES_SubIndexWALL_FURNACES_VENTED_FILTER">
        <term>Wall furnaces, vented, filter</term>
        <nav-pointer-group><nav-pointer rid="IFGC2021P2_Index">608.6</nav-pointer></nav-pointer-group>
    </tertiaryie>
</index-entry>
XML;
        // elements
        $element = new IndexEntry();
        $element->setId('');
        $element->setCtUuid('uuid');

        $term = new Term();
        $term->setBody('<insert role="insert" data-changed="changed_level0" data-changed-in="IBC2024V1.0">AAC Masonry</insert>');

        $primary = new PrimaryIndexEntry();
        $primary->setId('IBC2024V2.0_Index_AAC_MASONRY');
        $primary->setTerm($term);
        $primary->setNavPointerGroup(<<<XML
<nav-pointer-group>
    <nav-pointer rid="IBC2024V2.0_Ch02_Sec202">202</nav-pointer>
    <nav-pointer rid="IBC2024V2.0_Ch21_Sec2103.1">2103.1</nav-pointer>
    <nav-pointer rid="IBC2024V2.0_Ch21_Sec2108.1">2108.1</nav-pointer>
</nav-pointer-group>
XML
        );
        $element->setPrimaryIndexEntry($primary);

        $secondaryTerm = new Term();
        $secondaryTerm->setBody('Attic');

        $secondary = new SecondaryIndexEntry();
        $secondary->setId('IBC2024V2.0_Index_ACCESS_OPENINGS_SubIndexATTIC');
        $secondary->setTerm($secondaryTerm);
        $secondary->setNavPointerGroup('<nav-pointer-group><nav-pointer rid="IBC2024V2.0_Ch12_Sec1209.2">1209.2</nav-pointer></nav-pointer-group>');
        $element->addChild($secondary);

        $tertiaryTerm = new Term();
        $tertiaryTerm->setBody('Wall furnaces, vented, filter');

        $tertiary = new TertiaryIndexEntry();
        $tertiary->setId('IFGC2021P2_Index_ACCESS_APPLIANCES_SubIndexWALL_FURNACES_VENTED_FILTER');
        $tertiary->setTerm($tertiaryTerm);
        $tertiary->setNavPointerGroup('<nav-pointer-group><nav-pointer rid="IFGC2021P2_Index">608.6</nav-pointer></nav-pointer-group>');
        $element->addChild($tertiary);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
