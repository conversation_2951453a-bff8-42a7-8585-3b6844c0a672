<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\SeeAlsoIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Mapper\SeeAlsoIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SeeAlsoIndexEntryMapper
 */
class SeeAlsoIndexEntryMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(SeeAlsoIndexEntry::class, $service->classMap);
        $this->assertIsCallable($service->classMap[SeeAlsoIndexEntry::class]);
        $this->assertInstanceOf(SeeAlsoIndexEntryMapper::class, $service->classMap[SeeAlsoIndexEntry::class][0]);

        $this->assertArrayHasKey(SeeAlsoIndexEntryMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SeeAlsoIndexEntryMapper::elementName()]);
        $this->assertInstanceOf(SeeAlsoIndexEntryMapper::class, $service->elementMap[SeeAlsoIndexEntryMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(SeeAlsoIndexEntry $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<seealsoie %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new SeeAlsoIndexEntry();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setPrefix('prefix');
        $attrs['prefix'] = 'prefix';
        yield 'prefix' => [deep_copy($element), $attrs];

        $element->setReferenceId('rid');
        $attrs['rid'] = 'rid';
        yield 'rid' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(SeeAlsoIndexEntry $element, string $innerXml): void
    {
        $xml = sprintf('<seealsoie xmlns="%s" xmlns:m="%s">%s</seealsoie>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new SeeAlsoIndexEntry();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTerm(new Term());
        $innerXml .= '<term/>';
        yield 'term' => [deep_copy($element), $innerXml];

        $element->setNavPointerGroup('<nav-pointer-group/>');
        $innerXml .= '<nav-pointer-group/>';
        yield 'nav-pointer-group' => [deep_copy($element), $innerXml];
    }
}
