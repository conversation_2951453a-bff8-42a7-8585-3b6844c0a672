<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Url;
use App\Serializer\Encoder\Xml2\Mapper\UrlMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\UrlMapper
 */
class UrlMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Url::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Url::class]);
        $this->assertInstanceOf(UrlMapper::class, $service->classMap[Url::class][0]);

        $this->assertArrayHasKey(UrlMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[UrlMapper::elementName()]);
        $this->assertInstanceOf(UrlMapper::class, $service->elementMap[UrlMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Url $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<url %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Url();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);

        $element->setHref('href');
        $attrs['href'] = 'href';
        yield 'href' => [deep_copy($element), $attrs];

        $element->setAlt('alt');
        $attrs['alt'] = 'alt';
        yield 'alt' => [deep_copy($element), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testFields(Url $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<url %s>%s</url>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new Url();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('example.com');
        $innerXml .= 'example.com';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
