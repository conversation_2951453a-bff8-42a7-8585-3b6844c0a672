<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\TableNotes;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Mapper\TableNotesMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\TableNotesMapper
 */
class TableNotesMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(TableNotes::class, $service->classMap);
        $this->assertIsCallable($service->classMap[TableNotes::class]);
        $this->assertInstanceOf(TableNotesMapper::class, $service->classMap[TableNotes::class][0]);

        $this->assertArrayHasKey(TableNotesMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[TableNotesMapper::elementName()]);
        $this->assertInstanceOf(TableNotesMapper::class, $service->elementMap[TableNotesMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(TableNotes $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<table-notes %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new TableNotes();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
        yield from $this->revisionAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(TableNotes $element, string $innerXml): void
    {
        $xml = sprintf('<table-notes xmlns="%s" xmlns:m="%s">%s</table-notes>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new TableNotes();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitleGroup(new TitleGroup());
        $innerXml .= '<titlegroup/>';
        yield 'titleGroup' => [deep_copy($element), $innerXml];

        $element->setBody('<table-note/>');
        $innerXml .= '<table-note/>';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
