<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CoverImage;
use App\Serializer\Encoder\Xml2\Mapper\CoverImageMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\CoverImageMapper
 */
class CoverImageMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(CoverImage::class, $service->classMap);
        $this->assertIsCallable($service->classMap[CoverImage::class]);
        $this->assertInstanceOf(CoverImageMapper::class, $service->classMap[CoverImage::class][0]);

        $this->assertArrayHasKey(CoverImageMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[CoverImageMapper::elementName()]);
        $this->assertInstanceOf(CoverImageMapper::class, $service->elementMap[CoverImageMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(CoverImage $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<cover-image %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new CoverImage();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(CoverImage $element, string $innerXml): void
    {
        $xml = sprintf('<cover-image xmlns="%s" xmlns:m="%s">%s</cover-image>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new CoverImage();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('body');
        $innerXml .= 'body';
        yield 'body' => [deep_copy($element), $innerXml];
    }
}
