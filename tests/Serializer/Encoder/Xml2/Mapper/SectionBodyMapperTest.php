<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Definition;
use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\Figure;
use App\Serializer\Encoder\Xml2\Element\Promulgator;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Table;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Mapper\SectionBodyMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\SectionBodyMapper
 */
class SectionBodyMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(SectionBody::class, $service->classMap);
        $this->assertIsCallable($service->classMap[SectionBody::class]);
        $this->assertInstanceOf(SectionBodyMapper::class, $service->classMap[SectionBody::class][0]);

        $this->assertArrayHasKey(SectionBodyMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[SectionBodyMapper::elementName()]);
        $this->assertInstanceOf(SectionBodyMapper::class, $service->elementMap[SectionBodyMapper::elementName()][0]);
    }

    /** @dataProvider fieldCases */
    public function testFields(SectionBody $element, string $innerXml): void
    {
        $xml = sprintf('<section-body xmlns="%s" xmlns:m="%s">%s</section-body>',
            Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new SectionBody();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setBody('<p>body</p>');
        $innerXml = '<p>body</p>';
        yield 'body' => [deep_copy($element), $innerXml];

        $innerXml = <<<XML
<def-list>
<def-item>
<term><emphasis>About this appendix:</emphasis></term>
<def><p><emphasis>Appendix A is a depiction of what is prescribed in <xref role="table-reference" rid="IMC2024V1.0_Ch08_Sec803.10.4_Tbl803.10.4">Table 803.10.4</xref>. See <xref role="section-reference" rid="IMC2024V1.0_Ch08_Sec803.10.4">Section 803.10.4</xref>.</emphasis></p></def>
</def-item>
</def-list>
XML;
        $term = new Term();
        $term->setBody('<emphasis>About this appendix:</emphasis>');
        $def = new Definition();
        $def->setBody('<p><emphasis>Appendix A is a depiction of what is prescribed in <xref role="table-reference" rid="IMC2024V1.0_Ch08_Sec803.10.4_Tbl803.10.4">Table 803.10.4</xref>. See <xref role="section-reference" rid="IMC2024V1.0_Ch08_Sec803.10.4">Section 803.10.4</xref>.</emphasis></p>');
        $defItem = new DefinitionItem();
        $defItem->setTerm($term);
        $defItem->setDefinition($def);
        $defList = new DefinitionList();
        $defList->setChildren([$defItem]);

        $element->setBody('');
        $element->setChildren([$defList]);
        yield 'misused def-list' => [deep_copy($element), $innerXml];

        $innerXml = <<<XMLFRAGMENT
<p>above figure</p>
<figure/>
XMLFRAGMENT;
        $element->setBody('<p>above figure</p>');
        $element->setChildren([new Figure()]);
        yield 'figure' => [deep_copy($element), $innerXml];

        $innerXml = <<<XMLFRAGMENT
<p>above definitions</p>
<def-list/>
XMLFRAGMENT;
        $element->setBody('<p>above definitions</p>');
        $element->setChildren([new DefinitionList()]);
        yield 'def-list' => [deep_copy($element), $innerXml];

        $innerXml = '<promulgator/>';
        $element->setBody('');
        $element->setChildren([new Promulgator()]);
        yield 'promulgator' => [deep_copy($element), $innerXml];

        $innerXml = '<formal-table/>';
        $element->setBody('');
        $element->setChildren([new Table()]);
        yield 'table' => [deep_copy($element), $innerXml];
    }

/*
    public function testSectionWithDefinitionList(): void
    {
        $xml = <<<XML
<level-1 id="IWUIC2024V1.0_AppxE_SecE102.1" role="legis-section" xmlns="https://schema.iccsafe.org/book/schema/1.0" xmlns:m="http://www.w3.org/1998/Math/MathML">
    <titlegroup>
        <number>E102.1</number>
        <title>General.</title>
    </titlegroup>
    <section-body>
        <p>The following words and terms shall, for the purposes of this appendix, have the meanings shown herein. Refer to <xref role="chapter_reference" rid="IWUIC2024V1.0_Ch02">Chapter 2</xref> for general definitions.</p>
        <def-list>
            <def-item id="IWUIC2024V1.0_AppxE_SecE102.1_DefCLIMATE">
                <term>CLIMATE.</term>
                <def><p>The average course or condition of the weather at a particular place over a period of many years, as exhibited in absolute extremes, means and frequencies of given departures from these means (i.e., of temperature, wind velocity, precipitation and other weather elements).</p></def>
            </def-item>
            <def-item id="IWUIC2024V1.0_AppxE_SecE102.1_DefGEOGRAPHY">
                <term>GEOGRAPHY.</term>
                <def><p>“A science that deals with the earth and its life, especially the description of land, sea, air, and the distribution of plant and animal life including man and his industries with reference to the mutual relations of these diverse elements.” Webster's Third New International Dictionary of the English Language, Unabridged.</p></def>
            </def-item>
            <def-item id="IWUIC2024V1.0_AppxE_SecE102.1_DefINSURANCE_SERVICES_OFFICE">
                <term>INSURANCE SERVICES OFFICE (ISO).</term>
                <def><p>An agency that recommends fire insurance rates based on a grading schedule that incorporates evaluation of firefighting resources and capabilities.</p></def>
            </def-item>
            <def-item id="IWUIC2024V1.0_AppxE_SecE102.1_DefTOPOGRAPHY">
                <term>TOPOGRAPHY.</term>
                <def><p>The configuration of landmass surface, including its relief (elevation) and the position of its natural and man-made features that affect the ability to cross or transit a terrain.</p></def>
            </def-item>
        </def-list>
    </section-body>
</level-1>
XML;

        $element = new Level();
        $element->id = 'IWUIC2024V1.0_AppxE_SecE102.1';
        $element->role = 'legis-section';
        $element->displayLevel = 1;
        $element->titleGroup = new TitleGroup();
        $element->titleGroup->number = new Number('E102.1');
        $element->titleGroup->title = new Title('General.');
        $element->body = new SectionBody('<p>The following words and terms shall, for the purposes of this appendix, have the meanings shown herein. Refer to <xref role="chapter_reference" rid="IWUIC2024V1.0_Ch02">Chapter 2</xref> for general definitions.</p>');

        $defList = new DefinitionList();
        $defList->children[0] = new DefinitionItem();
        $defList->children[0]->id = 'IWUIC2024V1.0_AppxE_SecE102.1_DefCLIMATE';
        $defList->children[0]->term = new Term('CLIMATE.');
        $defList->children[0]->definition = new Definition('<p>The average course or condition of the weather at a particular place over a period of many years, as exhibited in absolute extremes, means and frequencies of given departures from these means (i.e., of temperature, wind velocity, precipitation and other weather elements).</p>');

        $defList->children[1] = new DefinitionItem();
        $defList->children[1]->id = 'IWUIC2024V1.0_AppxE_SecE102.1_DefGEOGRAPHY';
        $defList->children[1]->term = new Term('GEOGRAPHY.');
        $defList->children[1]->definition = new Definition('<p>“A science that deals with the earth and its life, especially the description of land, sea, air, and the distribution of plant and animal life including man and his industries with reference to the mutual relations of these diverse elements.” Webster\'s Third New International Dictionary of the English Language, Unabridged.</p>');

        $defList->children[2] = new DefinitionItem();
        $defList->children[2]->id = 'IWUIC2024V1.0_AppxE_SecE102.1_DefINSURANCE_SERVICES_OFFICE';
        $defList->children[2]->term = new Term('INSURANCE SERVICES OFFICE (ISO).');
        $defList->children[2]->definition = new Definition('<p>An agency that recommends fire insurance rates based on a grading schedule that incorporates evaluation of firefighting resources and capabilities.</p>');

        $defList->children[3] = new DefinitionItem();
        $defList->children[3]->id = 'IWUIC2024V1.0_AppxE_SecE102.1_DefTOPOGRAPHY';
        $defList->children[3]->term = new Term('TOPOGRAPHY.');
        $defList->children[3]->definition = new Definition('<p>The configuration of landmass surface, including its relief (elevation) and the position of its natural and man-made features that affect the ability to cross or transit a terrain.</p>');

        $element->body->children[] = $defList;
        $element->children[] = $defList;

        $this->testRead($xml, $element);
        $this->testWrite($xml, $element);
    }
*/
/*
    public function testSectionWithTableFigureAndChildren(): void
    {
        $xml = <<<XML
<level-1 role="legis-section" id="IWUIC2024V1.0_Ch06_Sec603.2" xmlns="https://schema.iccsafe.org/book/schema/1.0" xmlns:m="http://www.w3.org/1998/Math/MathML">
    <titlegroup>
        <number>603.2</number>
        <title>Fuel modification.</title>
    </titlegroup>
    <section-body>
        <p>Buildings or structures, constructed in compliance with the <emphasis>conforming
          defensible</emphasis> space category of <xref role="table-reference" rid="IWUIC2024V1.0_Ch05_Sec503.1_Tbl503.1">Table 503.1</xref>, shall comply with the <phrase role="keyterm">fuel modification</phrase> distances contained in <xref role="table-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2">Table 603.2</xref>.
          For all other purposes the <phrase role="keyterm">fuel modification</phrase> distance
          shall be not less than 30 feet (9144 mm) or to the lot line, whichever is less.
          Distances specified in <xref role="table-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2">Table 603.2</xref> shall be measured on a
          horizontal plane from the perimeter or projection of the building or structure as
          shown in <xref role="figure-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Fig603.2">Figure
          603.2</xref>. Distances specified in <xref role="table-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2">Table 603.2</xref> are allowed to be
          increased by the <phrase role="keyterm">code official</phrase> because of a
          site-specific analysis based on local conditions and the <phrase role="keyterm">fire
          protection plan</phrase>.</p>
        <formal-table id="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2" frame="all" rowsep="1" colsep="1" role="code">
            <titlegroup>
                <label>TABLE</label>
                <number>603.2</number>
                <title>REQUIRED DEFENSIBLE SPACE</title>
            </titlegroup>
            <table border="1" frame="border" class="colsep rowsep">
                <colgroup>
                    <col align="left" valign="middle"/>
                    <col align="center" valign="middle"/>
                </colgroup>
                <thead>
                    <tr>
                        <th align="center" valign="middle" role="shade">
                            <p>
                                <emphasis type="bold">WILDLAND-URBAN INTERFACE AREA</emphasis>
                            </p>
                        </th>
                        <th align="center" valign="middle" role="shade">
                            <p>
                                <emphasis type="bold">FUEL MODIFICATION DISTANCE (feet)<sup>a</sup></emphasis>
                            </p>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <tr>
                        <td align="left" valign="middle"><p>Moderate hazard</p></td>
                        <td align="center" valign="middle"><p>30</p></td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle"><p>High hazard</p></td>
                        <td align="center" valign="middle"><p>50</p></td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle"><p>Extreme hazard</p></td>
                        <td align="center" valign="middle"><p>100</p></td>
                    </tr>
                </tbody>
            </table>
            <table-notes>
                <table-note><p>For SI: 1 foot = 304.8 mm.</p></table-note>
                <table-note id="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2_note_a">
                    <ol type="custom">
                        <li>
                            <label>a.</label>
                            <p>Distances are allowed to be increased due to site-specific analysis based on local conditions and the <phrase role="keyterm">fire protection plan</phrase>.</p>
                        </li>
                    </ol>
                </table-note>
            </table-notes>
        </formal-table>
        <figure id="IWUIC2024V1.0_Ch06_Sec603.2_Fig603.2">
            <titlegroup>
                <label>FIGURE</label>
                <number>603.2</number>
                <title>MEASUREMENTS OF FUEL MODIFICATION DISTANCE</title>
            </titlegroup>
            <mediaobject-group>
                <mediaobject>
                    <img mime-type="image/Fig603.2.svg" src="Images/IWUIC2024V1.0_Ch06_Sec603.2_Fig603.2.svg" rendition="parent-image" alt="Alt Text: FIGURE 603.2"/>
                </mediaobject>
            </mediaobject-group>
        </figure>
    </section-body>
    <level-2 role="legis-section" id="IWUIC2024V1.0_Ch06_Sec603.2.1">
        <titlegroup>
            <number>603.2.1</number>
            <title>Responsible party.</title>
        </titlegroup>
        <section-body>
            <p>Persons owning, leasing, controlling, operating or maintaining buildings or
               structures requiring <phrase role="keyterm">defensible spaces</phrase> are
               responsible for modifying or removing nonfire-resistive vegetation on the property
               owned, leased or controlled by said person.</p>
        </section-body>
    </level-2>
</level-1>
XML;

        $element = new Level();
        $element->setId('IWUIC2024V1.0_Ch06_Sec603.2');
        $element->setRole('legis-section');
        $element->setDisplayLevel(1);
        $element->setTitleGroup($tg = new TitleGroup());
        $tg->number = new Number('603.2');
        $tg->title = new Title('Fuel modification.');
        $element->setBody(new SectionBody(<<<XML
<p>Buildings or structures, constructed in compliance with the <emphasis>conforming
          defensible</emphasis> space category of <xref role="table-reference" rid="IWUIC2024V1.0_Ch05_Sec503.1_Tbl503.1">Table 503.1</xref>, shall comply with the <phrase role="keyterm">fuel modification</phrase> distances contained in <xref role="table-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2">Table 603.2</xref>.
          For all other purposes the <phrase role="keyterm">fuel modification</phrase> distance
          shall be not less than 30 feet (9144 mm) or to the lot line, whichever is less.
          Distances specified in <xref role="table-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2">Table 603.2</xref> shall be measured on a
          horizontal plane from the perimeter or projection of the building or structure as
          shown in <xref role="figure-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Fig603.2">Figure
          603.2</xref>. Distances specified in <xref role="table-reference" rid="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2">Table 603.2</xref> are allowed to be
          increased by the <phrase role="keyterm">code official</phrase> because of a
          site-specific analysis based on local conditions and the <phrase role="keyterm">fire
          protection plan</phrase>.</p>
XML));

        $table = new Table();
        $table->setId('IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2');
        $table->setRole('code');
        $table->setFrame('all');
        $table->setRowSeparator(true);
        $table->setColumnSeparator(true);
        $table->setTitleGroup($tableTG = new TitleGroup());
        $tableTG->setLabel('TABLE');
        $tableTG->setNumber('603.2');
        $tableTG->setTitle('REQUIRED DEFENSIBLE SPACE');
        $table->setBody(<<<XML
<table border="1" frame="border" class="colsep rowsep">
                <colgroup>
                    <col align="left" valign="middle"/>
                    <col align="center" valign="middle"/>
                </colgroup>
                <thead>
                    <tr>
                        <th align="center" valign="middle" role="shade">
                            <p>
                                <emphasis type="bold">WILDLAND-URBAN INTERFACE AREA</emphasis>
                            </p>
                        </th>
                        <th align="center" valign="middle" role="shade">
                            <p>
                                <emphasis type="bold">FUEL MODIFICATION DISTANCE (feet)<sup>a</sup></emphasis>
                            </p>
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <tr>
                        <td align="left" valign="middle"><p>Moderate hazard</p></td>
                        <td align="center" valign="middle"><p>30</p></td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle"><p>High hazard</p></td>
                        <td align="center" valign="middle"><p>50</p></td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle"><p>Extreme hazard</p></td>
                        <td align="center" valign="middle"><p>100</p></td>
                    </tr>
                </tbody>
            </table>
XML);
        $table->setTableNotes($notes = new TableNotes());
        $notes->setBody(<<<XMLFRAG
<table-note><p>For SI: 1 foot = 304.8 mm.</p></table-note>
<table-note id="IWUIC2024V1.0_Ch06_Sec603.2_Tbl603.2_note_a">
                    <ol type="custom">
                        <li>
                            <label>a.</label>
                            <p>Distances are allowed to be increased due to site-specific analysis based on local conditions and the <phrase role="keyterm">fire protection plan</phrase>.</p>
                        </li>
                    </ol>
                </table-note>
XMLFRAG);

        $figure = new Figure();
        $figure->setId('IWUIC2024V1.0_Ch06_Sec603.2_Fig603.2');
        $figure->setTitleGroup($figureTG = new TitleGroup());
        $figureTG->setLabel('FIGURE');
        $figureTG->setNumber('603.2');
        $figureTG->setTitle('MEASUREMENTS OF FUEL MODIFICATION DISTANCE');
        $figure->setMedia(<<<XML
<mediaobject-group>
                <mediaobject>
                    <img mime-type="image/Fig603.2.svg" src="Images/IWUIC2024V1.0_Ch06_Sec603.2_Fig603.2.svg" rendition="parent-image" alt="Alt Text: FIGURE 603.2"/>
                </mediaobject>
            </mediaobject-group>
XML);

        $child = new Level();
        $child->setDisplayLevel(2);
        $child->setId('IWUIC2024V1.0_Ch06_Sec603.2.1');
        $child->setRole('legis-section');
        $child->setTitleGroup($childTG = new TitleGroup());
        $childTG->setNumber('603.2.1');
        $childTG->setTitle('Responsible party.');
        $child->setBody(new SectionBody(<<<XML
<p>Persons owning, leasing, controlling, operating or maintaining buildings or
               structures requiring <phrase role="keyterm">defensible spaces</phrase> are
               responsible for modifying or removing nonfire-resistive vegetation on the property
               owned, leased or controlled by said person.</p>
XML));

        $element->getBody()->setChildren([$table, $figure]);
        $element->setChildren([$table, $figure, $child]);

        $this->testRead($xml, $element);
        $this->testWrite($xml, $element);
    }
*/
}
