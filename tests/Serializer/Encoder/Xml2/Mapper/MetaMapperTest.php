<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\Meta;
use App\Serializer\Encoder\Xml2\Mapper\MetaMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\MetaMapper
 */
class MetaMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(Meta::class, $service->classMap);
        $this->assertIsCallable($service->classMap[Meta::class]);
        $this->assertInstanceOf(MetaMapper::class, $service->classMap[Meta::class][0]);

        $this->assertArrayHasKey(MetaMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[MetaMapper::elementName()]);
        $this->assertInstanceOf(MetaMapper::class, $service->elementMap[MetaMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(Meta $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<meta %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new Meta();
        $element->setName('name');
        $attrs = ['name' => 'name'];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider bodyCases */
    public function testBody(Meta $element, string $name, string $body): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
            'name'    => $name,
        ];
        $xml = sprintf('<meta %s>%s</meta>', $this->arrayToAttrs($attrs), $body);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function bodyCases(): iterable
    {
        $element = new Meta();
        $element->setName('schematron-ruleset');
        $element->setBody('I-Codes');
        yield 'schematron-ruleset' => [deep_copy($element), 'schematron-ruleset', 'I-Codes'];

        $element->setName('title');
        $element->setBody('2024 International Building Code');
        yield 'title' => [deep_copy($element), 'title', '2024 International Building Code'];

        $element->setName('edition');
        $element->setBody('2024 Edition');
        yield 'edition' => [deep_copy($element), 'edition', '2024 Edition'];

        $element->setName('parent-document');
        $element->setBody('IBC2024');
        yield 'parent-document' => [deep_copy($element), 'parent-document', 'IBC2024'];

        $element->setName('publication-id');
        $element->setBody('IBC2024V2.0');
        yield 'publication-id' => [deep_copy($element), 'publication-id', 'IBC2024V2.0'];

        $element->setName('year');
        $element->setBody('2024');
        yield 'year' => [deep_copy($element), 'year', '2024'];

        $element->setName('publication-abbrev');
        $element->setBody('IBC');
        yield 'publication-abbrev' => [deep_copy($element), 'publication-abbrev', 'IBC'];

        $element->setName('version');
        $element->setBody('Version 1.0');
        yield 'version' => [deep_copy($element), 'version', 'Version 1.0'];

        $element->setName('output-type');
        $element->setBody('Print PDF');
        yield 'output-type' => [deep_copy($element), 'output-type', 'Print PDF'];

        $element->setName('origin');
        $element->setBody('IB');
        yield 'origin' => [deep_copy($element), 'origin', 'IB'];

        $element->setName('date-origin');
        $element->setBody('2024-5-29');
        yield 'date-origin' => [deep_copy($element), 'date-origin', '2024-5-29'];

        $element->setName('modified-by');
        $element->setBody('Jeff Wight');
        yield 'modified-by' => [deep_copy($element), 'modified-by', 'Jeff Wight'];

        $element->setName('date-updated');
        $element->setBody('2025-06-10');
        yield 'date-updated' => [deep_copy($element), 'date-updated', '2025-06-10'];
    }
}
