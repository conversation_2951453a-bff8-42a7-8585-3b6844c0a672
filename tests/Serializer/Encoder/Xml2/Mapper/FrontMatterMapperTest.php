<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2\Mapper;

use App\Serializer\Encoder\Xml2\Element\CopyrightPage;
use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;
use App\Serializer\Encoder\Xml2\Element\TitlePage;
use App\Serializer\Encoder\Xml2\Mapper\FrontMatterMapper;
use App\Serializer\Encoder\Xml2\Xml2Schema;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Serializer\Encoder\Xml2\Mapper\AbstractFieldMapper
 * @covers \App\Serializer\Encoder\Xml2\Mapper\FrontMatterMapper
 */
class FrontMatterMapperTest extends MapperTestCase
{
    public function testService(): void
    {
        $service = $this->service->service;

        $this->assertArrayHasKey(FrontMatter::class, $service->classMap);
        $this->assertIsCallable($service->classMap[FrontMatter::class]);
        $this->assertInstanceOf(FrontMatterMapper::class, $service->classMap[FrontMatter::class][0]);

        $this->assertArrayHasKey(FrontMatterMapper::elementName(), $service->elementMap);
        $this->assertIsCallable($service->elementMap[FrontMatterMapper::elementName()]);
        $this->assertInstanceOf(FrontMatterMapper::class, $service->elementMap[FrontMatterMapper::elementName()][0]);
    }

    /** @dataProvider attributeCases */
    public function testAttributes(FrontMatter $element, array $attrs): void
    {
        $attrs['xmlns'] = Xml2Schema::XMLNS;
        $attrs['xmlns:m'] = Xml2Schema::XMLNS_M;
        $xml = sprintf('<frontmatter %s/>', $this->arrayToAttrs($attrs));

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function attributeCases(): iterable
    {
        $element = new FrontMatter();
        $attrs = [];
        yield 'baseline' => [deep_copy($element), $attrs];

        yield from $this->commonAttributeCases($element, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testFields(FrontMatter $element, string $innerXml): void
    {
        $attrs = [
            'xmlns'   => Xml2Schema::XMLNS,
            'xmlns:m' => Xml2Schema::XMLNS_M,
        ];
        $xml = sprintf('<frontmatter %s>%s</frontmatter>', $this->arrayToAttrs($attrs), $innerXml);

        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }

    public function fieldCases(): iterable
    {
        $element = new FrontMatter();
        $innerXml = '';
        yield 'baseline' => [deep_copy($element), $innerXml];

        $element->setTitlePage(new TitlePage());
        $innerXml .= '<titlepage/>';
        yield 'titlepage' => [deep_copy($element), $innerXml];

        $element->setCopyrightPage(new CopyrightPage());
        $innerXml .= '<copyright-page/>';
        yield 'copyright-page' => [deep_copy($element), $innerXml];

        $element->setPublisherNote(new PublisherNote());
        $innerXml .= '<publisher-note/>';
        yield 'publisher-note' => [deep_copy($element), $innerXml];

        $element->setForeword(new Foreword());
        $innerXml .= '<foreword/>';
        yield 'foreword' => [deep_copy($element), $innerXml];

        $element->setPreface(new Preface());
        $innerXml .= '<preface/>';
        yield 'preface' => [deep_copy($element), $innerXml];

        $child1 = new Section();
        $element->addChild($child1);
        $innerXml .= '<section disp-level="1"/>';
        yield 'child 1' => [deep_copy($element), $innerXml];

        $child2 = new Section();
        $element->addChild($child2);
        $innerXml .= '<section disp-level="1"/>';
        yield 'child 2' => [deep_copy($element), $innerXml];
    }

    public function testAllElements(): void
    {
        $xmlns = Xml2Schema::XMLNS;
        $xmlnsM = Xml2Schema::XMLNS_M;
        $xml = <<<XML
<frontmatter
    xmlns="$xmlns"
    xmlns:m="$xmlnsM"
    id="id"
    ct-uuid="uuid"
>
    <titlepage id="titlePage" ct-uuid="titlePage.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </titlepage>
    <copyright-page id="copyrightPage" ct-uuid="copyrightPage.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </copyright-page>
    <publisher-note id="publisherNote" ct-uuid="publisherNote.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </publisher-note>
    <foreword id="foreword" ct-uuid="foreword.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </foreword>
    <preface id="preface" ct-uuid="preface.uuid">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </preface>
    <section id="child1" ct-uuid="child1.uuid" disp-level="1">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </section>
    <section id="child2" ct-uuid="child2.uuid" disp-level="1">
        <titlegroup>
            <title>Title</title>
        </titlegroup>
    </section>
</frontmatter>
XML;
        // elements
        $element = new FrontMatter();
        $element->setId('id');
        $element->setCtUuid('uuid');

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $titlePage = new TitlePage();
        $titlePage->setId('titlePage');
        $titlePage->setCtUuid('titlePage.uuid');
        $titlePage->setTitleGroup($titleGroup);
        $element->setTitlePage($titlePage);

        $copyrightPage = new CopyrightPage();
        $copyrightPage->setId('copyrightPage');
        $copyrightPage->setCtUuid('copyrightPage.uuid');
        $copyrightPage->setTitleGroup($titleGroup);
        $element->setCopyrightPage($copyrightPage);

        $copyrightPage = new CopyrightPage();
        $copyrightPage->setId('copyrightPage');
        $copyrightPage->setCtUuid('copyrightPage.uuid');
        $copyrightPage->setTitleGroup($titleGroup);
        $element->setCopyrightPage($copyrightPage);

        $publisherNote = new PublisherNote();
        $publisherNote->setId('publisherNote');
        $publisherNote->setCtUuid('publisherNote.uuid');
        $publisherNote->setTitleGroup($titleGroup);
        $element->setPublisherNote($publisherNote);

        $foreword = new Foreword();
        $foreword->setId('foreword');
        $foreword->setCtUuid('foreword.uuid');
        $foreword->setTitleGroup($titleGroup);
        $element->setForeword($foreword);

        $preface = new Preface();
        $preface->setId('preface');
        $preface->setCtUuid('preface.uuid');
        $preface->setTitleGroup($titleGroup);
        $element->setPreface($preface);

        $child1 = new Section();
        $child1->setId('child1');
        $child1->setCtUuid('child1.uuid');
        $child1->setTitleGroup($titleGroup);
        $element->addChild($child1);

        $child2 = new Section();
        $child2->setId('child2');
        $child2->setCtUuid('child2.uuid');
        $child2->setTitleGroup($titleGroup);
        $element->addChild($child2);

        // tests
        $actual = $this->service->read($xml);
        $this->assertEquals($element, $actual);

        $actual = $this->service->write($element);
        $this->assertXmlStringEqualsXmlString($xml, $actual);
    }
}
