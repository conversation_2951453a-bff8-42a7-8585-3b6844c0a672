<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Encoder\Xml2;

use App\Serializer\Encoder\Xml2\Xml2Encoder;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function sprintf;

class Xml2EncoderTest extends KernelTestCase
{
    public function testEncode(): void
    {
        $this->assertTrue(true);
    }
}
