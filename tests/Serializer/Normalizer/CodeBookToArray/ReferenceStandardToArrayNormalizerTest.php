<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\ReferenceStandard;
use App\Entity\CodeBook\Volume;
use App\Enum\QrBusinessUnit;
use App\Enum\QrDepartment;
use App\Serializer\Normalizer\CodeBookToArray\ReferenceStandardToArrayNormalizer;
use Symfony\Component\Serializer\Serializer;

use function DeepCopy\deep_copy;

class ReferenceStandardToArrayNormalizerTest extends AbstractCodeBookToArrayNormalizer
{
    private ?ReferenceStandardToArrayNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(ReferenceStandardToArrayNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $this->assertTrue($this->normalizer->supportsNormalization(new ReferenceStandard(), 'json'));
        $this->assertTrue($this->serializer->supportsNormalization(new ReferenceStandard(), 'json'));

        $this->assertFalse($this->normalizer->supportsNormalization(new ReferenceStandard()));
        $this->assertFalse($this->normalizer->supportsNormalization(new Volume()));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(ReferenceStandard $fromNode, array $expected, bool $bookContents = false): void
    {
        $actual = $this->normalizer->normalize($fromNode, 'json', ['bookContents' => $bookContents]);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $from = new ReferenceStandard();
        $from->setUlid('uuid');
        $from->setNodeId('nodeId');
        $from->setQrId('nodeId');
        $expected = [
            '_type'              => 'referenceStandard',
            'dataType'           => 'referenceStandard',

            // attributes
            'id'                 => 'nodeId',
            'uuid'               => 'uuid',
            'ctXmlId'            => 'nodeId',
            'role'               => '',
            'display'            => '',
            'verbatim'           => false,
            'language'           => '',
            'additionalInfo'     => '',
            'revisionBy'         => '',
            'revisionDateTime'   => null,
            'revision'           => '',
            'revisionGroup'      => '',
            'dataChanged'        => '',
            'dataChangedIn'      => '',
            'relocatedFromAttr'  => '',
            'deletedBy'          => '',
            'deletedDate'        => null,

            // qr code
            'qrActive'           => false,
            'qrId'               => 'nodeId',
            'qrDisplay'          => false,
            'qrLevelReference'   => '',
            'qrPurpose'          => '',
            'qrImage'            => '',
            'qrShortUrl'         => '',
            'qrBookIcon'         => '',
            'qrUrl'              => '',
            'qrIcon'             => '',
            'qrDepartment'       => QrDepartment::DEFAULT,
            'qrBusinessUnit'     => QrBusinessUnit::DEFAULT,

            // fields
            'number'             => '',
            'numberYear'         => '',
            'title'              => '',
            'titleYear'          => '',
            'navPointerGroup'    => '',

            // internal
            'status'             => 'IN_PROGRESS',
            'showDeletionMarker' => false,
            'codesNotes'         => '',
            'pubsNotes'          => '',
            'typesetterNotes'    => '',
            'hasCodesNotes'      => false,
            'hasPubsNotes'       => false,
            'hasTypesetterNotes' => false,
            'hasApprovedChanges' => false,
            'hasFullyIncorporatedChanges' => false,
        ];

        yield 'baseline' => [deep_copy($from), $expected];

        yield from $this->commonMapCases($from, $expected);
        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);
        yield from $this->codeChangeMapCases($from, $expected);

        $from->setNumber('number');
        $expected['number'] = 'number';

        yield 'number' => [deep_copy($from), $expected];

        $from->setNumberYear('numberYear');
        $expected['numberYear'] = 'numberYear';

        yield 'numberYear' => [deep_copy($from), $expected];

        $from->setTitle('title');
        $expected['title'] = 'title';

        yield 'title' => [deep_copy($from), $expected];

        $from->setTitleYear('titleYear');
        $expected['titleYear'] = 'titleYear';

        yield 'titleYear' => [deep_copy($from), $expected];

        $from->setNavPointerGroup('navPointerGroup');
        $expected['navPointerGroup'] = 'navPointerGroup';

        yield 'navPointerGroup' => [deep_copy($from), $expected];
    }
}
