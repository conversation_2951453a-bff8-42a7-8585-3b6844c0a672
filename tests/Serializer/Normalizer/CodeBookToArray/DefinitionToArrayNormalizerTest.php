<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\Volume;
use App\Enum\QrBusinessUnit;
use App\Enum\QrDepartment;
use App\Serializer\Normalizer\CodeBookToArray\DefinitionToArrayNormalizer;
use Symfony\Component\Serializer\Serializer;

use function DeepCopy\deep_copy;

class DefinitionToArrayNormalizerTest extends AbstractCodeBookToArrayNormalizer
{
    private ?DefinitionToArrayNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(DefinitionToArrayNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $this->assertTrue($this->normalizer->supportsNormalization(new Definition(), 'json'));
        $this->assertTrue($this->serializer->supportsNormalization(new Definition(), 'json'));

        $this->assertFalse($this->normalizer->supportsNormalization(new Definition()));
        $this->assertFalse($this->normalizer->supportsNormalization(new Volume()));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(Definition $fromNode, array $expected, bool $bookContents = false): void
    {
        $actual = $this->normalizer->normalize($fromNode, 'json', ['bookContents' => $bookContents]);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $from = new Definition();
        $from->setUlid('uuid');
        $from->setNodeId('nodeId');
        $from->setQrId('nodeId');
        $expected = [
            '_type'                => 'definition',
            'dataType'             => 'definition',

            // attributes
            'id'                   => 'nodeId',
            'uuid'                 => 'uuid',
            'ctXmlId'              => 'nodeId',
            'role'                 => '',
            'display'              => '',
            'verbatim'             => false,
            'language'             => '',
            'additionalInfo'       => '',
            'revisionBy'           => '',
            'revisionDateTime'     => null,
            'revision'             => '',
            'revisionGroup'        => '',
            'dataChanged'          => '',
            'dataChangedIn'        => '',
            'relocatedFromAttr'    => '',
            'deletedBy'            => '',
            'deletedDate'          => null,
            'indexNumber'          => '',

            // qr code
            'qrActive'             => false,
            'qrId'                 => 'nodeId',
            'qrDisplay'            => false,
            'qrLevelReference'     => '',
            'qrPurpose'            => '',
            'qrImage'              => '',
            'qrShortUrl'           => '',
            'qrBookIcon'           => '',
            'qrUrl'                => '',
            'qrIcon'               => '',
            'qrDepartment'         => QrDepartment::DEFAULT,
            'qrBusinessUnit'       => QrBusinessUnit::DEFAULT,

            // fields
            'committeeDesignation' => '',
            'term'                 => '',
            'definition'           => '',
            'children'             => [],

            // internal
            'status'               => 'IN_PROGRESS',
            'showDeletionMarker'   => false,
            'codesNotes'           => '',
            'pubsNotes'            => '',
            'typesetterNotes'      => '',
            'hasCodesNotes'        => false,
            'hasPubsNotes'         => false,
            'hasTypesetterNotes'   => false,
            'hasApprovedChanges'   => false,
            'hasFullyIncorporatedChanges' => false,
        ];

        yield 'baseline' => [deep_copy($from), $expected];

        yield from $this->commonMapCases($from, $expected);
        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);
        yield from $this->codeChangeMapCases($from, $expected);

        $from->setIndexNumber('indexNumber');
        $expected['indexNumber'] = 'indexNumber';

        yield 'indexNumber' => [deep_copy($from), $expected];

        $from->setCommitteeDesignation('committeeDesignation');
        $expected['committeeDesignation'] = 'committeeDesignation';

        yield 'committeeDesignation' => [deep_copy($from), $expected];

        $from->setTerm('term');
        $expected['term'] = 'term';

        yield 'term' => [deep_copy($from), $expected];

        $from->setDefinition('definition');
        $expected['definition'] = 'definition';

        yield 'definition' => [deep_copy($from), $expected];
    }
}
