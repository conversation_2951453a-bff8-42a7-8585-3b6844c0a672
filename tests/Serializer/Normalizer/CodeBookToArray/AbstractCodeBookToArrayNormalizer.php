<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\AbstractCodeBookNode;
use App\Entity\CodeBook\Appendix;
use App\Entity\CommonBook\Attribute\RevisionAttributes;
use App\Enum\QrBusinessUnit;
use App\Enum\QrDepartment;
use App\Enum\QrIcon;
use App\Enum\Status;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

use function DeepCopy\deep_copy;

class AbstractCodeBookToArrayNormalizer extends KernelTestCase
{
    protected function commonMapCases(AbstractCodeBookNode $from, array &$expected): iterable
    {
        $expected['dataType'] = $from->getDataType();

        yield 'dataType' => [deep_copy($from), $expected];

        $from->setNodeId('newNodeId');
        $expected['id'] = 'newNodeId';
        $expected['ctXmlId'] = 'newNodeId';

        yield 'id, ctXmlId' => [deep_copy($from), $expected];

        $from->setUlid('new');
        $expected['uuid'] = 'new';

        yield 'uuid' => [deep_copy($from), $expected];

        $from->setStatus(Status::PUBS_COMPLETE);
        $expected['status'] = Status::PUBS_COMPLETE;

        yield 'status' => [deep_copy($from), $expected];

        $from->setShowDeletionMarker(true);
        $expected['showDeletionMarker'] = true;

        yield 'showDeletionMarker' => [deep_copy($from), $expected];

        $from->setCodesNotes('codesNotes');
        $expected['codesNotes'] = 'codesNotes';
        $expected['hasCodesNotes'] = true;

        yield 'codesNotes, hasCodesNotes' => [deep_copy($from), $expected];

        $from->setPubsNotes('pubsNotes');
        $expected['pubsNotes'] = 'pubsNotes';
        $expected['hasPubsNotes'] = true;

        yield 'pubsNotes, hasPubsNotes' => [deep_copy($from), $expected];

        $from->setTypesetterNotes('typesetterNotes');
        $expected['typesetterNotes'] = 'typesetterNotes';
        $expected['hasTypesetterNotes'] = true;

        yield 'typesetterNotes, hasTypesetterNotes' => [deep_copy($from), $expected];
    }

    protected function commonAttributeMapCases(AbstractCodeBookNode $from, array &$expected): iterable
    {
        $from->setRole('role');
        $expected['role'] = 'role';

        yield 'role' => [deep_copy($from), $expected];

        $from->setDisplay('display');
        $expected['display'] = 'display';

        yield 'display' => [deep_copy($from), $expected];

        $from->setVerbatim(true);
        $expected['verbatim'] = true;

        yield 'verbatim' => [deep_copy($from), $expected];

        $from->setLanguage('language');
        $expected['language'] = 'language';

        yield 'language' => [deep_copy($from), $expected];

        $from->setAdditionalInfo('add info');
        $expected['additionalInfo'] = 'add info';

        yield 'additionalInfo' => [deep_copy($from), $expected];
    }

    protected function codeChangeMapCases(AbstractCodeBookNode $from, array &$expected): iterable
    {
        $expected['hasApprovedChanges'] = false;
        $expected['hasFullyIncorporatedChanges'] = false;

        yield 'no code changes' => [deep_copy($from), $expected];
    }

    protected function revisionAttributeMapCases(AbstractCodeBookNode $from, array &$expected): iterable
    {
        /** @var RevisionAttributes $from */
        $from->setRevisionBy('revisionBy');
        $expected['revisionBy'] = 'revisionBy';

        yield 'revisionBy' => [deep_copy($from), $expected];

        $dt = new DateTime();
        $from->setRevisionDateTime($dt);
        $expected['revisionDateTime'] = (array) $dt;

        yield 'revisionDateTime' => [deep_copy($from), $expected];

        $from->setRevision('revision');
        $expected['revision'] = 'revision';

        yield 'revision' => [deep_copy($from), $expected];

        $from->setRevisionGroup('revisionGroup');
        $expected['revisionGroup'] = 'revisionGroup';

        yield 'revisionGroup' => [deep_copy($from), $expected];

        $from->setDataChanged('dataChanged');
        $expected['dataChanged'] = 'dataChanged';

        yield 'dataChanged' => [deep_copy($from), $expected];

        $from->setDataChangedIn('dataChangedIn');
        $expected['dataChangedIn'] = 'dataChangedIn';

        yield 'dataChangedIn' => [deep_copy($from), $expected];

        $from->setRelocatedFromAttr('relocatedFrom');
        $expected['relocatedFromAttr'] = 'relocatedFrom';

        yield 'relocatedFromAttr' => [deep_copy($from), $expected];

        $from->setDeletedBy('deletedBy');
        $expected['deletedBy'] = 'deletedBy';

        yield 'deletedBy' => [deep_copy($from), $expected];

        $dt = new DateTime();
        $from->setDeletedDate($dt);
        $expected['deletedDate'] = (array) $dt;

        yield 'deletedDate' => [deep_copy($from), $expected];
    }

    protected function titleGroupMapCases(AbstractCodeBookNode $from, array &$expected): iterable
    {
        yield 'no titleGroup' => [deep_copy($from), $expected];

        /** @var Appendix $from */
        $from->setSuperTitle('superTitle');
        $expected['superTitle'] = 'superTitle';

        yield 'superTitle' => [deep_copy($from), $expected];

        $from->setCommitteeDesignation('committeeDesignation');
        $expected['committeeDesignation'] = 'committeeDesignation';

        yield 'committeeDesignation' => [deep_copy($from), $expected];

        $from->setLabel('label');
        $expected['label'] = 'label';

        yield 'label' => [deep_copy($from), $expected];

        $from->setNumber('number');
        $expected['number'] = 'number';

        yield 'number' => [deep_copy($from), $expected];

        $from->setCorrelated('correlated');
        $expected['correlated'] = 'correlated';

        yield 'correlated' => [deep_copy($from), $expected];

        $from->setTitle('title');
        $expected['title'] = 'title';

        yield 'title' => [deep_copy($from), $expected];

        $from->setTitleAbbreviation('titleAbbreviation');
        $expected['titleAbbreviation'] = 'titleAbbreviation';

        yield 'titleAbbreviation' => [deep_copy($from), $expected];

        $from->setTitleYear('titleYear');
        $expected['titleYear'] = 'titleYear';

        yield 'titleYear' => [deep_copy($from), $expected];

        $from->setSubTitle('subTitle');
        $expected['subTitle'] = 'subTitle';

        yield 'subTitle' => [deep_copy($from), $expected];

//        $from->setTitleHistory('titleHistory');
//        $expected['titleHistory'] = 'titleHistory';
//
//        yield 'titleHistory' => [deep_copy($from), $expected];
    }

    protected function qrCodeMapCases(AbstractCodeBookNode $from, array &$expected): iterable
    {
        yield 'no qrCode' => [deep_copy($from), $expected];

        /** @var Appendix $from */
        $from->setQrActive(true);
        $expected['qrActive'] = true;

        yield 'qrActive' => [deep_copy($from), $expected];

        $from->setQrId('qrId');
        $expected['qrId'] = 'qrId';

        yield 'qrId' => [deep_copy($from), $expected];

        $from->setQrDisplay(true);
        $expected['qrDisplay'] = true;

        yield 'qrDisplay' => [deep_copy($from), $expected];

        $from->setQrLevelReference('qrLevelReference');
        $expected['qrLevelReference'] = 'qrLevelReference';

        yield 'qrLevelReference' => [deep_copy($from), $expected];

        $from->setQrPurpose('qrPurpose');
        $expected['qrPurpose'] = 'qrPurpose';

        yield 'qrPurpose' => [deep_copy($from), $expected];

        $from->setQrImage('qrImage');
        $expected['qrImage'] = 'qrImage';

        yield 'qrImage' => [deep_copy($from), $expected];

        $from->setQrShortUrl('qrShortUrl');
        $expected['qrShortUrl'] = 'qrShortUrl';

        yield 'qrShortUrl' => [deep_copy($from), $expected];

        $from->setQrBookIcon('qrBookIcon');
        $expected['qrBookIcon'] = 'qrBookIcon';

        yield 'qrBookIcon' => [deep_copy($from), $expected];

        $from->setQrUrl('qrUrl');
        $expected['qrUrl'] = 'qrUrl';

        yield 'qrUrl' => [deep_copy($from), $expected];

        $from->setQrIcon(QrIcon::IBC);
        $expected['qrIcon'] = QrIcon::IBC;

        yield 'qrIcon' => [deep_copy($from), $expected];

        $from->setQrDepartment(QrDepartment::MARKETING);
        $expected['qrDepartment'] = QrDepartment::MARKETING;

        yield 'qrDepartment' => [deep_copy($from), $expected];

        $from->setQrBusinessUnit(QrBusinessUnit::ES);
        $expected['qrBusinessUnit'] = QrBusinessUnit::ES;

        yield 'qrBusinessUnit' => [deep_copy($from), $expected];
    }
}
