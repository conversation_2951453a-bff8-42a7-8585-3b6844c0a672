<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Promulgator;
use App\Entity\CodeBook\Volume;
use App\Enum\QrBusinessUnit;
use App\Enum\QrDepartment;
use App\Serializer\Normalizer\CodeBookToArray\PromulgatorToArrayNormalizer;
use Symfony\Component\Serializer\Serializer;

use function DeepCopy\deep_copy;

class PromulgatorToArrayNormalizerTest extends AbstractCodeBookToArrayNormalizer
{
    private ?PromulgatorToArrayNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(PromulgatorToArrayNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $this->assertTrue($this->normalizer->supportsNormalization(new Promulgator(), 'json'));
        $this->assertTrue($this->serializer->supportsNormalization(new Promulgator(), 'json'));

        $this->assertFalse($this->normalizer->supportsNormalization(new Promulgator()));
        $this->assertFalse($this->normalizer->supportsNormalization(new Volume()));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(Promulgator $fromNode, array $expected, bool $bookContents = false): void
    {
        $actual = $this->normalizer->normalize($fromNode, 'json', ['bookContents' => $bookContents]);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $from = new Promulgator();
        $from->setUlid('uuid');
        $from->setNodeId('nodeId');
        $from->setQrId('nodeId');
        $expected = [
            '_type'                => 'promulgator',
            'dataType'             => 'promulgator',

            // attributes
            'id'                   => 'nodeId',
            'uuid'                 => 'uuid',
            'ctXmlId'              => 'nodeId',
            'role'                 => '',
            'display'              => '',
            'verbatim'             => false,
            'language'             => '',
            'additionalInfo'       => '',
            'revisionBy'           => '',
            'revisionDateTime'     => null,
            'revision'             => '',
            'revisionGroup'        => '',
            'dataChanged'          => '',
            'dataChangedIn'        => '',
            'relocatedFromAttr'    => '',
            'deletedBy'            => '',
            'deletedDate'          => null,

            // qr code
            'qrActive'             => false,
            'qrId'                 => 'nodeId',
            'qrDisplay'            => false,
            'qrLevelReference'     => '',
            'qrPurpose'            => '',
            'qrImage'              => '',
            'qrShortUrl'           => '',
            'qrBookIcon'           => '',
            'qrUrl'                => '',
            'qrIcon'               => '',
            'qrDepartment'         => QrDepartment::DEFAULT,
            'qrBusinessUnit'       => QrBusinessUnit::DEFAULT,

            // fields
            'acronym' => '',
            'addressLine'        => '',
            'organizationName'   => '',
            'street'             => '',
            'city'               => '',
            'state'              => '',
            'postalCode'         => '',
            'country'            => '',
            'email'              => '',
            'url'                => '',
            'urlHref'            => '',
            'urlAlt'             => '',
            'phone'              => '',
            'fax'                => '',
            'children' => [],

            // internal
            'status'               => 'IN_PROGRESS',
            'showDeletionMarker'   => false,
            'codesNotes'           => '',
            'pubsNotes'            => '',
            'typesetterNotes'      => '',
            'hasCodesNotes'        => false,
            'hasPubsNotes'         => false,
            'hasTypesetterNotes'   => false,
            'hasApprovedChanges'   => false,
            'hasFullyIncorporatedChanges' => false,
        ];

        yield 'baseline' => [deep_copy($from), $expected];

        yield from $this->commonMapCases($from, $expected);
        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);
        yield from $this->codeChangeMapCases($from, $expected);

        $from->setAcronym('acronym');
        $expected['acronym'] = 'acronym';

        yield 'acronym' => [deep_copy($from), $expected];

        $from->setAddressLine('addressLine');
        $expected['addressLine'] = 'addressLine';

        yield 'addressLine' => [deep_copy($from), $expected];

        $from->setOrganizationName('organizationName');
        $expected['organizationName'] = 'organizationName';

        yield 'organizationName' => [deep_copy($from), $expected];

        $from->setStreet('street');
        $expected['street'] = 'street';

        yield 'street' => [deep_copy($from), $expected];

        $from->setCity('city');
        $expected['city'] = 'city';

        yield 'city' => [deep_copy($from), $expected];

        $from->setState('state');
        $expected['state'] = 'state';

        yield 'state' => [deep_copy($from), $expected];

        $from->setPostalCode('postalCode');
        $expected['postalCode'] = 'postalCode';

        yield 'postalCode' => [deep_copy($from), $expected];

        $from->setCountry('country');
        $expected['country'] = 'country';

        yield 'country' => [deep_copy($from), $expected];

        $from->setEmail('email');
        $expected['email'] = 'email';

        yield 'email' => [deep_copy($from), $expected];

        $from->setUrl('url');
        $expected['url'] = 'url';

        yield 'url' => [deep_copy($from), $expected];

        $from->setUrlHref('urlHref');
        $expected['urlHref'] = 'urlHref';

        yield 'urlHref' => [deep_copy($from), $expected];

        $from->setUrlAlt('urlAlt');
        $expected['urlAlt'] = 'urlAlt';

        yield 'urlAlt' => [deep_copy($from), $expected];

        $from->setPhone('phone');
        $expected['phone'] = 'phone';

        yield 'phone' => [deep_copy($from), $expected];

        $from->setFax('fax');
        $expected['fax'] = 'fax';

        yield 'fax' => [deep_copy($from), $expected];
    }
}
