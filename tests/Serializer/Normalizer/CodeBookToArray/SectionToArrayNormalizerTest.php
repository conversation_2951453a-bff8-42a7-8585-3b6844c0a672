<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Volume;
use App\Enum\QrBusinessUnit;
use App\Enum\QrDepartment;
use App\Serializer\Dto\Book\SectionDto;
use App\Serializer\Normalizer\CodeBookToArray\SectionToArrayNormalizer;
use Symfony\Component\Serializer\Serializer;

use function DeepCopy\deep_copy;

class SectionToArrayNormalizerTest extends AbstractCodeBookToArrayNormalizer
{
    private ?SectionToArrayNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(SectionToArrayNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $this->assertTrue($this->normalizer->supportsNormalization(new Section(), 'json'));
        $this->assertTrue($this->serializer->supportsNormalization(new Section(), 'json'));

        $this->assertFalse($this->normalizer->supportsNormalization(new Section()));
        $this->assertFalse($this->normalizer->supportsNormalization(new Volume()));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(Section $fromNode, array $expected, bool $bookContents = false): void
    {
        $actual = $this->normalizer->normalize($fromNode, 'json', ['bookContents' => $bookContents]);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $from = new Section();
        $from->setUlid('uuid');
        $from->setNodeId('nodeId');
        $from->setQrId('nodeId');
        $from->setRole('');
        $expected = [
            '_type'                => 'section',
            'dataType'             => 'section',

            // attributes
            'id'                   => 'nodeId',
            'uuid'                 => 'uuid',
            'ctXmlId'              => 'nodeId',
            'role'                 => '',
            'display'              => '',
            'verbatim'             => false,
            'language'             => '',
            'additionalInfo'       => '',
            'revisionBy'           => '',
            'revisionDateTime'     => null,
            'revision'             => '',
            'revisionGroup'        => '',
            'dataChanged'          => '',
            'dataChangedIn'        => '',
            'relocatedFromAttr'    => '',
            'deletedBy'            => '',
            'deletedDate'          => null,
            'indexNumber'          => '',
            'tocEntry'             => false,
            'reserveCount'         => 0,
            'displayLevel'         => 1,

            // title group
            'superTitle'           => '',
            'committeeDesignation' => '',
            'label'                => '',
            'number'               => '',
            'correlated'           => '',
            'title'                => '',
            'titleAbbreviation'    => '',
            'titleYear'            => '',
            'subTitle'             => '',

            // qr code
            'qrActive'             => false,
            'qrId'                 => 'nodeId',
            'qrDisplay'            => false,
            'qrLevelReference'     => '',
            'qrPurpose'            => '',
            'qrImage'              => '',
            'qrShortUrl'           => '',
            'qrBookIcon'           => '',
            'qrUrl'                => '',
            'qrIcon'               => '',
            'qrDepartment'         => QrDepartment::DEFAULT,
            'qrBusinessUnit'       => QrBusinessUnit::DEFAULT,

            // fields
            'history'              => '',
            'objectivesTitle'      => '',
            'objectives'           => '',
            'abstractTitle'        => '',
            'abstract'             => '',
            'keywordsTitle'        => '',
            'keywords'             => '',
            'body'                 => '',
            'children'             => [],

            // internal
            'status'               => 'IN_PROGRESS',
            'showDeletionMarker'   => false,
            'codesNotes'           => '',
            'pubsNotes'            => '',
            'typesetterNotes'      => '',
            'hasCodesNotes'        => false,
            'hasPubsNotes'         => false,
            'hasTypesetterNotes'   => false,
            'hasApprovedChanges'   => false,
            'hasFullyIncorporatedChanges' => false,
        ];

        yield 'baseline' => [deep_copy($from), $expected];

        yield from $this->commonMapCases($from, $expected);
        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);
        yield from $this->codeChangeMapCases($from, $expected);

        $from->setIndexNumber('indexNumber');
        $expected['indexNumber'] = 'indexNumber';

        yield 'indexNumber' => [deep_copy($from), $expected];

        $from->setTocEntry(true);
        $expected['tocEntry'] = true;

        yield 'tocEntry' => [deep_copy($from), $expected];

        $from->setReserveCount(100);
        $expected['reserveCount'] = 100;

        yield 'reserveCount' => [deep_copy($from), $expected];

        $from->setDisplayLevel(100);
        $expected['displayLevel'] = 100;

        yield 'displayLevel' => [deep_copy($from), $expected];

        $from->setHistory('history');
        $expected['history'] = 'history';

        yield 'history' => [deep_copy($from), $expected];

        $from->setObjectivesTitle('objectivesTitle');
        $expected['objectivesTitle'] = 'objectivesTitle';

        yield 'objectivesTitle' => [deep_copy($from), $expected];

        $from->setObjectives('objectives');
        $expected['objectives'] = 'objectives';

        yield 'objectives' => [deep_copy($from), $expected];

        $from->setAbstractTitle('abstractTitle');
        $expected['abstractTitle'] = 'abstractTitle';

        yield 'abstractTitle' => [deep_copy($from), $expected];

        $from->setAbstract('abstract');
        $expected['abstract'] = 'abstract';

        yield 'abstract' => [deep_copy($from), $expected];

        $from->setKeywordsTitle('keywordsTitle');
        $expected['keywordsTitle'] = 'keywordsTitle';

        yield 'keywordsTitle' => [deep_copy($from), $expected];

        $from->setKeywords('keywords');
        $expected['keywords'] = 'keywords';

        yield 'keywords' => [deep_copy($from), $expected];

        $from->setBody('body');
        $expected['body'] = 'body';

        yield 'body' => [deep_copy($from), $expected];

        $child = new Section();
        $child->setNodeId('nodeId');
        $child->setUlid('uuid');
        $from->setChildren([
            clone $child,
            clone $child,
        ]);

        $childDto = new SectionDto();
        $childDto->setId('nodeId');
        $childDto->setCtXmlId('nodeId');
        $childDto->setUuid('uuid');
        $expected['children'] = [
            $childDto->asArray(),
            $childDto->asArray(),
        ];

        yield 'children' => [deep_copy($from), $expected];
    }
}
