<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer\CodeBookToArray;

use App\Entity\CodeBook\Table;
use App\Entity\CodeBook\Volume;
use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\Enum\QrBusinessUnit;
use App\Enum\QrDepartment;
use App\Enum\Rules;
use App\Serializer\Dto\Book\TableDto;
use App\Serializer\Normalizer\CodeBookToArray\TableToArrayNormalizer;
use Symfony\Component\Serializer\Serializer;

use function DeepCopy\deep_copy;

class TableToArrayNormalizerTest extends AbstractCodeBookToArrayNormalizer
{
    private ?TableToArrayNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(TableToArrayNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $this->assertTrue($this->normalizer->supportsNormalization(new Table(), 'json'));
        $this->assertTrue($this->serializer->supportsNormalization(new Table(), 'json'));

        $this->assertFalse($this->normalizer->supportsNormalization(new Table()));
        $this->assertFalse($this->normalizer->supportsNormalization(new Volume()));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(Table $fromNode, array $expected, bool $bookContents = false): void
    {
        $actual = $this->normalizer->normalize($fromNode, 'json', ['bookContents' => $bookContents]);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $from = new Table();
        $from->setUlid('uuid');
        $from->setNodeId('nodeId');
        $from->setQrId('nodeId');
        $from->setRole('');
        $expected = [
            '_type'                => 'table',
            'dataType'             => 'table',

            // attributes
            'id'                   => 'nodeId',
            'uuid'                 => 'uuid',
            'ctXmlId'              => 'nodeId',
            'role'                 => '',
            'display'              => '',
            'verbatim'             => false,
            'language'             => '',
            'additionalInfo'       => '',
            'revisionBy'           => '',
            'revisionDateTime'     => null,
            'revision'             => '',
            'revisionGroup'        => '',
            'dataChanged'          => '',
            'dataChangedIn'        => '',
            'relocatedFromAttr'    => '',
            'deletedBy'            => '',
            'deletedDate'          => null,
            // cals
            'orientation'          => Orientation::PORTRAIT,
            'float'                => '',
            'tocEntry'             => false,
            'pageWide'             => false,
            'frame'                => Frame::NONE,
            'columnSeparator'      => false,
            'rowSeparator'         => false,
            'backgroundColor'      => '',
            'tableStyle'           => '',
            // html
            'class'                => '',
            'titleAttr'            => '',
            'summary'              => '',
            'width'                => '',
            'border'               => '',
            'cellSpacing'          => 0,
            'cellPadding'          => 0,
            'rules'                => Rules::NONE,

            // title group
            'superTitle'           => '',
            'committeeDesignation' => '',
            'label'                => '',
            'number'               => '',
            'correlated'           => '',
            'title'                => '',
            'titleAbbreviation'    => '',
            'titleYear'            => '',
            'subTitle'             => '',

            // qr code
            'qrActive'             => false,
            'qrId'                 => 'nodeId',
            'qrDisplay'            => false,
            'qrLevelReference'     => '',
            'qrPurpose'            => '',
            'qrImage'              => '',
            'qrShortUrl'           => '',
            'qrBookIcon'           => '',
            'qrUrl'                => '',
            'qrIcon'               => '',
            'qrDepartment'         => QrDepartment::DEFAULT,
            'qrBusinessUnit'       => QrBusinessUnit::DEFAULT,

            // fields
            'table'                => '',
            'caption'              => '',
            'tableNotesTitle'      => '',
            'tableNotes'           => '',
            'legend'               => '',
            'source'               => '',
            'creditTitle'          => '',
            'credit'               => '',

            // internal
            'status'               => 'IN_PROGRESS',
            'showDeletionMarker'   => false,
            'codesNotes'           => '',
            'pubsNotes'            => '',
            'typesetterNotes'      => '',
            'hasCodesNotes'        => false,
            'hasPubsNotes'         => false,
            'hasTypesetterNotes'   => false,
            'hasApprovedChanges'   => false,
            'hasFullyIncorporatedChanges' => false,
        ];

        yield 'baseline' => [deep_copy($from), $expected];

        yield from $this->commonMapCases($from, $expected);
        yield from $this->commonAttributeMapCases($from, $expected);
        yield from $this->revisionAttributeMapCases($from, $expected);
        yield from $this->titleGroupMapCases($from, $expected);
        yield from $this->qrCodeMapCases($from, $expected);
        yield from $this->codeChangeMapCases($from, $expected);

        $from->setOrientation(Orientation::LANDSCAPE);
        $expected['orientation'] = Orientation::LANDSCAPE;

        yield 'orientation' => [deep_copy($from), $expected];

        $from->setFloat(FloatEnum::MARGIN);
        $expected['float'] = FloatEnum::MARGIN;

        yield 'float' => [deep_copy($from), $expected];

        $from->setTocEntry(true);
        $expected['tocEntry'] = true;

        yield 'tocEntry' => [deep_copy($from), $expected];

        $from->setPageWide(true);
        $expected['pageWide'] = true;

        yield 'pageWide' => [deep_copy($from), $expected];

        $from->setFrame(Frame::ABOVE);
        $expected['frame'] = Frame::ABOVE;

        yield 'frame' => [deep_copy($from), $expected];

        $from->setColumnSeparator(true);
        $expected['columnSeparator'] = true;

        yield 'columnSeparator' => [deep_copy($from), $expected];

        $from->setRowSeparator(true);
        $expected['rowSeparator'] = true;

        yield 'rowSeparator' => [deep_copy($from), $expected];

        $from->setBackgroundColor('backgroundColor');
        $expected['backgroundColor'] = 'backgroundColor';

        yield 'backgroundColor' => [deep_copy($from), $expected];

        $from->setTableStyle('tableStyle');
        $expected['tableStyle'] = 'tableStyle';

        yield 'tableStyle' => [deep_copy($from), $expected];

        $from->setClass('class');
        $expected['class'] = 'class';

        yield 'class' => [deep_copy($from), $expected];

        $from->setTitleAttr('titleAttr');
        $expected['titleAttr'] = 'titleAttr';

        yield 'titleAttr' => [deep_copy($from), $expected];

        $from->setSummary('summary');
        $expected['summary'] = 'summary';

        yield 'summary' => [deep_copy($from), $expected];

        $from->setWidth('width');
        $expected['width'] = 'width';

        yield 'width' => [deep_copy($from), $expected];

        $from->setBorder('border');
        $expected['border'] = 'border';

        yield 'border' => [deep_copy($from), $expected];

        $from->setCellSpacing(100);
        $expected['cellSpacing'] = 100;

        yield 'cellSpacing' => [deep_copy($from), $expected];

        $from->setCellPadding(100);
        $expected['cellPadding'] = 100;

        yield 'cellPadding' => [deep_copy($from), $expected];

        $from->setRules(Rules::ROWS);
        $expected['rules'] = Rules::ROWS;

        yield 'rules' => [deep_copy($from), $expected];

        $from->setTable('table');
        $expected['table'] = 'table';

        yield 'table' => [deep_copy($from), $expected];

        $from->setCaption('caption');
        $expected['caption'] = 'caption';

        yield 'caption' => [deep_copy($from), $expected];

        $from->setTableNotesTitle('tableNotesTitle');
        $expected['tableNotesTitle'] = 'tableNotesTitle';

        yield 'tableNotesTitle' => [deep_copy($from), $expected];

        $from->setTableNotes('tableNotes');
        $expected['tableNotes'] = 'tableNotes';

        yield 'tableNotes' => [deep_copy($from), $expected];

        $from->setLegend('legend');
        $expected['legend'] = 'legend';

        yield 'legend' => [deep_copy($from), $expected];

        $from->setSource('source');
        $expected['source'] = 'source';

        yield 'source' => [deep_copy($from), $expected];

        $from->setCreditTitle('creditTitle');
        $expected['creditTitle'] = 'creditTitle';

        yield 'creditTitle' => [deep_copy($from), $expected];

        $from->setCredit('credit');
        $expected['credit'] = 'credit';

        yield 'credit' => [deep_copy($from), $expected];
    }
}
