<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer;

use App\Entity\Project;
use App\Entity\ProjectCategory;
use App\Serializer\Dto\Project\ProjectCategoryDto;
use App\Serializer\Normalizer\ProjectCategoryDtoNormalizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Serializer;

use function json_decode;
use function json_encode;

/**
 * @covers \App\Serializer\Normalizer\ProjectCategoryDtoNormalizer
 */
class ProjectCategoryDtoNormalizerTest extends KernelTestCase
{
    private ProjectCategoryDtoNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(ProjectCategoryDtoNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $type = new ProjectCategory();
        $this->assertTrue($this->normalizer->supportsNormalization($type, 'dto'));

        $this->assertFalse($this->normalizer->supportsNormalization($type, 'json'));
        $this->assertFalse($this->normalizer->supportsNormalization(new Project(), 'dto'));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(ProjectCategory $entity, ProjectCategoryDto $dto): void
    {
        $actual = $this->serializer->normalize($entity, 'dto', ['groups' => ['project:read']]);
        $expected = json_decode(json_encode($dto), true);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $entity = new ProjectCategory();
        $dto = new ProjectCategoryDto();

        yield 'base' => [$entity, $dto];

        $entity->setName('name');
        $dto->name = 'name';

        yield 'name' => [$entity, $dto];
    }
}
