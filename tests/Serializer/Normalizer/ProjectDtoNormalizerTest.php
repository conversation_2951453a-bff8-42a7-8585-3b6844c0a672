<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer;

use App\Entity\Project;
use App\Entity\ProjectCategory;
use App\Entity\ProjectType;
use App\Entity\User\User;
use App\Entity\VersionType;
use App\Enum\ProposalEvaluationStatus;
use App\Helper\DateTimeHelper;
use App\Serializer\Dto\Project\ProjectDto;
use App\Serializer\Dto\Project\ProjectTypeDto;
use App\Serializer\Dto\Project\VersionTypeDto;
use App\Serializer\Normalizer\ProjectDtoNormalizer;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Serializer;

use function DeepCopy\deep_copy;
use function json_decode;
use function json_encode;

/**
 * @covers \App\Serializer\Normalizer\ProjectDtoNormalizer
 */
class ProjectDtoNormalizerTest extends KernelTestCase
{
    private ProjectDtoNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(ProjectDtoNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $project = new Project();
        $this->assertTrue($this->normalizer->supportsNormalization($project, 'dto', ['groups' => ['project:read']]));

        $this->assertFalse($this->normalizer->supportsNormalization($project, 'json'));
        $this->assertFalse($this->normalizer->supportsNormalization($project, 'dto'));
        $this->assertFalse($this->normalizer->supportsNormalization($project, 'dto', ['groups' => ['user:read']]));
        $this->assertFalse($this->normalizer->supportsNormalization(new User(), 'dto', ['groups' => ['project:read']]));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(Project $entity, ProjectDto $dto): void
    {
        $actual = $this->serializer->normalize($entity, 'dto', ['groups' => ['project:read']]);
        $expected = json_decode(json_encode($dto), true);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $entity = new Project();
        $dto = new ProjectDto();
        $dto->createdDate = DateTimeHelper::parseToString($entity->getCreatedDate());
        $dto->lastModified = DateTimeHelper::parseToString($entity->getLastModifiedDate());
        $dto->lifeCycle = 'Active';
        $dto->proposalsEvaluationStatus = ProposalEvaluationStatus::IDLE;
        $dto->lastProposalsEvaluationAt = null;

        yield 'base' => [deep_copy($entity), deep_copy($dto)];

        $entity->setShortCode('shortCode');
        $dto->shortCode = 'shortCode';

        yield 'shortCode' => [deep_copy($entity), deep_copy($dto)];

        $entity->setBaseBook('baseBook');
        $dto->baseBook = 'baseBook';

        yield 'baseBook' => [deep_copy($entity), deep_copy($dto)];

        $entity->setBookTitle('bookTitle');
        $dto->title = 'bookTitle';

        yield 'bookTitle' => [deep_copy($entity), deep_copy($dto)];

        $entity->setWorkingTitle('workingTitle');
        $dto->workingTitle = 'workingTitle';

        yield 'workingTitle' => [deep_copy($entity), deep_copy($dto)];

        $projectType = new ProjectType();
        $projectType->setName('projectTypeName');
        $entity->setProjectType($projectType);

        $dto->projectType = new ProjectTypeDto();
        $dto->projectType->id = 0;
        $dto->projectType->name = 'projectTypeName';

        yield 'projectType' => [deep_copy($entity), deep_copy($dto)];

        $versionType = new VersionType();
        $versionType->setName('versionTypeName');
        $entity->setVersionType($versionType);

        $dto->versionType = new VersionTypeDto();
        $dto->versionType->id = 0;
        $dto->versionType->name = 'versionTypeName';

        yield 'versionType' => [deep_copy($entity), deep_copy($dto)];

        $projectCategory = new ProjectCategory();
        $projectCategory->setName('projectCategoryName');
        $entity->setProjectCategory($projectCategory);

        $dto->category = 'projectCategoryName';

        yield 'category' => [deep_copy($entity), deep_copy($dto)];

        $user = new User();
        $user->setFirstName('category');
        $user->setLastName('user');
        $user->addRole('ROLE_EDITOR');
        $projectCategory->addUser($user);

        $dto->categoryUsers[0] = [
            'id'    => null,
            'name'  => 'category user',
            'roles' => ['ROLE_EDITOR', 'ROLE_USER'],
        ];

        yield 'categoryUsers' => [deep_copy($entity), deep_copy($dto)];

        $dto->createdDate = '2021-01-01T00:00:00+00:00';
        $entity->setCreatedDate(new DateTime($dto->createdDate));

        yield 'createdDate' => [deep_copy($entity), deep_copy($dto)];

        $dto->lastModified = '2021-01-01T00:00:00+00:00';
        $entity->setLastModifiedDate(new DateTime($dto->lastModified));

        yield 'lastModified' => [deep_copy($entity), deep_copy($dto)];

        $entity->setActive(false);
        $dto->lifeCycle = 'InActive';

        yield 'active' => [deep_copy($entity), deep_copy($dto)];

        $entity->setBlueLine(true);
        $dto->lifeCycle = 'BlueLine';

        yield 'blueLine' => [deep_copy($entity), deep_copy($dto)];

        $entity->setCommentaryEnabled(true);
        $dto->commentaryEnabled = true;

        yield 'commentaryEnabled' => [deep_copy($entity), deep_copy($dto)];

        $entity->setHasCdpAccess(true);
        $dto->hasCdpAccess = true;

        yield 'hasCdpAccess' => [deep_copy($entity), deep_copy($dto)];

        //        $cycle = new CdpCycle();
        //        $cycle->setName('cycle');
        //        $cycle->setCdpId('123');
        //        $entity->setCycle($cycle);
        //
        //        $dto->cycle = new CycleDto();
        //        $dto->cycle->id = 0;
        //        $dto->cycle->name = 'cycle';
        //
        //        yield 'cycle' => [deep_copy($entity), deep_copy($dto)];

        $entity->setStatus(Project::STATUS_FAILED);
        $dto->status = Project::STATUS_FAILED;

        yield 'status' => [deep_copy($entity), deep_copy($dto)];

        $entity->setManualImagePath('/manualImagePath/');
        $dto->manualImagePath = '/manualImagePath/';

        yield 'manualImagePath' => [deep_copy($entity), deep_copy($dto)];

        $entity->setBlueLineUser('blueLineUser');
        $dto->blueLineUser = 'blueLineUser';

        yield 'blueLineUser' => [deep_copy($entity), deep_copy($dto)];

        $dto->blueLineDate = '2021-01-01T00:00:00+00:00';
        $entity->setBlueLineDate(new DateTime($dto->blueLineDate));

        yield 'blueLineDate' => [deep_copy($entity), deep_copy($dto)];

        $entity->setErrorMessage('errorMessage');
        $dto->errorMessage = 'errorMessage';

        yield 'errorMessage' => [deep_copy($entity), deep_copy($dto)];

        $entity->setProposalsEvaluationStatus(ProposalEvaluationStatus::QUEUED);
        $dto->proposalsEvaluationStatus = ProposalEvaluationStatus::QUEUED;
        yield 'proposalsEvaluationStatus' => [deep_copy($entity), deep_copy($dto)];

        $dto->lastProposalsEvaluationAt = '2021-02-03T04:05:06+00:00';
        $entity->setLastProposalsEvaluationAt(new DateTime($dto->lastProposalsEvaluationAt));
        yield 'lastProposalsEvaluationAt' => [deep_copy($entity), deep_copy($dto)];
    }
}
