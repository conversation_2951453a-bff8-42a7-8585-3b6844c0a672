<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer;

use App\Entity\Project;
use App\Entity\User\User;
use App\Serializer\Dto\UserDto;
use App\Serializer\Normalizer\UserDtoNormalizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Serializer;

use function json_decode;
use function json_encode;

/**
 * @covers \App\Serializer\Normalizer\UserDtoNormalizer
 */
class UserDtoNormalizerTest extends KernelTestCase
{
    private UserDtoNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(UserDtoNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $type = new User();
        $this->assertTrue($this->normalizer->supportsNormalization($type, 'dto'));

        $this->assertFalse($this->normalizer->supportsNormalization($type, 'json'));
        $this->assertFalse($this->normalizer->supportsNormalization(new Project(), 'dto'));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(User $entity, UserDto $dto): void
    {
        $actual = $this->serializer->normalize($entity, 'dto');
        $expected = json_decode(json_encode($dto), true);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $entity = new User();
        $dto = new UserDto();

        yield 'base' => [$entity, $dto];

        $entity->setEmail('email');
        $dto->email = 'email';

        yield 'email' => [$entity, $dto];

        $entity->setFirstName('firstName');
        $dto->firstName = 'firstName';

        yield 'firstName' => [$entity, $dto];

        $entity->setLastName('lastName');
        $dto->lastName = 'lastName';

        yield 'lastName' => [$entity, $dto];
    }
}
