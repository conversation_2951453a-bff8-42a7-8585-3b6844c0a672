<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Serializer\Normalizer;

use App\Entity\Project;
use App\Entity\ProjectType;
use App\Serializer\Dto\Project\ProjectTypeDto;
use App\Serializer\Normalizer\ProjectTypeDtoNormalizer;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Serializer;

use function json_decode;
use function json_encode;

/**
 * @covers \App\Serializer\Normalizer\ProjectTypeDtoNormalizer
 */
class ProjectTypeDtoNormalizerTest extends KernelTestCase
{
    private ProjectTypeDtoNormalizer $normalizer;
    private Serializer $serializer;

    protected function setUp(): void
    {
        $this->normalizer = self::getContainer()->get(ProjectTypeDtoNormalizer::class);
        $this->serializer = self::getContainer()->get('serializer');
    }

    public function testSupportsNormalization(): void
    {
        $projectType = new ProjectType();
        $this->assertTrue($this->normalizer->supportsNormalization($projectType, 'dto'));

        $this->assertFalse($this->normalizer->supportsNormalization($projectType, 'json'));
        $this->assertFalse($this->normalizer->supportsNormalization(new Project(), 'dto'));
    }

    /** @dataProvider normalizeCases */
    public function testNormalize(ProjectType $entity, ProjectTypeDto $dto): void
    {
        $actual = $this->serializer->normalize($entity, 'dto', ['groups' => ['project:read']]);
        $expected = json_decode(json_encode($dto), true);
        $this->assertEquals($expected, $actual);
    }

    public function normalizeCases(): iterable
    {
        $entity = new ProjectType();
        $dto = new ProjectTypeDto();

        yield 'base' => [$entity, $dto];

        $entity->setName('name');
        $dto->name = 'name';

        yield 'name' => [$entity, $dto];
    }
}
