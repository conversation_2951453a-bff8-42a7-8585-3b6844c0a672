<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\MessageHandler\Cdp;

use App\Entity\Cdp\CdpEndpoint;
use App\Entity\Cdp\CdpProposal;
use App\Enum\CdpEndpointStatus;
use App\Exception\ApiException;
use App\Message\Cdp\SyncProposalsFromEndpointMessage;
use App\MessageHandler\Cdp\SyncProposalsFromEndpointMessageHandler;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\Serializer;

class SyncProposalsFromEndpointMessageHandlerTest extends TestCase
{
    private EntityManagerInterface|MockObject $em;
    private EntityRepository|MockObject $repo;
    private LoggerInterface|MockObject $logger;
    private Serializer|MockObject $serializer;
    private SyncProposalsFromEndpointMessageHandler|MockObject $handler;
    private CdpEndpoint $endpoint;

    protected function setUp(): void
    {
        $this->endpoint = $this->createMock(CdpEndpoint::class);
        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->repo = $this->createMock(EntityRepository::class);
        $this->em
            ->expects($this->any())
            ->method('getRepository')
            ->with($this->equalTo(CdpEndpoint::class))
            ->willReturn($this->repo);

        $this->logger = $this->createMock(LoggerInterface::class);
        $this->serializer = $this->createMock(Serializer::class);
        $this->handler = $this->getMockBuilder(SyncProposalsFromEndpointMessageHandler::class)
            ->setConstructorArgs([$this->em, $this->logger, $this->serializer])
            ->onlyMethods(['getProposals'])
            ->getMock();
    }

    private function withEndpoint(?CdpEndpoint $endpoint): void
    {
        $this->repo
            ->expects($this->once())
            ->method('find')
            ->willReturn($endpoint);
    }

    public function testWhenNoEndpoint(): void
    {
        $this->expectException(ApiException::class);
        $this->withEndpoint(null);

        $message = new SyncProposalsFromEndpointMessage($this->endpoint);
        ($this->handler)($message);
    }

    public function testWhenNoProposals(): void
    {
        $this->withEndpoint($this->endpoint);
        $this->endpoint
            ->expects($this->exactly(2))
            ->method('setStatus')
            ->withConsecutive([CdpEndpointStatus::SYNCING], [CdpEndpointStatus::IDLE]);
        $this->em
            ->expects($this->exactly(2))
            ->method('flush');
        $this->em
            ->expects($this->never())
            ->method('persist');
        $this->handler
            ->expects($this->once())
            ->method('getProposals')
            ->willReturn([]);

        $message = new SyncProposalsFromEndpointMessage($this->endpoint);
        ($this->handler)($message);
    }

    public function testWithProposals(): void
    {
        $data = [
            'id'                => -9287,
            'title'             => 'ADM1-24',
            'tracking_number'   => 'ADM1-24',
            'group'             => '2024 Group A',
            'final_action'      => '',
            'committee_actions' => [
                'International Fuel Gas Code' => 'None',
            ],
            'reason_statement'  => '<p>Section 101.2.2.1</p><p>...</p>',
            'proposal_status'   => 'Ready for Final Admin',
            'cah1_action'       => 'Disapproved',
            'cah2_action'       => 'As Modified by Committee (AMC2)',
            'pch_action'        => 'None',
            'sections'          => [],
        ];
        $proposal = new CdpProposal();

        $this->withEndpoint($this->endpoint);
        $this->em
            ->expects($this->exactly(2))
            ->method('flush');
        $this->em
            ->expects($this->once())
            ->method('persist')
            ->with($this->equalTo($proposal));
        $this->handler
            ->expects($this->once())
            ->method('getProposals')
            ->willReturn([$data]);
        $this->serializer
            ->expects($this->once())
            ->method('denormalize')
            ->with($this->equalTo($data), $this->equalTo(CdpProposal::class), $this->equalTo('json'), $this->equalTo(['endpoint' => $this->endpoint]))
            ->willReturn($proposal);

        $message = new SyncProposalsFromEndpointMessage($this->endpoint);
        ($this->handler)($message);
    }
}
