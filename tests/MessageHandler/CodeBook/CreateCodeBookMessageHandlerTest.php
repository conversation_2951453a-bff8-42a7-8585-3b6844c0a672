<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\MessageHandler\CodeBook;

use App\Entity\BaseBook\AbstractBaseBookNode;
use App\Entity\CodeBook\Publication;
use App\Event\CodeBook\CreateCodeBookEvent;
use App\Event\CodeBook\PostCreateCodeBookEvent;
use App\Event\CodeBook\PreCreateCodeBookEvent;
use App\Exception\ApiException;
use App\Message\CodeBook\CreateCodeBookMessage;
use App\MessageHandler\CodeBook\CreateCodeBookMessageHandler;
use App\ObjectMapper\BaseBookToCodeBookMapper;
use App\Service\StopwatchService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Stopwatch\StopwatchEvent;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

class CreateCodeBookMessageHandlerTest extends TestCase
{
    private EventDispatcherInterface $eventDispatcher;
    private BaseBookToCodeBookMapper $baseBookToCodeBookMapper;
    private CreateCodeBookMessageHandler $handler;

    protected function setUp(): void
    {
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $this->baseBookToCodeBookMapper =$this->createMock(BaseBookToCodeBookMapper::class);

        $event = $this->createMock(StopwatchEvent::class);
        $event->expects($this->once())->method('start');
        $event->expects($this->once())->method('stop');
        $stopwatchService = $this->createMock(StopwatchService::class);
        $stopwatchService->expects($this->once())->method('create')->willReturn($event);

        $this->handler = new CreateCodeBookMessageHandler(
            $this->eventDispatcher,
            $this->baseBookToCodeBookMapper,
            $stopwatchService
        );
    }

    public function testInvokeSuccessfullyProcessesMessage(): void
    {
        $baseBook = $this->createMock(AbstractBaseBookNode::class);
        $codeBook = $this->createMock(Publication::class);

        $message = $this->createMock(CreateCodeBookMessage::class);
        $message
            ->expects($this->once())
            ->method('getBaseBook')
            ->willReturn($baseBook);
        $message
            ->expects($this->once())
            ->method('setCodeBook')
            ->with($codeBook);

        $this->baseBookToCodeBookMapper
            ->expects($this->once())
            ->method('map')
            ->with($baseBook)
            ->willReturn($codeBook);

        $matcher = $this->exactly(3);
        $this->eventDispatcher
            ->expects($this->exactly(3))
            ->method('dispatch')
            ->willReturnCallback(function ($event) use ($matcher, $baseBook, $codeBook) {
                  match ($matcher->getInvocationCount()) {
                      0 => true,
                      1 => $this->assertInstanceOf(PreCreateCodeBookEvent::class, $event),
                      2 => $this->assertInstanceOf(CreateCodeBookEvent::class, $event),
                      3 => $this->assertInstanceOf(PostCreateCodeBookEvent::class, $event),
                  };
                  return $event;
            });

        $this->handler->__invoke($message);
    }

    public function testInvokeThrowsApiExceptionWhenDenormalizationFails(): void
    {
        // Arrange
        $baseBook = $this->createMock(AbstractBaseBookNode::class);
        $exception = new ApiException();

        $message = $this->createMock(CreateCodeBookMessage::class);
        $message
            ->expects($this->once())
            ->method('getBaseBook')
            ->willReturn($baseBook);

        $this->baseBookToCodeBookMapper
            ->expects($this->once())
            ->method('map')
            ->with($baseBook)
            ->willThrowException($exception);

        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(PreCreateCodeBookEvent::class));

        $this->expectException(ApiException::class);

        $this->handler->__invoke($message);
    }
}
