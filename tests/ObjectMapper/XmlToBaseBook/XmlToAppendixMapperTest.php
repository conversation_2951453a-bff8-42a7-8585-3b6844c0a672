<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToAppendixMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\Body;
use App\Serializer\Encoder\Xml2\Element\History;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Note;
use App\Serializer\Encoder\Xml2\Element\Objectives;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToAppendixMapper
 */
class XmlToAppendixMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private XmlToAppendixMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToAppendixMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(Appendix $element, BaseBook\Appendix $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Appendix();
        $element->setId('id');
        $expected = new BaseBook\Appendix();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);
        yield from $this->qrCodeCases($element, $expected);

        $element->setIndexNumber('index-number');
        $expected->setIndexNumber('index-number');
        yield '@index-number' => [deep_copy($element), deep_copy($expected)];

        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@toc-entry' => [deep_copy($element), deep_copy($expected)];

        $element->setTocAutoAdd(true);
        $expected->setTocAutoAdd(true);
        yield '@toc-auto-add' => [deep_copy($element), deep_copy($expected)];

        $history = new History();
        $history->setBody('History');
        $element->setHistory($history);
        $expected->setHistory('History');
        yield 'history' => [deep_copy($element), deep_copy($expected)];

        $body = new Body();
        $body->setBody('<p>Body</p>');

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $objectives = new Objectives();
        $objectives->setBody($body);
        $element->setObjectives($objectives);
        $expected->setObjectives('<p>Body</p>');
        yield 'objectives' => [deep_copy($element), deep_copy($expected)];

        $objectives->setTitleGroup($titleGroup);
        $expected->setObjectivesTitle('Title');
        yield 'objectivesTitle' => [deep_copy($element), deep_copy($expected)];

        $note = new Note();
        $note->setBody('<p>Note</p>');
        $element->setNote($note);
        $expected->setNote('<p>Note</p>');
        yield 'note' => [deep_copy($element), deep_copy($expected)];

        $note->setTitleGroup($titleGroup);
        $expected->setNoteTitle('Title');
        yield 'noteTitle' => [deep_copy($element), deep_copy($expected)];

        $abstract = new AbstractField();
        $abstract->setBody('<p>Abstract</p>');
        $element->setAbstract($abstract);
        $expected->setAbstract('<p>Abstract</p>');
        yield 'abstract' => [deep_copy($element), deep_copy($expected)];

        $abstract->setTitleGroup($titleGroup);
        $expected->setAbstractTitle('Title');
        yield 'abstractTitle' => [deep_copy($element), deep_copy($expected)];

        $keywords = new Keywords();
        $keywords->setBody('<keyword>Keyword</keyword>');
        $element->setKeywords($keywords);
        $expected->setKeywords('<keyword>Keyword</keyword>');
        yield 'keywords' => [deep_copy($element), deep_copy($expected)];

        $keywords->setTitleGroup($titleGroup);
        $expected->setKeywordsTitle('Title');
        yield 'keywordsTitle' => [deep_copy($element), deep_copy($expected)];

        $body = new SectionBody();
        $body->setBody('<p>Body</p>');
        $element->setBody($body);
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        $level = new Level();
        $level->setId('level');
        $level->setRole('legis-section');
        $level->setDisplayLevel(1);
        $element->addChild($level);

        $expectedLevel = new BaseBook\Section();
        $expectedLevel->setRole('legis-section');
        $expectedLevel->setNodeId('level');
        $expectedLevel->setUlid('level.uuid');
        $expected->addChild($expectedLevel);
        yield 'level' => [deep_copy($element), deep_copy($expected)];

        $section = new Section();
        $section->setId('section');
        $section->setRole('section');
        $section->setDisplayLevel(1);
        $element->addChild($section);

        $expectedSection = new BaseBook\Section();
        $expectedSection->setRole('section');
        $expectedSection->setNodeId('section');
        $expectedSection->setUlid('section.uuid');
        $expected->addChild($expectedSection);
        yield 'section' => [deep_copy($element), deep_copy($expected)];

        $elementRelocatedTo = new RelocatedTo();
        $elementRelocatedTo->setId('relocatedTo');
        $element->addChild($elementRelocatedTo);

        $expectedRelocatedTo = new BaseBook\RelocatedTo();
        $expectedRelocatedTo->setNodeId('relocatedTo');
        $expectedRelocatedTo->setUlid('relocatedTo.uuid');
        $expected->addChild($expectedRelocatedTo);
        yield 'relocated-to' => [deep_copy($element), deep_copy($expected)];
    }
}
