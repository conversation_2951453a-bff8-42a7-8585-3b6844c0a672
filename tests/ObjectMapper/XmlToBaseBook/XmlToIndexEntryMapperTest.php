<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToIndexEntryMapper;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;
use App\Serializer\Encoder\Xml2\Element\PrimaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\SecondaryIndexEntry;
use App\Serializer\Encoder\Xml2\Element\Term;
use App\Serializer\Encoder\Xml2\Element\TertiaryIndexEntry;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToIndexEntryMapper
 */
class XmlToIndexEntryMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private ?XmlToIndexEntryMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToIndexEntryMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(IndexEntry $element, BaseBook\IndexEntry $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new IndexEntry();
        $element->setId('');

        $primaryIE = new PrimaryIndexEntry();
        $primaryIE->setId('id');
        $element->setPrimaryIndexEntry($primaryIE);

        $expected = new BaseBook\IndexEntry();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        $elementSecondary = new SecondaryIndexEntry();
        $elementSecondary->setId('secondary');
        $element->addChild($elementSecondary);

        $expectedSecondary = new BaseBook\SecondaryIndexEntry();
        $expectedSecondary->setNodeId('secondary');
        $expectedSecondary->setUlid('secondary.uuid');
        $expected->addChild($expectedSecondary);
        yield 'secondary' => [deep_copy($element), deep_copy($expected)];

        $elementTertiary = new TertiaryIndexEntry();
        $elementTertiary->setId('tertiary');
        $element->addChild($elementTertiary);

        $expectedTertiary = new BaseBook\TertiaryIndexEntry();
        $expectedTertiary->setNodeId('tertiary');
        $expectedTertiary->setUlid('tertiary.uuid');
        $expected->addChild($expectedTertiary);
        yield 'tertiary' => [deep_copy($element), deep_copy($expected)];
    }

    public function testPrimary(): void
    {
        $elementPrimary = new PrimaryIndexEntry();
        $elementPrimary->setId('id');

        $elementTerm = new Term();
        $elementTerm->setBody('Term');
        $elementPrimary->setTerm($elementTerm);
        $elementPrimary->setNavPointerGroup('<nav-pointer-group/>');

        $element = new IndexEntry();
        $element->setPrimaryIndexEntry($elementPrimary);

        $expected = new BaseBook\IndexEntry();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        $expected->setTerm('Term');
        $expected->setNavPointerGroup('<nav-pointer-group/>');

        $actual = $this->mapper->map($element);
        $actual->setUlid('id.uuid');
        $this->assertEquals($expected, $actual);
    }
}
