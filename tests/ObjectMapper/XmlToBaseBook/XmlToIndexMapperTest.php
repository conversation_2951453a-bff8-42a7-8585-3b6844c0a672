<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToIndexMapper;
use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToIndexMapper
 */
class XmlToIndexMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private XmlToIndexMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToIndexMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(Index $element, BaseBook\Index $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Index();
        $element->setId('id');

        $expected = new BaseBook\Index();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@toc-entry' => [deep_copy($element), deep_copy($expected)];

        $elementEntry = new IndexEntry();
        $elementEntry->setId('entry');
        $element->addChild($elementEntry);

        $expectedEntry = new BaseBook\IndexEntry();
        $expectedEntry->setNodeId('');
        $expectedEntry->setUlid('.uuid');
        $expected->addChild($expectedEntry);
        yield 'index-entry' => [deep_copy($element), deep_copy($expected)];

        $elementDivision = new IndexDivision();
        $elementDivision->setId('div');
        $element->addChild($elementDivision);

        $expectedDivision = new BaseBook\IndexDivision();
        $expectedDivision->setNodeId('div');
        $expectedDivision->setUlid('div.uuid');
        $expected->addChild($expectedDivision);
        yield 'index-div' => [deep_copy($element), deep_copy($expected)];
    }
}
