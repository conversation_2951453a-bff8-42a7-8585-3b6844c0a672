<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToIndexDivisionMapper;
use App\Serializer\Encoder\Xml2\Element\IndexDivision;
use App\Serializer\Encoder\Xml2\Element\IndexEntry;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToIndexDivisionMapper
 */
class XmlToIndexDivisionMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private ?XmlToIndexDivisionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToIndexDivisionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(IndexDivision $element, BaseBook\IndexDivision $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new IndexDivision();
        $element->setId('id');

        $expected = new BaseBook\IndexDivision();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $elementEntry = new IndexEntry();
        $elementEntry->setId('entry');
        $element->addChild($elementEntry);

        $expectedEntry = new BaseBook\IndexEntry();
        $expectedEntry->setNodeId('');
        $expectedEntry->setUlid('.uuid');
        $expected->addChild($expectedEntry);
        yield 'index-entry' => [deep_copy($element), deep_copy($expected)];

        $elementEntry = new IndexEntry();
        $elementEntry->setId('entry2');
        $element->addChild($elementEntry);

        $expectedEntry = new BaseBook\IndexEntry();
        $expectedEntry->setNodeId('');
        $expectedEntry->setUlid('.uuid');
        $expected->addChild($expectedEntry);
        yield 'index-entry2' => [deep_copy($element), deep_copy($expected)];
    }
}
