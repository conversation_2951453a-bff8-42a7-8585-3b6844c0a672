<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToSectionMapper;
use App\Serializer\Encoder\Xml2\Element\AbstractField;
use App\Serializer\Encoder\Xml2\Element\Keywords;
use App\Serializer\Encoder\Xml2\Element\Level;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\SectionBody;
use App\Serializer\Encoder\Xml2\Element\Title;
use App\Serializer\Encoder\Xml2\Element\TitleGroup;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToSectionMapper
 */
class XmlToSectionMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private ?XmlToSectionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToSectionMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(Level|Section $element, BaseBook\Section $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new Level();
        $element->setId('id');
        $expected = new BaseBook\Section();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@toc-entry' => [deep_copy($element), deep_copy($expected)];

        $element->setReserveCount('1');
        $expected->setReserveCount(1);
        yield '@reserve-count' => [deep_copy($element), deep_copy($expected)];

        $element->setDisplayLevel('1');
        $expected->setDisplayLevel(1);
        yield '@display-level' => [deep_copy($element), deep_copy($expected)];

        $title = new Title();
        $title->setBody('Title');
        $titleGroup = new TitleGroup();
        $titleGroup->setTitle($title);

        $abstract = new AbstractField();
        $abstract->setBody('<p>Abstract</p>');
        $element->setAbstract($abstract);
        $expected->setAbstract('<p>Abstract</p>');
        yield 'abstract' => [deep_copy($element), deep_copy($expected)];

        $abstract->setTitleGroup($titleGroup);
        $expected->setAbstractTitle('Title');
        yield 'abstractTitle' => [deep_copy($element), deep_copy($expected)];

        $keywords = new Keywords();
        $keywords->setBody('<keyword>Keyword</keyword>');
        $element->setKeywords($keywords);
        $expected->setKeywords('<keyword>Keyword</keyword>');
        yield 'keywords' => [deep_copy($element), deep_copy($expected)];

        $keywords->setTitleGroup($titleGroup);
        $expected->setKeywordsTitle('Title');
        yield 'keywordsTitle' => [deep_copy($element), deep_copy($expected)];

        $body = new SectionBody();
        $body->setBody('<p>Body</p>');
        $element->setBody($body);
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        $section1 = new Level();
        $section1->setId('section1');
        $section1->setRole('legis-section');
        $element->addChild($section1);

        $expectedSection1 = new BaseBook\Section();
        $expectedSection1->setRole('legis-section');
        $expectedSection1->setNodeId('section1');
        $expectedSection1->setUlid('section1.uuid');
        $expected->addChild($expectedSection1);
        yield 'section 1' => [deep_copy($element), deep_copy($expected)];

        $section2 = new Section();
        $section2->setId('section2');
        $section2->setRole('legis-section');
        $element->addChild($section2);

        $expectedSection2 = new BaseBook\Section();
        $expectedSection2->setRole('legis-section');
        $expectedSection2->setNodeId('section2');
        $expectedSection2->setUlid('section2.uuid');
        $expected->addChild($expectedSection2);
        yield 'section 2' => [deep_copy($element), deep_copy($expected)];
    }
}
