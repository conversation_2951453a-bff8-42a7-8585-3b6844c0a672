<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToCopyrightPageMapper;
use App\Serializer\Encoder\Xml2\Element\CopyrightPage;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToCopyrightPageMapper
 */
class XmlToCopyrightPageMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private ?XmlToCopyrightPageMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToCopyrightPageMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(CopyrightPage $element, BaseBook\CopyrightPage $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new CopyrightPage();
        $element->setId('id');
        $expected = new BaseBook\CopyrightPage();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $element->setBody('<p>Body</p>');
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];
    }
}
