<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToDefinitionListMapper;
use App\Serializer\Encoder\Xml2\Element\DefinitionItem;
use App\Serializer\Encoder\Xml2\Element\DefinitionList;
use App\Serializer\Encoder\Xml2\Element\RelocatedFrom;
use App\Serializer\Encoder\Xml2\Element\RelocatedTo;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToDefinitionListMapper
 */
class XmlToDefinitionListMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private XmlToDefinitionListMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToDefinitionListMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(DefinitionList $element, BaseBook\DefinitionList $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new DefinitionList();
        $element->setId('id');
        $expected = new BaseBook\DefinitionList();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $elementDefItem = new DefinitionItem();
        $elementDefItem->setId('def');
        $element->addChild($elementDefItem);

        $expectedDef = new BaseBook\Definition();
        $expectedDef->setNodeId('def');
        $expectedDef->setUlid('def.uuid');
        $expected->addChild($expectedDef);
        yield 'def-item' => [deep_copy($element), deep_copy($expected)];

        $elementRelocatedFrom = new RelocatedFrom();
        $elementRelocatedFrom->setId('relocatedFrom');
        $element->addChild($elementRelocatedFrom);

        $expectedRelocatedFrom = new BaseBook\RelocatedFrom();
        $expectedRelocatedFrom->setNodeId('relocatedFrom');
        $expectedRelocatedFrom->setUlid('relocatedFrom.uuid');
        $expected->addChild($expectedRelocatedFrom);
        yield 'relocated-from' => [deep_copy($element), deep_copy($expected)];

        $elementRelocatedTo = new RelocatedTo();
        $elementRelocatedTo->setId('relocatedTo');
        $element->addChild($elementRelocatedTo);

        $expectedRelocatedTo = new BaseBook\RelocatedTo();
        $expectedRelocatedTo->setNodeId('relocatedTo');
        $expectedRelocatedTo->setUlid('relocatedTo.uuid');
        $expected->addChild($expectedRelocatedTo);
        yield 'relocated-to' => [deep_copy($element), deep_copy($expected)];
    }
}
