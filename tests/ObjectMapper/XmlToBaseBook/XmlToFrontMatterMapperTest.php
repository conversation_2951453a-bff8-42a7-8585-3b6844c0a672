<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToFrontMatterMapper;
use App\Serializer\Encoder\Xml2\Element\CopyrightPage;
use App\Serializer\Encoder\Xml2\Element\Foreword;
use App\Serializer\Encoder\Xml2\Element\FrontMatter;
use App\Serializer\Encoder\Xml2\Element\Preface;
use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;
use App\Serializer\Encoder\Xml2\Element\TitlePage;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToFrontMatterMapper
 */
class XmlToFrontMatterMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private XmlToFrontMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToFrontMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(FrontMatter $element, BaseBook\FrontMatter $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new FrontMatter();
        $element->setId('id');
        $expected = new BaseBook\FrontMatter();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);

        $titlePage = new TitlePage();
        $titlePage->setId('titlePage');
        $element->setTitlePage($titlePage);

        $expectedTitlePage = new BaseBook\TitlePage();
        $expectedTitlePage->setUlid('titlePage.uuid');
        $expectedTitlePage->setNodeId('titlePage');
        $expected->setTitlePage($expectedTitlePage);
        yield 'titlePage' => [deep_copy($element), deep_copy($expected)];

        $copyrightPage = new CopyrightPage();
        $copyrightPage->setId('copyright');
        $element->setCopyrightPage($copyrightPage);

        $expectedCopyrightPage = new BaseBook\CopyrightPage();
        $expectedCopyrightPage->setUlid('copyright.uuid');
        $expectedCopyrightPage->setNodeId('copyright');
        $expected->setCopyrightPage($expectedCopyrightPage);
        yield 'copyrightPage' => [deep_copy($element), deep_copy($expected)];

        $publisherNote = new PublisherNote();
        $publisherNote->setId('pubNote');
        $element->setPublisherNote($publisherNote);

        $expectedPublisherNote = new BaseBook\PublisherNote();
        $expectedPublisherNote->setUlid('pubNote.uuid');
        $expectedPublisherNote->setNodeId('pubNote');
        $expected->setPublisherNote($expectedPublisherNote);
        yield 'publisherNote' => [deep_copy($element), deep_copy($expected)];

        $foreword = new Foreword();
        $foreword->setId('foreword');
        $element->setForeword($foreword);

        $expectedForeword = new BaseBook\Foreword();
        $expectedForeword->setUlid('foreword.uuid');
        $expectedForeword->setNodeId('foreword');
        $expected->setForeword($expectedForeword);
        yield 'foreword' => [deep_copy($element), deep_copy($expected)];

        $preface = new Preface();
        $preface->setId('preface');
        $element->setPreface($preface);

        $expectedPreface = new BaseBook\Preface();
        $expectedPreface->setUlid('preface.uuid');
        $expectedPreface->setNodeId('preface');
        $expected->setPreface($expectedPreface);
        yield 'preface' => [deep_copy($element), deep_copy($expected)];

        $section1 = new Section();
        $section1->setId('section1');
        $element->addChild($section1);

        $expectedSection1 = new BaseBook\Section();
        $expectedSection1->setUlid('section1.uuid');
        $expectedSection1->setNodeId('section1');
        $expected->addChild($expectedSection1);
        yield 'section1' => [deep_copy($element), deep_copy($expected)];

        $section2 = new Section();
        $section2->setId('section2');
        $element->addChild($section2);

        $expectedSection2 = new BaseBook\Section();
        $expectedSection2->setUlid('section2.uuid');
        $expectedSection2->setNodeId('section2');
        $expected->addChild($expectedSection2);
        yield 'section2' => [deep_copy($element), deep_copy($expected)];
    }
}
