<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToBackMatterMapper;
use App\Serializer\Encoder\Xml2\Element\Appendix;
use App\Serializer\Encoder\Xml2\Element\BackMatter;
use App\Serializer\Encoder\Xml2\Element\Index;
use App\Serializer\Encoder\Xml2\Element\Level;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToBackMatterMapper
 */
class XmlToBackMatterMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private XmlToBackMatterMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToBackMatterMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(BackMatter $element, BaseBook\BackMatter $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new BackMatter();
        $element->setId('id');

        $expected = new BaseBook\BackMatter();
        $expected->setUlid('uuid');
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);

        $appendix1 = new Appendix();
        $appendix1->setId('appendix1');
        $element->addChild($appendix1);

        $expectedAppendix1 = new BaseBook\Appendix();
        $expectedAppendix1->setUlid('appendix1.uuid');
        $expectedAppendix1->setNodeId('appendix1');
        $expected->addChild($expectedAppendix1);
        yield 'appendix1' => [deep_copy($element), deep_copy($expected)];

        $appendix2 = new Appendix();
        $appendix2->setId('appendix2');
        $element->addChild($appendix2);

        $expectedAppendix2 = new BaseBook\Appendix();
        $expectedAppendix2->setUlid('appendix2.uuid');
        $expectedAppendix2->setNodeId('appendix2');
        $expected->addChild($expectedAppendix2);
        yield 'appendix2' => [deep_copy($element), deep_copy($expected)];

        $level1 = new Level();
        $level1->setId('level1');
        $element->addChild($level1);

        $expectedLevel1 = new BaseBook\Section();
        $expectedLevel1->setUlid('level1.uuid');
        $expectedLevel1->setNodeId('level1');
        $expected->addChild($expectedLevel1);
        yield 'level1' => [deep_copy($element), deep_copy($expected)];

        $level2 = new Level();
        $level2->setId('level2');
        $element->addChild($level2);

        $expectedLevel2 = new BaseBook\Section();
        $expectedLevel2->setUlid('level2.uuid');
        $expectedLevel2->setNodeId('level2');
        $expected->addChild($expectedLevel2);
        yield 'level2' => [deep_copy($element), deep_copy($expected)];

        $index = new Index();
        $index->setId('index');
        $element->addChild($index);

        $expectedIndex = new BaseBook\Index();
        $expectedIndex->setUlid('index.uuid');
        $expectedIndex->setNodeId('index');
        $expected->addChild($expectedIndex);
        yield 'index' => [deep_copy($element), deep_copy($expected)];
    }
}
