<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\ObjectMapper\XmlToBaseBook;

use App\Entity\BaseBook;
use App\ObjectMapper\XmlToBaseBook\XmlToPublisherNoteMapper;
use App\Serializer\Encoder\Xml2\Element\PublisherNote;
use App\Serializer\Encoder\Xml2\Element\Section;

use function DeepCopy\deep_copy;

/**
 * @covers \App\ObjectMapper\XmlToBaseBook\AbstractXmlToBaseBookMapper
 * @covers \App\ObjectMapper\XmlToBaseBook\XmlToPublisherNoteMapper
 */
class XmlToPublisherNoteMapperTest extends AbstractMapperTestCase
{
    use MapperCasesTrait;

    private XmlToPublisherNoteMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(XmlToPublisherNoteMapper::class);
    }

    /** @dataProvider mapCases */
    public function testMap(PublisherNote $element, BaseBook\PublisherNote $expected): void
    {
        $actual = $this->mapper->map($element);
        foreach ($actual as $i) {
            $i->setUlid($i->getNodeId() . '.uuid');
        }
        $this->assertEquals($expected, $actual);
    }

    public function mapCases(): iterable
    {
        $element = new PublisherNote();
        $element->setId('id');
        $expected = new BaseBook\PublisherNote();
        $expected->setNodeId('id');
        $expected->setUlid('id.uuid');
        yield 'baseline' => [deep_copy($element), deep_copy($expected)];

        yield from $this->commonAttributeCases($element, $expected);
        yield from $this->revisionAttributeCases($element, $expected);
        yield from $this->titleGroupCases($element, $expected);

        $element->setTocEntry(true);
        $expected->setTocEntry(true);
        yield '@toc-entry' => [deep_copy($element), deep_copy($expected)];

        $element->setBody('<p>Body</p>');
        $expected->setBody('<p>Body</p>');
        yield 'body' => [deep_copy($element), deep_copy($expected)];

        $section1 = new Section();
        $section1->setId('section1');
        $element->addChild($section1);

        $expectedSection1 = new BaseBook\Section();
        $expectedSection1->setUlid('section1.uuid');
        $expectedSection1->setNodeId('section1');
        $expected->addChild($expectedSection1);
        yield 'section1' => [deep_copy($element), deep_copy($expected)];

        $section2 = new Section();
        $section2->setId('section2');
        $element->addChild($section2);

        $expectedSection2 = new BaseBook\Section();
        $expectedSection2->setUlid('section2.uuid');
        $expectedSection2->setNodeId('section2');
        $expected->addChild($expectedSection2);
        yield 'section2' => [deep_copy($element), deep_copy($expected)];
    }
}
