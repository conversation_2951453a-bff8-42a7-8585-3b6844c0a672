<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\Project\Action;

use App\Entity\Cdp\ProposalAction\CdpAppendixAction;
use App\Entity\Cdp\ProposalAction\CdpChapterAction;
use App\Entity\Cdp\ProposalAction\CdpDefinitionAction;
use App\Entity\Cdp\ProposalAction\CdpFigureAction;
use App\Entity\Cdp\ProposalAction\CdpPromulgatorAction;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Entity\Cdp\ProposalAction\CdpReferenceStandardAction;
use App\Entity\Cdp\ProposalAction\CdpSectionAction;
use App\Entity\Cdp\ProposalAction\CdpTableAction;
use App\Entity\Project;
use App\ObjectMapper\Project\Action\ProposalActionMapper;
use App\Service\Project\Action\ProposalActionPathIdService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ProposalActionPathIdServiceTest extends KernelTestCase
{
    private ProposalActionMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(ProposalActionMapper::class);
    }

    /**
     * @dataProvider appendixCases
     * @dataProvider chapterCases
     * @dataProvider definitionCases
     * @dataProvider figureCases
     * @dataProvider promulgatorCases
     * @dataProvider refStandardCases
     * @dataProvider sectionCases
     * @dataProvider tableCases
     */
    public function testUpdatePathId(CdpProposalAction $cdpAction, string $pathId, string $parentPathId = ''): void
    {
        $action = $this->mapper->map(new Project(), $cdpAction);
        $service = new ProposalActionPathIdService();
        $actual = $service->updatePathId($action);
        $this->assertSame($pathId, $actual);
        $this->assertSame($pathId, $action->getPathId());
        $this->assertSame($parentPathId, $action->getParentPathId());
    }

    public function appendixCases(): iterable
    {
        $cdpAction = new CdpAppendixAction();
        $cdpAction->setOriginalOrdinal('A');

        yield 'appendix / existing' => [$cdpAction, 'AppxA', 'BackMatter'];

        $cdpAction = new CdpAppendixAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('C');

        yield 'appendix / new' => [$cdpAction, 'AppxC', 'BackMatter'];

        $cdpAction = new CdpAppendixAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('<del>A</del><delete>A</delete><ins>C</ins><insert>C</insert>');

        yield 'appendix / change' => [$cdpAction, 'AppxCC', 'BackMatter'];

        $cdpAction = new CdpAppendixAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('Appendix C');

        yield 'appendix / label' => [$cdpAction, 'AppxC', 'BackMatter'];

        $cdpAction = new CdpAppendixAction();
        $cdpAction->setOriginalXmlId('IPC2024P1_AppxA');
        $cdpAction->setOriginalOrdinal('A');

        yield 'appendix / parent' => [$cdpAction, 'AppxA', 'BackMatter'];
    }

    public function chapterCases(): iterable
    {
        $cdpAction = new CdpChapterAction();
        $cdpAction->setOriginalOrdinal('1');

        yield 'chapter / existing' => [$cdpAction, 'Ch01', 'Volume0'];

        $cdpAction = new CdpChapterAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('3');

        yield 'chapter / new' => [$cdpAction, 'Ch03', 'Volume0'];

        $cdpAction = new CdpChapterAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('<del>1</del><delete>1</delete><ins>3</ins><insert>3</insert>');

        yield 'chapter / change' => [$cdpAction, 'Ch33', 'Volume0'];

        $cdpAction = new CdpChapterAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('Chapter 1');

        yield 'chapter / label' => [$cdpAction, 'Ch01', 'Volume0'];

        $cdpAction = new CdpChapterAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('111');

        yield 'chapter / 100+' => [$cdpAction, 'Ch111', 'Volume0'];

        $cdpAction = new CdpChapterAction();
        $cdpAction->setOriginalXmlId('IEBC2024P1_Ch16');
        $cdpAction->setOriginalOrdinal('16');

        yield 'chapter / no direct parent' => [$cdpAction, 'Ch16', 'Volume0'];

        $cdpAction = new CdpChapterAction();
        $cdpAction->setOriginalXmlId('IFC2024P1_Pt02_Ch03');
        $cdpAction->setOriginalOrdinal('3');

        yield 'chapter / direct parent' => [$cdpAction, 'Ch03', 'Pt02'];
    }

    public function definitionCases(): iterable
    {
        $cdpAction = new CdpDefinitionAction();
        $cdpAction->setOriginalOrdinal('202');
        $cdpAction->setOriginalTitle('ACCESS (TO)');
        $cdpAction->setTerm('');

        yield 'definition / existing' => [$cdpAction, 'DefACCESS_TO', 'Sec202'];

        $cdpAction = new CdpDefinitionAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setOriginalTitle('');
        $cdpAction->setTerm('READY ACCESS (TO)');

        yield 'definition / new' => [$cdpAction, 'DefREADY_ACCESS_TO', 'Sec202'];

        $cdpAction = new CdpDefinitionAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setOriginalTitle('');
        $cdpAction->setTerm('READY ACCESS<delete> (TO)</delete><insert> (FROM)</insert>');

        yield 'definition / change' => [$cdpAction, 'DefREADY_ACCESS_FROM', 'Sec202'];

        $cdpAction = new CdpDefinitionAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setOriginalTitle('');
        $cdpAction->setTerm('[F] COMPRESSED GAS');

        yield 'definition / label' => [$cdpAction, 'DefF_COMPRESSED_GAS', 'Sec202'];

        $cdpAction = new CdpDefinitionAction();
        $cdpAction->setOriginalXmlId('IFC2024P1_AppxA_DefWILDFIRE_RISK_AREA');
        $cdpAction->setOriginalOrdinal('A');
        $cdpAction->setOriginalTitle('Wildfire Risk Area.');
        $cdpAction->setTerm('');

        yield 'definition / parent appendix' => [$cdpAction, 'DefWILDFIRE_RISK_AREA', 'AppxA'];
    }

    public function figureCases(): iterable
    {
        $cdpAction = new CdpFigureAction();
        $cdpAction->setOriginalOrdinal('3203.9(1)');
        $cdpAction->setNumber('');

        yield 'figure / existing' => [$cdpAction, 'Fig3203.9_1', 'Sec3203.9'];

        $cdpAction = new CdpFigureAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('1207.11.7.1');

        yield 'figure / new' => [$cdpAction, 'Fig1207.11.7.1', 'Sec1207.11.7.1'];

        $cdpAction = new CdpFigureAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('<delete>3203.9(1)</delete><insert>1207.11.7.1</insert>');

        yield 'figure / change' => [$cdpAction, 'Fig1207.11.7.1', 'Sec1207.11.7.1'];

        $cdpAction = new CdpFigureAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('FIGURE H101.1(4)');

        yield 'figure / label' => [$cdpAction, 'FigH101.1_4', 'SecH101.1'];

        $cdpAction = new CdpFigureAction();
        $cdpAction->setOriginalXmlId('IRC2024P1_Pt07_Ch29_SecP2910.3_FigP2910.3');
        $cdpAction->setOriginalOrdinal('P2910.3');
        $cdpAction->setNumber('');

        yield 'figure / parent' => [$cdpAction, 'FigP2910.3', 'SecP2910.3'];
    }

    public function promulgatorCases(): iterable
    {
        $cdpAction = new CdpPromulgatorAction();
        $cdpAction->setOriginalXmlId('IMC2024P1_Ch15_PromSMACNA');
        $cdpAction->setAcronym('');

        yield 'promulgator / existing' => [$cdpAction, 'PromSMACNA', 'Ch15'];

        $cdpAction = new CdpPromulgatorAction();
        $cdpAction->setOriginalXmlId('');
        $cdpAction->setAcronym('CRRC');

        yield 'promulgator / new' => [$cdpAction, 'PromCRRC', ''];

        $cdpAction = new CdpPromulgatorAction();
        $cdpAction->setOriginalXmlId('');
        $cdpAction->setAcronym('<delete>SMACNA</delete><insert>CRRC</insert>');

        yield 'promulgator / change' => [$cdpAction, 'PromCRRC', ''];

        $cdpAction = new CdpPromulgatorAction();
        $cdpAction->setOriginalXmlId('');
        $cdpAction->setAcronym('[F] CRRC');

        yield 'promulgator / label' => [$cdpAction, 'PromF_CRRC', ''];

        $cdpAction = new CdpPromulgatorAction();
        $cdpAction->setOriginalXmlId('');
        $cdpAction->setAcronym('IAPMO');
        $cdpAction->setName('International Association of Plumbing and Mechanical Officials');
        $cdpAction->setUrl('https://dev.cdpaccess.com/proposal/9261/29796/preview/');

        yield 'promulgator / cdp 176804' => [$cdpAction, 'PromIAPMO', ''];
    }

    public function refStandardCases(): iterable
    {
        $cdpAction = new CdpReferenceStandardAction();
        $cdpAction->setPromulgatorAcronym('AHRI');
        $cdpAction->setOriginalOrdinal('AHRI 1380-2019');
        $cdpAction->setNumber('');

        yield 'refStandard / existing' => [$cdpAction, 'RefStdAHRI_1380', 'PromAHRI'];

        $cdpAction = new CdpReferenceStandardAction();
        $cdpAction->setPromulgatorAcronym('ICC');
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('C1549');

        yield 'refStandard / new' => [$cdpAction, 'RefStdC1549', 'PromICC'];

        $cdpAction = new CdpReferenceStandardAction();
        $cdpAction->setPromulgatorAcronym('ICC');
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('C1549');

        yield 'refStandard / change' => [$cdpAction, 'RefStdC1549', 'PromICC'];

        $cdpAction = new CdpReferenceStandardAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('C1549');

        yield 'refStandard / no parent' => [$cdpAction, 'RefStdC1549', ''];
    }

    public function sectionCases(): iterable
    {
        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalXmlId('IBC2024P1_Ch10_Sec1015.2');
        $cdpAction->setOriginalOrdinal('1015.2');
        $cdpAction->setNumber('');
        $cdpAction->setBody('');

        yield 'section / existing' => [$cdpAction, 'Sec1015.2', 'Sec1015'];

        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('1011.5.5.3');
        $cdpAction->setBody('');

        yield 'section / new' => [$cdpAction, 'Sec1011.5.5.3', 'Sec1011.5.5'];

        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('<insert>R404.7.6.1</insert>');
        $cdpAction->setBody('');

        yield 'section / change' => [$cdpAction, 'SecR404.7.6.1', 'SecR404.7.6'];

        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('[BE] R404.7.6.1');
        $cdpAction->setBody('');

        yield 'section / label' => [$cdpAction, 'SecBE_R404.7.6.1', 'SecBE_R404.7.6'];

        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalOrdinal('101');
        $cdpAction->setNumber('');
        $cdpAction->setBody('');

        yield 'section / parent length 3' => [$cdpAction, 'Sec101', 'Ch01'];

        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalOrdinal('1015');
        $cdpAction->setNumber('');
        $cdpAction->setBody('');

        yield 'section / parent length 4+' => [$cdpAction, 'Sec1015', 'Ch10'];

        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalXmlId('IFC2024P1_Pt07_AppxE_SecE104');
        $cdpAction->setOriginalOrdinal('E104');
        $cdpAction->setNumber('');
        $cdpAction->setBody('');

        yield 'section / parent xmlId' => [$cdpAction, 'SecE104', 'AppxE'];

        $cdpAction = new CdpSectionAction();
        $cdpAction->setOriginalOrdinal('E104');
        $cdpAction->setNumber('');
        $cdpAction->setBody('');

        yield 'section / parent appendix' => [$cdpAction, 'SecE104', 'AppxE'];
    }

    public function tableCases(): iterable
    {
        $cdpAction = new CdpTableAction();
        $cdpAction->setOriginalXmlId('IBC2024P1_Ch10_Sec1004.5_Tbl1004.5');
        $cdpAction->setOriginalOrdinal('1004.5');
        $cdpAction->setNumber('');
        $cdpAction->setBody('');

        yield 'table / existing' => [$cdpAction, 'Tbl1004.5', 'Sec1004.5'];

        $cdpAction = new CdpTableAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('1011.5.5.3 (2)');
        $cdpAction->setBody('');

        yield 'table / new' => [$cdpAction, 'Tbl1011.5.5.3_2', 'Sec1011.5.5.3'];

        $cdpAction = new CdpTableAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('<insert>R404.7.6.1</insert>');
        $cdpAction->setBody('');

        yield 'table / change' => [$cdpAction, 'TblR404.7.6.1', 'SecR404.7.6.1'];

        $cdpAction = new CdpTableAction();
        $cdpAction->setOriginalOrdinal('');
        $cdpAction->setNumber('TABLE R404.7.6.1');
        $cdpAction->setBody('');

        yield 'table / label' => [$cdpAction, 'TblR404.7.6.1', 'SecR404.7.6.1'];
    }
}
