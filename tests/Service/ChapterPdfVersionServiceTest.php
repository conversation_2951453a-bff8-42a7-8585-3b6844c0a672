<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service;

use App\Entity\ChapterPdfVersion;
use App\Entity\CodeBook\Chapter;
use App\Entity\Project;
use App\Entity\User\User;
use App\Service\ChapterPdfVersionService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

/**
 * @covers \App\Service\ChapterPdfVersionService
 */
class ChapterPdfVersionServiceTest extends KernelTestCase
{
    private ChapterPdfVersionService $service;
    private EntityManagerInterface $em;

    protected function setUp(): void
    {
        $this->service = self::getContainer()->get(ChapterPdfVersionService::class);
        $this->em = self::getContainer()->get(EntityManagerInterface::class);
    }

    public function testServiceExists(): void
    {
        $this->assertInstanceOf(ChapterPdfVersionService::class, $this->service);
    }

    public function testGetChapterVersionsWithValidProject(): void
    {
        $project = $this->em->getRepository(Project::class)->findOneBy([]);

        if (!$project) {
            $this->markTestSkipped('No project found in test database. Fixtures may not be loaded properly.');
        }

        $user = $this->em->getRepository(User::class)->findOneBy([]);

        if (!$user) {
            $this->markTestSkipped('No user found in test database. Fixtures may not be loaded properly.');
        }

        $result = $this->service->getChapterVersions($project, 'http://localhost');
        $this->assertIsArray($result->versionList);
    }

    public function testCreatePlaceholderVersions(): void
    {
        $project = $this->em->getRepository(Project::class)->findOneBy([]);
        $chapter = $this->em->getRepository(Chapter::class)->findOneBy([]);
        $user = $this->em->getRepository(User::class)->findOneBy([]);

        if (!$project || !$chapter || !$user) {
            $this->markTestSkipped('Required entities not found in test database. Fixtures may not be loaded properly.');
        }
        try {
            $versions = $this->service->createPdfVersionOnStatusChange(
                $chapter,
                $project,
                'WAITING_CODES',
                $user
            );

            $this->assertIsArray($versions);
            $this->assertArrayHasKey('without_notes', $versions);
            $this->assertArrayHasKey('with_notes', $versions);
            $this->assertInstanceOf(ChapterPdfVersion::class, $versions['without_notes']);
            $this->assertInstanceOf(ChapterPdfVersion::class, $versions['with_notes']);

        } catch (\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    public function testHasProjectAccessWithSuperAdmin(): void
    {
        $project = $this->em->getRepository(Project::class)->findOneBy([]);

        if (!$project) {
            $this->markTestSkipped('No project found in test database.');
        }

        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setRoles(['ROLE_SUPER_ADMIN']);

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('hasProjectAccess');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, $project, $user);
        $this->assertTrue($result);
    }

    public function testConvertChapterStatus(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('convertChapterStatus');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, 'WAITING_CODES');
        $this->assertEquals('Send to Codes', $result);

        $result = $method->invoke($this->service, 'WAITING_PUBS');
        $this->assertEquals('Send to Pubs', $result);

        $result = $method->invoke($this->service, 'UNKNOWN_STATUS');
        $this->assertEquals('UNKNOWN_STATUS', $result);
    }
}
