<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeXml\Mapper;

use App\Entity\CodeBook\Section;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeXml\CodeXmlNode;
use App\Service\CodeXml\Mapper\SectionXmlMapper;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Service\CodeXml\Mapper\SectionXmlMapper
 */
class SectionXmlMapperTest extends MapperTestCase
{
    private SectionXmlMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;
    private Xml2Encoder $xml2Encoder;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(SectionXmlMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
        $this->xml2Encoder = self::getContainer()->get(Xml2Encoder::class);
    }

    /** @dataProvider attributeCases */
    public function testMapAttributes(Section $entity, array $attrs): void
    {
        $attrs['ct-uuid'] = 'entity';
        $entity->setUlid('entity');

        $parent = new Section();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<section %s/>', $this->arrayToAttrs($attrs));
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider attributeCases */
    public function testSyncAttributes(Section $expected, array $attrs): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new Section();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = '<section id="expected" ct-uuid="expected"/>';
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        foreach ($attrs as $attr => $value) {
            $domEl->setAttribute($attr, $value);
        }

        $codeXmlNode = new CodeXmlNode($actual, $xml, $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue(empty($attrs) || $codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function attributeCases(): iterable
    {
        $entity = new Section();
        $entity->setNodeId('');
        $attrs = ['disp-level' => '1'];
        yield 'baseline' => [deep_copy($entity), $attrs];

        yield from $this->commonAttrCases($entity, $attrs);
        yield from $this->revisionAttrCases($entity, $attrs);

        $entity->setIndexNumber('indexNumber');
        $attrs['indexnum'] = 'indexNumber';
        yield 'attrs / @indexnum' => [deep_copy($entity), $attrs];

        $entity->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'attrs / @tocentry' => [deep_copy($entity), $attrs];

        $entity->setReserveCount(100);
        $attrs['reservecount'] = '100';
        yield 'attrs / @reservecount' => [deep_copy($entity), $attrs];

        $entity->setDisplayLevel(100);
        $attrs['disp-level'] = '100';
        yield 'attrs / @disp-level' => [deep_copy($entity), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testMapFields(Section $entity, string $innerXml): void
    {
        $parent = new Section();
        $parent->addChild($entity);
        $entity->setNodeId('entity');
        $entity->setUlid('entity');

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<section id="entity" ct-uuid="entity" disp-level="1">%s</section>', $innerXml);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider fieldCases */
    public function testSyncFields(Section $expected, string $innerXml): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new Section();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = sprintf('<section id="expected" ct-uuid="expected">%s</section>', $innerXml);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        $codeXmlNode = new CodeXmlNode($actual, '<section/>', $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue($codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function fieldCases(): iterable
    {
        $entity = new Section();
        $innerXml = '';
        yield 'baseline' => [deep_copy($entity), $innerXml];

        yield from $this->syncTitleGroupCase($entity, $innerXml);

        $innerXml .= <<<XML
<abstract>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <p>Abstract</p>
</abstract>
XML;
        $entity->setAbstract('<p>Abstract</p>');
        $entity->setAbstractTitle('Title');
        yield 'abstract' => [deep_copy($entity), $innerXml];

        $innerXml .= <<<XML
<keywords>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <keyword>Keyword</keyword>
</keywords>
XML;
        $entity->setKeywords('<keyword>Keyword</keyword>');
        $entity->setKeywordsTitle('Title');
        yield 'keywords' => [deep_copy($entity), $innerXml];

        $innerXml .= <<<XML
<body>
    <p>Body.</p><p>Body.</p><p>Body.</p>
</body>
XML;
        $entity->setBody('<p>Body.</p><p>Body.</p><p>Body.</p>');
        yield 'body' => [deep_copy($entity), $innerXml];
    }
}
