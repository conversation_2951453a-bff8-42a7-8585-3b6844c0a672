<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeXml\Mapper;

use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\TitlePage;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeXml\CodeXmlNode;
use App\Service\CodeXml\Mapper\TitlePageXmlMapper;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Service\CodeXml\Mapper\AbstractXmlMapper
 * @covers \App\Service\CodeXml\Mapper\FrontMatterXmlMapper
 */
class TitlePageXmlMapperTest extends MapperTestCase
{
    private TitlePageXmlMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;
    private Xml2Encoder $xml2Encoder;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(TitlePageXmlMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
        $this->xml2Encoder = self::getContainer()->get(Xml2Encoder::class);
    }

    /** @dataProvider attributeCases */
    public function testMapAttributes(TitlePage $entity, array $attrs): void
    {
        $attrs['ct-uuid'] = 'entity';
        $entity->setUlid('entity');

        $parent = new FrontMatter();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<titlepage %s/>', $this->arrayToAttrs($attrs));
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider attributeCases */
    public function testSyncAttributes(TitlePage $expected, array $attrs): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new TitlePage();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = '<titlepage id="expected"/>';
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        foreach ($attrs as $attr => $value) {
            $domEl->setAttribute($attr, $value);
        }

        $codeXmlNode = new CodeXmlNode($actual, $xml, $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue(empty($attrs) || $codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function attributeCases(): iterable
    {
        $entity = new TitlePage();
        $entity->setNodeId('');
        $attrs = [];
        yield 'baseline' => [deep_copy($entity), $attrs];

        yield from $this->commonAttrCases($entity, $attrs);
        yield from $this->revisionAttrCases($entity, $attrs);
    }

    /** @dataProvider fieldCases */
    public function testMapFields(TitlePage $entity, string $innerXml): void
    {
        $entity->setNodeId('entity');
        $entity->setUlid('entity');

        $parent = new FrontMatter();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<titlepage id="entity" ct-uuid="entity">%s</titlepage>', $innerXml);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider fieldCases */
    public function testSyncFields(TitlePage $expected, string $innerXml): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new TitlePage();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = sprintf('<titlepage id="expected" ct-uuid="expected">%s</titlepage>', $innerXml);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        $codeXmlNode = new CodeXmlNode($actual, '<titlepage/>', $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue($codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function fieldCases(): iterable
    {
        $entity = new TitlePage();
        $innerXml = '';
        yield 'baseline' => [deep_copy($entity), $innerXml];

        yield from $this->syncTitleGroupCase($entity, $innerXml);

        $entity->setBody('<mediaobject/>');
        $innerXml .= '<cover-image><mediaobject/></cover-image>';
        yield 'body' => [deep_copy($entity), $innerXml];
    }
}
