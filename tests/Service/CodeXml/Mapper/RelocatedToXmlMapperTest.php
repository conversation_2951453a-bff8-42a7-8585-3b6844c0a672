<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeXml\Mapper;

use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\RelocatedTo;
use App\Enum\Align;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeXml\CodeXmlNode;
use App\Service\CodeXml\Mapper\RelocatedToXmlMapper;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Service\CodeXml\Mapper\AbstractXmlMapper
 * @covers \App\Service\CodeXml\Mapper\RelocatedToXmlMapper
 */
class RelocatedToXmlMapperTest extends MapperTestCase
{
    private RelocatedToXmlMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;
    private Xml2Encoder $xml2Encoder;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(RelocatedToXmlMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
        $this->xml2Encoder = self::getContainer()->get(Xml2Encoder::class);
    }

    /** @dataProvider attributeCases */
    public function testMapAttributes(RelocatedTo $entity, array $attrs): void
    {
        $attrs['ct-uuid'] = 'entity';
        $entity->setUlid('entity');

        $parent = new Chapter();
        $parent->setRole('legis-section');
        $parent->setDisplayLevel(1);
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<relocated-to %s/>', $this->arrayToAttrs($attrs));
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider attributeCases */
    public function testSyncAttributes(RelocatedTo $expected, array $attrs): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new RelocatedTo();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');
        $actual->setAlign('');

        $xml = '<relocated-to id="expected"/>';
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        foreach ($attrs as $attr => $value) {
            $domEl->setAttribute($attr, $value);
        }

        $codeXmlNode = new CodeXmlNode($actual, $xml, $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue(empty($attrs) || $codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function attributeCases(): iterable
    {
        $entity = new RelocatedTo();
        $entity->setNodeId('');
        $entity->setAlign('');
        $attrs = [];
        yield 'baseline' => [deep_copy($entity), $attrs];

        yield from $this->commonAttrCases($entity, $attrs);
        yield from $this->revisionAttrCases($entity, $attrs);

        $entity->setAlign(Align::CENTER);
        $attrs['align'] = Align::CENTER;
        yield 'attrs / @align' => [deep_copy($entity), $attrs];

        $entity->setIndent(true);
        $attrs['indent'] = 'yes';
        yield 'attrs / @indent' => [deep_copy($entity), $attrs];

        $entity->setReferenceId('referenceId');
        $attrs['rid'] = 'referenceId';
        yield 'attrs / @referenceId' => [deep_copy($entity), $attrs];

        $entity->setRelocatedTo('relocatedTo');
        $attrs['relocated-to'] = 'relocatedTo';
        yield 'attrs / @relocatedTo' => [deep_copy($entity), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testMapFields(RelocatedTo $entity, string $innerXml): void
    {
        $entity->setNodeId('entity');
        $entity->setUlid('entity');

        $parent = new Chapter();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<relocated-to id="entity" ct-uuid="entity">%s</relocated-to>', $innerXml);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider fieldCases */
    public function testSyncFields(RelocatedTo $expected, string $innerXml): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new RelocatedTo();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');
        $actual->setAlign('');

        $xml = sprintf('<relocated-to id="expected" ct-uuid="expected">%s</relocated-to>', $innerXml);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        $codeXmlNode = new CodeXmlNode($actual, '<relocated-to/>', $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue($codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function fieldCases(): iterable
    {
        $entity = new RelocatedTo();
        $entity->setAlign('');
        $innerXml = '';
        yield 'baseline' => [deep_copy($entity), $innerXml];

        $entity->setBody('body');
        $innerXml = 'body';
        yield 'body' => [deep_copy($entity), $innerXml];
    }
}
