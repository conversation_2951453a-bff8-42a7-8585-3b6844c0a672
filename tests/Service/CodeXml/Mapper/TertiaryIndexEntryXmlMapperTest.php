<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeXml\Mapper;

use App\Entity\CodeBook\IndexEntry;
use App\Entity\CodeBook\TertiaryIndexEntry;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeXml\CodeXmlNode;
use App\Service\CodeXml\Mapper\TertiaryIndexEntryXmlMapper;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Service\CodeXml\Mapper\AbstractXmlMapper
 * @covers \App\Service\CodeXml\Mapper\SecondaryIndexEntryXmlMapper
 */
class TertiaryIndexEntryXmlMapperTest extends MapperTestCase
{
    private TertiaryIndexEntryXmlMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;
    private Xml2Encoder $xml2Encoder;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(TertiaryIndexEntryXmlMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
        $this->xml2Encoder = self::getContainer()->get(Xml2Encoder::class);
    }

    /** @dataProvider attributeCases */
    public function testMapAttributes(TertiaryIndexEntry $entity, array $attrs): void
    {
        $attrs['ct-uuid'] = 'entity';
        $entity->setUlid('entity');

        $parent = new IndexEntry();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<tertiaryie %s/>', $this->arrayToAttrs($attrs));
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider attributeCases */
    public function testSyncAttributes(TertiaryIndexEntry $expected, array $attrs): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new TertiaryIndexEntry();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = '<tertiaryie id="expected"/>';
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        foreach ($attrs as $attr => $value) {
            $domEl->setAttribute($attr, $value);
        }

        $codeXmlNode = new CodeXmlNode($actual, $xml, $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue(empty($attrs) || $codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function attributeCases(): iterable
    {
        $entity = new TertiaryIndexEntry();
        $entity->setNodeId('');
        $attrs = [];
        yield 'baseline' => [deep_copy($entity), $attrs];

        $this->commonAttrCases($entity, $attrs);
        $this->revisionAttrCases($entity, $attrs);

        $entity->setReferenceId('referenceId');
        $attrs['rid'] = 'referenceId';
        yield 'attrs / @rid' => [deep_copy($entity), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testMapFields(TertiaryIndexEntry $entity, string $innerXml): void
    {
        $entity->setNodeId('entity');
        $entity->setUlid('entity');

        $parent = new IndexEntry();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<tertiaryie ct-uuid="entity" id="entity">%s</tertiaryie>', $innerXml);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider fieldCases */
    public function testSyncFields(TertiaryIndexEntry $expected, string $innerXml): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new TertiaryIndexEntry();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = sprintf('<tertiaryie>%s</tertiaryie>', $innerXml);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        $codeXmlNode = new CodeXmlNode($actual, '<tertiaryie id="original"/>', $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue($codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function fieldCases(): iterable
    {
        $entity = new TertiaryIndexEntry();
        $innerXml = '';
        yield 'baseline' => [deep_copy($entity), $innerXml];

        $entity->setTerm('Term');
        $innerXml .= '<term>Term</term>';
        yield 'term' => [deep_copy($entity), $innerXml];

        $navPointerGroup = <<<XML
<nav-pointer-group>
    <nav-pointer rid="IBC2024V2.0_Ch16_Sec1604.3.5">1604.3.5</nav-pointer>
    <nav-pointer rid="IBC2024V2.0_Ch20_Sec2002.1">2002.1</nav-pointer>
</nav-pointer-group>
XML;
        $entity->setNavPointerGroup($navPointerGroup);
        $innerXml .= $navPointerGroup;
        yield 'nav-pointer-group' => [deep_copy($entity), $innerXml];
    }
}
