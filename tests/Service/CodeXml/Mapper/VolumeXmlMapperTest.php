<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeXml\Mapper;

use App\Entity\CodeBook\Publication;
use App\Entity\CodeBook\Volume;
use App\Enum\GoverningType;
use App\Enum\TitleType;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeXml\CodeXmlNode;
use App\Service\CodeXml\Mapper\VolumeXmlMapper;

use function DeepCopy\deep_copy;
use function sprintf;

/**
 * @covers \App\Service\CodeXml\Mapper\VolumeXmlMapper
 */
class VolumeXmlMapperTest extends MapperTestCase
{
    private VolumeXmlMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;
    private Xml2Encoder $xml2Encoder;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(VolumeXmlMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
        $this->xml2Encoder = self::getContainer()->get(Xml2Encoder::class);
    }

    /** @dataProvider attributeCases */
    public function testMapAttributes(Volume $entity, array $attrs): void
    {
        $attrs['ct-uuid'] = 'entity';
        $entity->setUlid('entity');

        $parent = new Publication();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<volume %s/>', $this->arrayToAttrs($attrs));
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider attributeCases */
    public function testSyncAttributes(Volume $expected, array $attrs): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new Volume();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = '<volume id="expected" ct-uuid="expected"/>';
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        foreach ($attrs as $attr => $value) {
            $domEl->setAttribute($attr, $value);
        }

        $codeXmlNode = new CodeXmlNode($actual, $xml, $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue(empty($attrs) || $codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function attributeCases(): iterable
    {
        $entity = new Volume();
        $entity->setNodeId('');
        $attrs = [];
        yield 'attrs / baseline' => [deep_copy($entity), $attrs];

        yield from $this->commonAttrCases($entity, $attrs);

        $entity->setCustomerId('customer');
        $attrs['customer-id'] = 'customer';
        yield 'attrs / @customer-id' => [deep_copy($entity), $attrs];

        $entity->setTitleType(TitleType::LAND_USE);
        $attrs['title-type'] = TitleType::LAND_USE;
        yield 'attrs / @title-type' => [deep_copy($entity), $attrs];

        $entity->setGoverningType(GoverningType::TOWN);
        $attrs['governing-type'] = GoverningType::TOWN;
        yield 'attrs / @governing-type' => [deep_copy($entity), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testMapFields(Volume $entity, string $innerXml): void
    {
        $parent = new Publication();
        $parent->addChild($entity);
        $entity->setNodeId('entity');
        $entity->setUlid('entity');

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<volume id="entity" ct-uuid="entity">%s</volume>', $innerXml);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider fieldCases */
    public function testSyncFields(Volume $expected, string $innerXml): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new Volume();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');

        $xml = sprintf('<volume id="expected" ct-uuid="expected">%s</volume>', $innerXml);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        $codeXmlNode = new CodeXmlNode($actual, '<volume/>', $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue($codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function fieldCases(): iterable
    {
        $entity = new Volume();
        $innerXml = '';
        yield 'baseline' => [deep_copy($entity), $innerXml];

        $innerXml .= <<<XML
<metadata>
    <meta name="title">Title</meta>
    <meta name="edition">2024 Edition</meta>
    <meta name="parent-document">IBC2024</meta>
    <meta name="publication-id">IBC2024V2.0</meta>
    <meta name="year">2024</meta>
    <meta name="publication-abbrev">IBC</meta>
    <meta name="version">Version 1.0</meta>
    <meta name="origin">IB</meta>
    <meta name="date-origin">2024-5-29</meta>
    <meta name="modified-by">Jeff Wight</meta>
    <meta name="date-updated">2025-06-10</meta>
</metadata>
XML;
        $entity->setMetaTitle('Title');
        $entity->setEdition('2024 Edition');
        $entity->setParentDocument('IBC2024');
        $entity->setPublicationId('IBC2024V2.0');
        $entity->setYear('2024');
        $entity->setPublicationAbbreviation('IBC');
        $entity->setVersion('Version 1.0');
        $entity->setOrigin('IB');
        $entity->setDateOrigin('2024-5-29');
        $entity->setModifiedBy('Jeff Wight');
        $entity->setDateUpdated('2025-06-10');
        yield 'metadata' => [deep_copy($entity), $innerXml];

        yield from $this->syncTitleGroupCase($entity, $innerXml);
    }
}
