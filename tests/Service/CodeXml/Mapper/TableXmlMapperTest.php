<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\CodeXml\Mapper;

use App\Entity\CodeBook\Section;
use App\Entity\CodeBook\Table;
use App\Enum\FloatEnum;
use App\Enum\Frame;
use App\Enum\Orientation;
use App\Enum\Rules;
use App\Helper\Xml2Helper;
use App\ObjectMapper\CodeBookToXmlMapper;
use App\Serializer\Encoder\Xml2\Xml2Encoder;
use App\Service\CodeXml\CodeXmlNode;
use App\Service\CodeXml\Mapper\TableXmlMapper;

use function DeepCopy\deep_copy;
use function sprintf;

class TableXmlMapperTest extends MapperTestCase
{
    private TableXmlMapper $mapper;
    private CodeBookToXmlMapper $codeBookToXmlMapper;
    private Xml2Encoder $xml2Encoder;

    protected function setUp(): void
    {
        $this->mapper = self::getContainer()->get(TableXmlMapper::class);
        $this->codeBookToXmlMapper = self::getContainer()->get(CodeBookToXmlMapper::class);
        $this->xml2Encoder = self::getContainer()->get(Xml2Encoder::class);
    }

    /** @dataProvider attributeCases */
    public function testMapAttributes(Table $entity, array $attrs): void
    {
        $attrs['ct-uuid'] = 'entity';
        $entity->setUlid('entity');

        $parent = new Section();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<table %s/>', $this->arrayToAttrs($attrs));
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider attributeCases */
    public function testSyncAttributes(Table $expected, array $attrs): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');

        $actual = new Table();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');
        $actual->setFrame('');
        $actual->setOrientation('');
        $actual->setRules('');

        $xml = '<table id="expected"/>';
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        foreach ($attrs as $attr => $value) {
            $domEl->setAttribute($attr, $value);
        }

        $codeXmlNode = new CodeXmlNode($actual, $xml, $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue(empty($attrs) || $codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function attributeCases(): iterable
    {
        $entity = new Table();
        $entity->setNodeId('');
        $entity->setFrame('');
        $entity->setOrientation('');
        $entity->setRules('');
        $attrs = [];
        yield 'baseline' => [deep_copy($entity), $attrs];

        yield from $this->commonAttrCases($entity, $attrs);
        yield from $this->revisionAttrCases($entity, $attrs);

        $entity->setOrientation(Orientation::LANDSCAPE);
        $attrs['orient'] = Orientation::LANDSCAPE;
        yield 'attrs / @orientation' => [deep_copy($entity), $attrs];

        $entity->setFloat(FloatEnum::MARGIN);
        $attrs['float'] = FloatEnum::MARGIN;
        yield 'attrs / @float' => [deep_copy($entity), $attrs];

        $entity->setTocEntry(true);
        $attrs['tocentry'] = 'yes';
        yield 'attrs / @tocEntry' => [deep_copy($entity), $attrs];

        $entity->setPageWide(true);
        $attrs['pgwide'] = 'yes';
        yield 'attrs / @pageWide' => [deep_copy($entity), $attrs];

        $entity->setFrame(Frame::TOP);
        $attrs['frame'] = Frame::TOP;
        yield 'attrs / @frame' => [deep_copy($entity), $attrs];

        $entity->setColumnSeparator(true);
        $attrs['colsep'] = '1';
        yield 'attrs / @columnSeparator' => [deep_copy($entity), $attrs];

        $entity->setRowSeparator(true);
        $attrs['rowsep'] = '1';
        yield 'attrs / @rowSeparator' => [deep_copy($entity), $attrs];

        $entity->setBackgroundColor('backgroundColor');
        $attrs['background-color'] = 'backgroundColor';
        yield 'attrs / @backgroundColor' => [deep_copy($entity), $attrs];

        $entity->setTableStyle('tableStyle');
        $attrs['tabstyle'] = 'tableStyle';
        yield 'attrs / @tableStyle' => [deep_copy($entity), $attrs];

        $entity->setClass('class');
        $attrs['class'] = 'class';
        yield 'attrs / @class' => [deep_copy($entity), $attrs];

        $entity->setTitleAttr('titleAttr');
        $attrs['title'] = 'titleAttr';
        yield 'attrs / @titleAttr' => [deep_copy($entity), $attrs];

        $entity->setSummary('summary');
        $attrs['summary'] = 'summary';
        yield 'attrs / @summary' => [deep_copy($entity), $attrs];

        $entity->setWidth('width');
        $attrs['width'] = 'width';
        yield 'attrs / @width' => [deep_copy($entity), $attrs];

        $entity->setBorder('border');
        $attrs['border'] = 'border';
        yield 'attrs / @border' => [deep_copy($entity), $attrs];

        $entity->setCellPadding(10);
        $attrs['cellpadding'] = '10';
        yield 'attrs / @cellPadding' => [deep_copy($entity), $attrs];

        $entity->setCellSpacing(20);
        $attrs['cellspacing'] = '20';
        yield 'attrs / @cellSpacing' => [deep_copy($entity), $attrs];

        $entity->setRules(Rules::COLS);
        $attrs['rules'] = Rules::COLS;
        yield 'attrs / @rules' => [deep_copy($entity), $attrs];
    }

    /** @dataProvider fieldCases */
    public function testMapFields(Table $entity, string $innerXml): void
    {
        $entity->setNodeId('entity');
        $entity->setUlid('entity');

        $parent = new Section();
        $parent->addChild($entity);

        $element = $this->codeBookToXmlMapper->map($parent);
        $xml = $this->xml2Encoder->encode($element);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $codeXmlNode = $this->mapper->map($entity, $document, $xpath);
        $domElement = $codeXmlNode->getDomElement();

        $this->assertNotNull($codeXmlNode);
        $this->assertSame($document, $domElement->ownerDocument);

        $expected = sprintf('<table id="entity" ct-uuid="entity">%s</table>', $innerXml);
        $this->assertXmlStringEqualsXmlString($expected, $domElement->ownerDocument->saveXML($domElement));
    }

    /** @dataProvider fieldCases */
    public function testSyncFields(Table $expected, string $innerXml): void
    {
        $expected->setNodeId('expected');
        $expected->setUlid('expected');
        $expected->setFrame('');
        $expected->setOrientation('');
        $expected->setRules('');

        $actual = new Table();
        $actual->setNodeId('expected');
        $actual->setUlid('expected');
        $actual->setFrame('');
        $actual->setOrientation('');
        $actual->setRules('');

        $xml = sprintf('<table id="expected" ct-uuid="expected">%s</table>', $innerXml);
        $document = Xml2Helper::createDOMDocument($xml, true);
        $xpath = Xml2Helper::createDOMXPath($document);

        $domEl = $document->documentElement->firstElementChild;
        $codeXmlNode = new CodeXmlNode($actual, '<table/>', $domEl);
        $this->mapper->syncEntity($codeXmlNode, $xpath);

        $this->assertTrue($codeXmlNode->hasChanges());
        $this->assertEquals($expected, $actual);
    }

    public function fieldCases(): iterable
    {
        $entity = new Table();
        $innerXml = '';
        yield 'baseline' => [deep_copy($entity), $innerXml];

        yield from $this->syncTitleGroupCase($entity, $innerXml);
        yield from $this->syncQrCodeCase($entity, $innerXml);

        $entity->setTable('<html-table/><mediaobject/><mediaobject-group/>');
        $innerXml .= '<html-table/><mediaobject/><mediaobject-group/>';
        yield 'table' => [deep_copy($entity), $innerXml];

        $entity->setCaption('<p>Caption</p>');
        $innerXml .= '<caption><p>Caption</p></caption>';
        yield 'caption' => [deep_copy($entity), $innerXml];

        $entity->setLegend('<legend>Legend 1</legend><legend>Legend 2</legend><legend>Legend 3</legend>');
        $innerXml .= '<legend>Legend 1</legend><legend>Legend 2</legend><legend>Legend 3</legend>';
        yield 'legend' => [deep_copy($entity), $innerXml];

        $entity->setTableNotes('<table-note ct-xml-id="entity_NOTE_01">Notes</table-note>');
        $entity->setTableNotesTitle('Title');
        $innerXml .= <<<XMLFRAG
<table-notes>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <table-note ct-xml-id="entity_NOTE_01">Notes</table-note>
</table-notes>
XMLFRAG;
        yield 'table-notes' => [deep_copy($entity), $innerXml];

        $entity->setSource('<p>Source</p>');
        $innerXml .= '<source><p>Source</p></source>';
        yield 'source' => [deep_copy($entity), $innerXml];

        $entity->setCredit('<p>Credit</p>');
        $entity->setCreditTitle('Title');
        $innerXml .= <<<XMLFRAG
<credit>
    <titlegroup>
        <title>Title</title>
    </titlegroup>
    <p>Credit</p>
</credit>
XMLFRAG;
        yield 'credit' => [deep_copy($entity), $innerXml];
    }
}
