<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\Xml2;

use App\Dto\Xml2\Relocation\GetRelocationOptionsRequest;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Section;
use App\Entity\Project;
use App\Enum\ObjectType;
use App\Repository\CodeBookNodeRepository;
use App\Service\CodeBook\CodeBookPathIdService;
use App\Service\Xml2\AppendixService;
use App\Service\Xml2\PromulgatorService;
use App\Service\Xml2\RelocationService;
use App\Service\Xml2\SectionService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Bundle\SecurityBundle\Security;

class RelocationServiceTest extends TestCase
{
    public function testDuplicateSectionOrdinalReturnsError(): void
    {
        $parent = new Chapter();
        $parent->setNodeId('chapter-1');

        $existingSection = new Section();
        $existingSection->setNodeId('section-1');
        $existingSection->setNumber('101 (Reserved)');
        $parent->addChild($existingSection);

        $repo = $this->createMock(CodeBookNodeRepository::class);
        $repo->expects($this->once())
             ->method('findNodeById')
             ->with('chapter-1')
             ->willReturn($parent);

        $service = new RelocationService(
            $this->createMock(EntityManagerInterface::class),
            $repo,
            $this->createMock(Security::class),
            $this->createMock(CodeBookPathIdService::class),
            $this->createMock(AppendixService::class),
            $this->createMock(PromulgatorService::class),
            $this->createMock(SectionService::class)
        );

        $request = new GetRelocationOptionsRequest();
        $request->type = ObjectType::TYPE_SECTION;
        $request->number = '101';
        $request->parentId = 'chapter-1';
        $request->relocatingId = 'relocating-123';

        $project = new Project();

        $response = $service->getRelocationOptions($project, $request);

        $this->assertFalse($response->isValidRelocation);
        $this->assertSame('Section "101" already exists.', $response->message);
    }
}
