<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\Xml2;

use App\Dto\Xml2\Project\CreateProjectRequest;
use App\Entity\Project;
use App\Exception\ApiException;
use App\Message\Project\CreateProjectMessage;
use App\Service\Xml2\ProjectService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\SerializerInterface;

class ProjectServiceTest extends TestCase
{
    private SerializerInterface $serializer;
    private EntityManagerInterface $em;
    private MessageBusInterface $bus;

    protected function setUp(): void
    {
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->bus = $this->createMock(MessageBusInterface::class);
    }

    public function testCreateThrowsExceptionOnDuplicateShortCode(): void
    {
        $this->expectException(ApiException::class);
        $this->expectExceptionMessage('Project with Short Code "DUPLICATE" already exists.');

        $request = new CreateProjectRequest();
        $request->shortCode = 'DUPLICATE';

        $service = $this->getMockBuilder(ProjectService::class)
            ->setConstructorArgs([$this->serializer, $this->em, $this->bus])
            ->onlyMethods(['checkDuplicateShortCode'])
            ->getMock();
        $service->expects($this->once())
            ->method('checkDuplicateShortCode')
            ->with($request->shortCode)
            ->willReturn(true);

        $service->create($request);
    }

    public function testCreateDispatchesMessageAndReturnsProject(): void
    {
        $request = new CreateProjectRequest();
        $request->shortCode = 'UNIQUE';

        $project = new Project();
        $envelope = new Envelope(new CreateProjectMessage($request));

        $this->bus->expects($this->once())
            ->method('dispatch')
            ->with($this->callback(function ($message) use ($project) {
                if ($message instanceof CreateProjectMessage) {
                    $message->setProject($project);
                    return true;
                }
                return false;
            }))
            ->willReturn($envelope);

        $service = $this->getMockBuilder(ProjectService::class)
            ->setConstructorArgs([$this->serializer, $this->em, $this->bus])
            ->onlyMethods(['checkDuplicateShortCode'])
            ->getMock();
        $service->expects($this->once())
            ->method('checkDuplicateShortCode')
            ->willReturn(false);

        $result = $service->create($request);
        $this->assertSame($project, $result);
    }
}
