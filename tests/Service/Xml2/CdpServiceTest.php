<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Service\Xml2;

use App\Dto\Xml2\Cdp\CdpUpdateProposalRequest;
use App\Dto\Xml2\Cdp\CdpUpdateProposalResponse;
use App\Entity\Cdp\ProposalAction\CdpProposalAction;
use App\Entity\Project\CodeChange\ChapterCodeChange;
use App\Entity\Project\CodeChange\ProjectCodeChange;
use App\Service\Xml2\CdpService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CdpServiceTest extends KernelTestCase
{
    private EntityManagerInterface $em;
    private CdpService $cdpService;

    protected function setUp(): void
    {
        $this->em = self::getContainer()->get(EntityManagerInterface::class);
        $this->cdpService = self::getContainer()->get(CdpService::class);
    }

    public function testUpdateSection(): void
    {
        $cdpAction = $this->em->getRepository(CdpProposalAction::class)->findOneBy([
            'cdpId' => '170748',
        ]);
        $action = $this->em->getRepository(ProjectCodeChange::class)->findOneBy([
            'cdpCodeChange' => $cdpAction,
        ]);
        $this->assertInstanceOf(ChapterCodeChange::class, $action);
        $action->setNeedsEvaluation(false);

        $request = new CdpUpdateProposalRequest();
        $request->id = (string) $action->getId();
        $request->endpoint = (string) $cdpAction->getEndpoint()->getId();

        $response = $this->cdpService->update($action->getProject(), $request);
        $this->assertInstanceOf(CdpUpdateProposalResponse::class, $response);
        $this->assertFalse($action->isNeedsEvaluation());

        $request->term = 'do nothing';
        $this->cdpService->update($action->getProject(), $request);
        $this->assertFalse($action->isNeedsEvaluation());

        $request->number = 'number';
        $this->cdpService->update($action->getProject(), $request);
        $this->assertSame('number', $action->getNumber());
        $this->assertTrue($action->isNeedsEvaluation());

        $request->codesIncorporated = true;
        $this->cdpService->update($action->getProject(), $request);
        $this->assertTrue($action->isCodesIncorporated());

        $request->codesIncorporated = false;
        $this->cdpService->update($action->getProject(), $request);
        $this->assertFalse($action->isCodesIncorporated());

        $request->pubsChecked = true;
        $this->cdpService->update($action->getProject(), $request);
        $this->assertTrue($action->isPubsChecked());

        $request->pubsChecked = false;
        $this->cdpService->update($action->getProject(), $request);
        $this->assertFalse($action->isPubsChecked());
    }
}
