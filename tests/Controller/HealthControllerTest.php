<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller;

use Symfony\Component\HttpFoundation\Response;

/**
 * @covers \App\Controller\HealthController
 */
class HealthControllerTest extends ApiTestCase
{
    public function testRoot(): void
    {
        $client = static::createClient();
        $client->jsonRequest('GET', '');

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testHealth(): void
    {
        $client = static::createApiClient($this->VIEWER);
        $client->jsonRequest('GET', '/api/health');

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }
}
