<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\QR;

use App\Tests\Controller\ApiTestCase;

use function json_decode;

class QrControllerTest extends ApiTestCase
{
    public function testNoAuth(): void
    {
        $this->markTestSkipped('needs mocking');

        $client = static::createClient();
        $client->jsonRequest('POST', '/api/janus/qr', [
            'filename'      => 'iccsafe',
            'redirectToUrl' => 'https://www.iccsafe.org',
            'bookIcon'      => 'ifc',
            'businessUnit'  => 'icc',
            'department'    => 'pubs',
            'status'        => true,
        ]);

        $this->assertResponseStatusCodeSame(401);
    }

    public function testCreate(): void
    {
        $this->markTestSkipped('needs mocking');

        $client = static::createApiClient($this->CODES);
        $client->jsonRequest('POST', '/api/janus/qr', [
            'filename'      => 'iccsafe',
            'redirectToUrl' => 'https://www.iccsafe.org',
            'bookIcon'      => 'ifc',
            'businessUnit'  => 'icc',
            'department'    => 'pubs',
            'status'        => true,
        ]);

        $this->assertResponseIsSuccessful();

        $actual = json_decode($client->getResponse()->getContent(), true);
        $expected = [
            'success' => true,
            'data'    => [
                'qrCodeImageUrl'        => $actual['data']['qrCodeImageUrl'],
                'qrCodeRedirectFromUrl' => $actual['data']['qrCodeRedirectFromUrl'],
                'redirectToUrl'         => 'https://www.iccsafe.org',
            ],
        ];

        $this->assertEquals($expected, $actual);
    }

    public function testUpdate(): void
    {
        $this->markTestSkipped('needs mocking');

        $client = static::createApiClient($this->CODES);
        $client->jsonRequest('POST', '/api/janus/qr/filename', [
            'redirectToUrl' => 'https://www.iccsafe.org',
        ]);

        $this->assertResponseIsSuccessful();
    }
}
