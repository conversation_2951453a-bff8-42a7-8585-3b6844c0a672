<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller;

use App\Entity\User\User;
use Lexik\Bundle\JWTAuthenticationBundle\Encoder\JWTEncoderInterface;
use RuntimeException;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\BrowserKit\AbstractBrowser;

use function method_exists;
use function sprintf;

abstract class ApiTestCase extends WebTestCase
{
    protected array $SUPER_ADMIN = [
        'username' => '<EMAIL>',
        'roles'    => [User::ROLE_SUPER_ADMIN],
    ];
    protected array $ADMIN = [
        'username' => '<EMAIL>',
        'roles'    => [user::ROLE_ADMIN],
    ];
    protected array $CODES = [
        'username' => '<EMAIL>',
        'roles'    => [User::ROLE_CODES],
    ];
    protected array $PUBS = [
        'username' => '<EMAIL>',
        'roles'    => [User::ROLE_PUBS],
    ];
    protected array $SECRETARIAT = [
        'username' => '<EMAIL>',
        'roles'    => [User::ROLE_SECRETARIAT],
    ];
    protected array $VIEWER = [
        'username' => '<EMAIL>',
        'roles'    => [User::ROLE_VIEW],
    ];

    public static function createApiClient(array $claims): AbstractBrowser
    {
        if (!method_exists(__CLASS__, 'createClient')) {
            throw new RuntimeException('Unit test must extend ' . WebTestCase::class);
        }

        $client = self::getContainer()->get('test.client');
        $encoder = $client->getContainer()->get(JWTEncoderInterface::class);
        $client->setServerParameter('HTTP_AUTHORIZATION', sprintf('Bearer %s', $encoder->encode($claims)));

        return self::getClient($client);
    }
}
