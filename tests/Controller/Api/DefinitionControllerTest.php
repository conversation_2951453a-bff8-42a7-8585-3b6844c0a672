<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\Definition\CreateDefinitionRequest;
use App\Dto\Xml2\Definition\ListDefinitionsResponse;
use App\Dto\Xml2\Definition\MoveDefinitionRequest;
use App\Dto\Xml2\Definition\UpdateDefinitionRequest;
use App\Entity\CodeBook\Definition;
use App\Entity\CodeBook\Section;
use App\Entity\Project;
use App\Enum\Status;
use App\Service\Xml2\DefinitionService;
use App\Service\Xml2\SearchService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function array_walk;
use function http_build_query;
use function json_decode;
use function json_encode;
use function sprintf;

/**
 * @covers \App\Controller\Api\DefinitionController
 */
class DefinitionControllerTest extends ApiTestCase
{
    private DefinitionService $definitionService;
    private SearchService $searchService;
    private EntityManagerInterface $em;
    private Project $project;
    private Definition $definition;
    private Section $section;
    private array $definitionData = [
        'indexNumber'          => 'indexNumber',
        'committeeDesignation' => 'committeeDesignation',
        'term'                 => 'term',
        'definition'           => 'definition',
    ];

    protected function setUp(): void
    {
        $container = static::getContainer();

        $this->definitionService = $this->createMock(DefinitionService::class);
        $container->set(DefinitionService::class, $this->definitionService);

        $this->searchService = $this->createMock(SearchService::class);
        $container->set(SearchService::class, $this->searchService);

        $this->em = $container->get(EntityManagerInterface::class);

        $this->project = new Project();
        $this->project->setShortCode('definition-test');
        $this->em->persist($this->project);

        $this->definition = new Definition();
        $this->definition->setNodeId('existingDefinition');
        foreach ($this->definitionData as $key => $value) {
            $setter = sprintf('set%s', ucfirst($key));
            $this->definition->$setter($value);
        }
        $this->em->persist($this->definition);

        $this->section = new Section();
        $this->section->addChild($this->definition);
        $this->em->persist($this->section);

        $this->em->flush();
    }

    public function testList(): void
    {
        $this->definitionService
            ->expects($this->once())
            ->method('list')
            ->willReturn(new ListDefinitionsResponse([$this->definition]));

        $uri = sprintf('/api/v2/books/%s/definitions', $this->project->getShortCode());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertCount(1, $response);
    }

    public function testCreate(): void
    {
        $data = $this->definitionData;
        $data['parentNode'] = $this->section->getNodeId();
        $data['neighbor'] = 'neighbor';
        $data['term'] = 'term';
        $data['insertBefore'] = '';

        $this->definitionService
            ->expects($this->once())
            ->method('create')
            ->with($this->project, $this->callback(function ($request) use ($data): bool {
                $this->assertInstanceOf(CreateDefinitionRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                return true;
            }))
            ->willReturn($this->createMock(Definition::class));

        $uri = sprintf('/api/v2/books/%s/definitions', $this->project->getShortCode());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testRead(): void
    {
        $uri = sprintf('/api/v2/books/%s/definitions/%s', $this->project->getShortCode(), $this->definition->getNodeId());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        foreach ($this->definitionData as $key => $value) {
            $this->assertSame($value, $response[$key]);
        }
    }

    public function testUpdate(): void
    {
        $data = [
            'status' => Status::IN_PROGRESS,
            'term'   => 'New Term',
        ];

        $this->definitionService
            ->expects($this->once())
            ->method('update')
            ->with($this->definition, $this->callback(function ($request) use ($data): bool {
                $this->assertInstanceOf(UpdateDefinitionRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                return true;
            }))
            ->willReturnArgument(0);

        $uri = sprintf('/api/v2/books/%s/definitions/%s', $this->project->getShortCode(), $this->definition->getNodeId());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_ACCEPTED);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testDelete(): void
    {
        $this->definitionService
            ->expects($this->once())
            ->method('delete')
            ->with($this->definition)
            ->willReturnCallback(function (Definition $definition) {
                $definition->setRevisionBy('test user');
                $definition->deleteNode('test user');
                return $definition;
            });

        $uri = sprintf('/api/v2/books/%s/definitions/%s', $this->project->getShortCode(), $this->definition->getNodeId());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('DELETE', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_ACCEPTED);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertEquals('test user', $response['revisionBy']);
        $this->assertEquals('test user', $response['deletedBy']);
        $this->assertFalse($response['showDeletionMarker']);
    }

    public function testMove(): void
    {
        $data = [
            'parentNode'   => 'parentNode',
            'neighbor'     => 'neighbor',
            'term'         => 'term',
            'insertBefore' => 'insertBefore',
        ];

        $this->definitionService
            ->expects($this->once())
            ->method('move')
            ->with($this->project, $this->definition, $this->callback(function ($request) use ($data): bool {
                $this->assertInstanceOf(MoveDefinitionRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                return true;
            }))
            ->willReturnArgument(1);

        $uri = sprintf('/api/v2/books/%s/definitions/%s/move', $this->project->getShortCode(), $this->definition->getNodeId());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_ACCEPTED);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testToggleFormalUsage(): void
    {
        $uri = sprintf('/api/v2/books/%s/definitions-formal-usage', $this->project->getShortCode());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode([]));

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testGetDefinitionUsage(): void
    {
        $this->searchService
            ->expects($this->once())
            ->method('getDefinitionUsages')
            ->with(
                $this->project,
                $this->equalTo('query'),
                $this->equalTo([]),
                $this->equalTo(1),
                $this->equalTo(10),
            )
            ->willReturn([]);

        $uri = sprintf('/api/v2/books/%s/definitions-usage?%s', $this->project->getShortCode(), http_build_query([
            'query'          => 'query',
            'similarMatches' => [],
            'page'           => 1,
            'limit'          => 10,
        ]));
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }
}
