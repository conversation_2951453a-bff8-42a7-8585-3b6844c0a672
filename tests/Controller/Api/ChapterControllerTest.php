<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\Chapter\CreateChapterRequest;
use App\Dto\Xml2\Chapter\GetChapterSectionsResponse;
use App\Dto\Xml2\Chapter\UpdateChapterRequest;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Volume;
use App\Entity\Project;
use App\Enum\Status;
use App\Exception\ApiException;
use App\Service\Xml2\ChapterService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\BrowserKit\AbstractBrowser;
use Symfony\Component\HttpFoundation\Response;

use function array_walk;
use function json_decode;
use function json_encode;
use function sprintf;

/**
 * @covers \App\Controller\Api\ChapterController
 */
class ChapterControllerTest extends ApiTestCase
{
    private AbstractBrowser $client;
    private ChapterService $chapterService;
    private EntityManagerInterface $em;
    private Project $project;
    private Chapter $chapter;
    private Volume $volume;
    private array $chapterData = [
        'indexNumber'          => 'Ch 1',
        'reserveCount'         => 5,
        'tocEntry'             => false,
        'tocAutoAdd'           => false,
        'displayLevel'         => 1,
        'superTitle'           => 'superTitle',
        'committeeDesignation' => 'committeeDesignation',
        'label'                => 'Appendix',
        'number'               => 'A',
        'title'                => 'Test',
        'titleAbbreviation'    => 'titleAbbreviation',
        'titleYear'            => 'titleYear',
        'subTitle'             => 'subTitle',
        'history'              => 'history',
        'objectivesTitle'      => 'objectivesTitle',
        'objectives'           => 'objectives',
        'abstractTitle'        => 'abstractTitle',
        'abstract'             => 'abstract',
        'keywordsTitle'        => 'keywordsTitle',
        'keywords'             => 'keywords',
        'body'                 => 'body',
    ];

    protected function setUp(): void
    {
        $this->client = static::createApiClient($this->SUPER_ADMIN);
        $container = self::getContainer();

        $this->chapterService = $this->createMock(ChapterService::class);
        $container->set(ChapterService::class, $this->chapterService);

        $this->em = $container->get(EntityManagerInterface::class);
        $this->project = new Project();
        $this->project->setShortCode('chapter-test');
        $this->em->persist($this->project);

        $this->chapter = new Chapter();
        $this->chapter->setNodeId('existingChapter');
        foreach ($this->chapterData as $key => $value) {
            $setter = sprintf('set%s', ucfirst($key));
            $this->chapter->$setter($value);
        }
        $this->em->persist($this->chapter);

        $this->volume = new Volume();
        $this->volume->addChild($this->chapter);
        $this->em->persist($this->volume);

        $this->em->flush();
    }

    public function testIndex(): void
    {
        $this->chapterService
            ->expects($this->once())
            ->method('list')
            ->with($this->project)
            ->willReturn([$this->chapter]);

        $uri = '/api/v2/books/chapter-test/chapters';
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertCount(1, $response);
    }

    public function testCreate(): void
    {
        $data = $this->chapterData;
        $data['parentNode'] = $this->volume->getNodeId();
        $data['insertBefore'] = $this->chapter->getNodeId();
        $data['action'] = 'action';
        $data['neighbor'] = 'neighbor';
        $data['number'] = 'number';
        $data['title'] = 'title';

        $this->chapterService
            ->expects($this->once())
            ->method('create')
            ->with($this->project, $this->callback(function ($request) use ($data) {
                $this->assertInstanceOf(CreateChapterRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                return true;
            }))
            ->willReturn($this->createMock(Chapter::class));

        $uri = '/api/v2/books/chapter-test/chapters';
        $this->client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testRead(): void
    {
        $uri = sprintf('/api/v2/books/chapter-test/chapters/%s', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($this->client->getResponse()->getContent(), true);
        foreach ($this->chapterData as $key => $value) {
            $this->assertSame($value, $response[$key]);
        }
    }

    public function testUpdate(): void
    {
        $data = $this->chapterData;
        $data['status'] = Status::APPROVED;

        $this->chapterService
            ->expects($this->once())
            ->method('update')
            ->with($this->project, $this->chapter, $this->callback(function ($request) use ($data) {
                $this->assertInstanceOf(UpdateChapterRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                return true;
            }))
            ->willReturnArgument(1);

        $uri = sprintf('/api/v2/books/chapter-test/chapters/%s', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testDelete(): void
    {
        $this->chapterService
            ->expects($this->once())
            ->method('delete')
            ->with($this->chapter)
            ->willReturnCallback(function (Chapter $chapter) {
                $chapter->setRevisionBy('test user');
                $chapter->deleteNode('test user');
                return null;
            });

        $uri = sprintf('/api/v2/books/chapter-test/chapters/%s', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('DELETE', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertEquals('test user', $response['revisionBy']);
        $this->assertEquals('test user', $response['deletedBy']);
        $this->assertFalse($response['showDeletionMarker']);
    }

    public function testSections(): void
    {
        $assertBool = false;
        $this->chapterService
            ->expects($this->once())
            ->method('getSections')
            ->with($this->chapter, $this->callback(function ($isReferenceLinks) use (&$assertBool) {
                $this->assertIsBool($isReferenceLinks);
                $this->assertSame($assertBool, $isReferenceLinks);
                return true;
            }))
            ->willReturn($this->createMock(GetChapterSectionsResponse::class));

        $uri = sprintf('/api/v2/books/chapter-test/chapters/%s/sections', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        // with ref links
        $assertBool = true;
        $uri = sprintf('/api/v2/books/chapter-test/chapters/%s/sections?referencelinks=1', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testReferenceSections(): void
    {
        $this->chapterService
            ->expects($this->once())
            ->method('getReferenceSections')
            ->with($this->chapter->getUlid())
            ->willReturn($this->createMock(GetChapterSectionsResponse::class));

        $uri = sprintf('/api/v2/books/chapter-test/chapter/%s/reference-sections', $this->chapter->getUlid());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testGetAllLinksInChapter(): void
    {
        $this->chapterService
            ->expects($this->once())
            ->method('getAllLinksValidationResult')
            ->with($this->project, $this->chapter)
            ->willReturn([]);

        $uri = sprintf('/api/v2/books/chapter-test/chapter/%s/links-report', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testGetAllLinksInChapterException(): void
    {
        $this->chapterService
            ->expects($this->once())
            ->method('getAllLinksValidationResult')
            ->with($this->project, $this->chapter)
            ->willThrowException(new ApiException());

        $uri = sprintf('/api/v2/books/chapter-test/chapter/%s/links-report', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testXmlValidationCsvReturnsNoContentWhenNoIssues(): void
    {
        $uri = sprintf('/api/v2/reports/chapter-test/chapters/%s/xml-validation.csv', $this->chapter->getNodeId());
        $this->client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        $this->assertSame('', $this->client->getResponse()->getContent());
    }
}
