<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api\Proposal;

use App\DataFixtures\Cdp\CdpEndpointTestFixture;
use App\Entity\Cdp\CdpEndpoint;
use App\Enum\CdpEndpointStatus;
use App\Tests\Controller\ApiTestCase;
use Symfony\Component\HttpFoundation\Response;

use function json_decode;
use function sprintf;

class EndpointControllerTest extends ApiTestCase
{
    public function testList()
    {
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', '/api/v2/proposals/endpoint');

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertCount(6, $response);
    }

    public function testStatus()
    {
        $client = static::createApiClient($this->SUPER_ADMIN);
        $em = $client->getContainer()->get('doctrine')->getManager();

        $endpoint = $em->getRepository(CdpEndpoint::class)->findOneBy([
            'name' => CdpEndpointTestFixture::$endpoints['cdpTEST']['name'],
        ]);
        $this->assertInstanceOf(CdpEndpoint::class, $endpoint);

        $uri = sprintf('/api/v2/proposals/endpoint/%s/status', $endpoint->getId());
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), false);
        $this->assertIsObject($response);
        $this->assertObjectHasProperty('status', $response);
        $this->assertSame(CdpEndpointStatus::IDLE, $response->status);
    }
}
