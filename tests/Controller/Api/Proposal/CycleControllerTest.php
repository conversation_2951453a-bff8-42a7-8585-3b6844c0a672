<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api\Proposal;

use App\Tests\Controller\ApiTestCase;
use Symfony\Component\HttpFoundation\Response;

use function json_decode;

class CycleControllerTest extends ApiTestCase
{
    public function testList()
    {
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', '/api/v2/proposals/cycles-books');

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), false);
        $this->assertIsObject($response);
        $this->assertObjectHasProperty('cycles', $response);
        $this->assertIsArray($response->cycles);;
        $this->assertCount(1, $response->cycles);
    }
}
