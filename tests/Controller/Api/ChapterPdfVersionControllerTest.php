<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\Chapter\GetChapterVersionsListResponse;
use App\Entity\ChapterPdfVersion;
use App\Entity\Project;
use App\Enum\Status;
use App\Service\ChapterPdfVersionService;
use App\Tests\Controller\ApiTestCase;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\BrowserKit\AbstractBrowser;

use function sprintf;

class ChapterPdfVersionControllerTest extends ApiTestCase
{
    private AbstractBrowser $client;
    private ChapterPdfVersionService $pdfVersionService;
    private Project $project;
    private ChapterPdfVersion $chapterPdfVersion;

    protected function setUp(): void
    {
        $container = self::getContainer();

        $this->client = static::createApiClient($this->SUPER_ADMIN);
        $this->pdfVersionService = $this->createMock(ChapterPdfVersionService::class);
        $container->set(ChapterPdfVersionService::class, $this->pdfVersionService);

        $this->project = new Project();
        $this->project->setShortCode('pdf-version-test');

        $this->chapterPdfVersion = new ChapterPdfVersion();
        $this->chapterPdfVersion->setProject($this->project);
        $this->chapterPdfVersion->setFilename('version.pdf');
        $this->chapterPdfVersion->setStatus(Status::IN_PROGRESS);
        $this->chapterPdfVersion->setCreatedByEmail($this->SUPER_ADMIN['username']);
        $this->chapterPdfVersion->setCreatedAt(new DateTime);

        $em = $container->get(EntityManagerInterface::class);
        $em->persist($this->project);
        $em->persist($this->chapterPdfVersion);
        $em->flush();
    }

    public function testGetChapterVersions(): void
    {
        $this->pdfVersionService
            ->expects($this->once())
            ->method('getChapterVersions')
            ->with(
                $this->project,
                $this->callback(function ($baseUrl): bool {
                    $this->assertStringContainsString('/api/v2/books/', $baseUrl);
                    return true;
                }),
            )
            ->willReturn($this->createMock(GetChapterVersionsListResponse::class));

        $uri = sprintf('/api/v2/books/%s/chapter-versions', $this->project->getShortCode());
        $this->client->request('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(200);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testDownloadPdf(): void
    {
        $this->pdfVersionService
            ->expects($this->once())
            ->method('downloadPdf')
            ->with($this->chapterPdfVersion)
            ->willReturn('pdf');

        $uri = sprintf('/api/v2/books/%s/pdf-version/%d/file.pdf',
            $this->project->getShortCode(),
            $this->chapterPdfVersion->getId());
        $this->client->request('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(200);
        $this->assertResponseHeaderSame('content-type', 'application/pdf');
        $this->assertResponseHeaderSame('content-length', '3');
    }

    public function testDownloadPdfMismatchedVersion(): void
    {
        $this->pdfVersionService
            ->expects($this->never())
            ->method('downloadPdf');

        $this->chapterPdfVersion->setProject($this->createMock(Project::class));

        $uri = sprintf('/api/v2/books/%s/pdf-version/%d/file.pdf',
            $this->project->getShortCode(),
            $this->chapterPdfVersion->getId());
        $this->client->request('GET', $uri);

        $this->assertResponseStatusCodeSame(404);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testDownloadPdfException(): void
    {
        $this->pdfVersionService
            ->expects($this->once())
            ->method('downloadPdf')
            ->willThrowException(new \RuntimeException());

        $uri = sprintf('/api/v2/books/%s/pdf-version/%d/file.pdf',
            $this->project->getShortCode(),
            $this->chapterPdfVersion->getId());
        $this->client->request('GET', $uri);

        $this->assertResponseStatusCodeSame(404);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }
}
