<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api\Definition;

use App\Entity\CodeBook\Definition;
use App\Entity\Project;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\BrowserKit\AbstractBrowser;
use Symfony\Component\HttpFoundation\Response;

use function json_decode;

class ReadDefinitionControllerTest extends ApiTestCase
{
    private string $projectId = 'project.IWUIC';
    private string $uri = '/api/v2/books/project.IWUIC/definitions';
    private AbstractBrowser $client;
    private EntityManagerInterface $em;
    private EntityRepository $repo;
    private Definition $definition;

    protected function apiClient(array $claims = []): AbstractBrowser
    {
        if (empty($claims)) {
            $claims = $this->SUPER_ADMIN;
        }

        $this->client = static::createApiClient($claims);
        $this->em = $this->client->getContainer()->get(EntityManagerInterface::class);
        $this->repo = $this->em->getRepository(Definition::class);

        $project = $this->em->getRepository(Project::class)->findOneBy(['shortCode' => $this->projectId]);
        $this->definition = $project->getCodeBook()->getNodesByClass(Definition::class)[4];
        $this->uri = sprintf('%s/%s', $this->uri, $this->definition->getNodeId());

        return $this->client;
    }

    /** @dataProvider securityAttributeCases */
    public function testSecurity(array $user, bool $expectedResult): void
    {
        $client = $this->apiClient($user);
        $client->xmlHttpRequest('GET', $this->uri);
        $this->assertSame($expectedResult, $client->getResponse()->isSuccessful());
    }

    public function securityAttributeCases(): iterable
    {
        return [
            'Super User'  => [$this->SUPER_ADMIN, true],
            'Admin'       => [$this->ADMIN, true],
            'Pubs'        => [$this->PUBS, false],
            'Codes'       => [$this->CODES, false],
            'Secretariat' => [$this->SECRETARIAT, false],
            'Viewer'      => [$this->VIEWER, false],
        ];
    }

    public function testRead(): void
    {
        $client = $this->apiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $this->uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);

        $expected = [
            'dataType'             => 'definition',
            'committeeDesignation' => $this->definition->getCommitteeDesignation(),
            'term'                 => $this->definition->getTerm(),
            'definition'           => $this->definition->getDefinition(),
            'children'             => [],
        ];

        foreach ($expected as $key => $value) {
            $this->assertArrayHasKey($key, $response);
            $this->assertSame($value, $response[$key]);
        }
    }
}
