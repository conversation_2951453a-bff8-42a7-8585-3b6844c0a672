<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api\Definition;

use App\Tests\Controller\ApiTestCase;
use Symfony\Component\HttpFoundation\Response;

use function json_decode;

class IndexDefinitionControllerTest extends ApiTestCase
{
    private string $projectId = 'project.IWUIC';
    private string $uri = '/api/v2/books/project.IWUIC/definitions';

    /** @dataProvider methodCases */
    public function testMethods(string $method, bool $expectedStatus): void
    {
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest($method, $this->uri);
        $this->assertSame($expectedStatus, $client->getResponse()->isSuccessful());
    }

    public function methodCases(): iterable
    {
        return [
            'GET'    => ['GET', true],
            'POST'   => ['POST', false],
            'PUT'    => ['PUT', false],
            'DELETE' => ['DELETE', false],
        ];
    }

    /** @dataProvider securityAttributeCases */
    public function testSecurity(array $user, bool $expectedResult): void
    {
        $client = static::createApiClient($user);
        $client->xmlHttpRequest('GET', $this->uri);
        $this->assertSame($expectedResult, $client->getResponse()->isSuccessful());
    }

    public function securityAttributeCases(): iterable
    {
        return [
            'Super User'  => [$this->SUPER_ADMIN, true],
            'Admin'       => [$this->ADMIN, true],
            'Pubs'        => [$this->PUBS, false],
            'Codes'       => [$this->CODES, false],
            'Secretariat' => [$this->SECRETARIAT, false],
            'Viewer'      => [$this->VIEWER, false],
        ];
    }

    public function testIndex(): void
    {
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $this->uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertArrayHasKey('definitions', $response);
        $this->assertCount(53, $response['definitions']);
    }
}
