<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\Promulgator\AddStandardToPromulgatorRequest;
use App\Dto\Xml2\Promulgator\AddStandardToPromulgatorResponse;
use App\Dto\Xml2\Promulgator\CreatePromulgatorRequest;
use App\Dto\Xml2\Promulgator\DeletePromulgatorResponse;
use App\Dto\Xml2\Promulgator\MovePromulgatorRequest;
use App\Dto\Xml2\Promulgator\MovePromulgatorResponse;
use App\Dto\Xml2\Promulgator\UpdatePromulgatorRequest;
use App\Entity\CodeBook\Chapter;
use App\Entity\CodeBook\Promulgator;
use App\Entity\Project;
use App\Enum\Status;
use App\Service\Xml2\PromulgatorService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function array_merge;
use function array_walk;
use function json_decode;
use function json_encode;
use function sprintf;
use function ucfirst;

/**
 * @covers \App\Controller\Api\PromulgatorController
 */
class PromulgatorControllerTest extends ApiTestCase
{
    private PromulgatorService $promulgatorService;
    private Project $project;
    private Chapter $chapter;
    private Promulgator $existingPromulgator;
    private array $promulgatorData = [
        'acronym'          => 'acronym',
        'addressLine'      => 'addressLine',
        'organizationName' => 'organizationName',
        'street'           => 'street',
        'city'             => 'city',
        'state'            => 'state',
        'postalCode'       => 'postalCode',
        'country'          => 'country',
        'email'            => 'email',
        'url'              => 'url',
        'phone'            => 'phone',
        'fax'              => 'fax',
    ];

    protected function setUp(): void
    {
        $container = self::getContainer();
        $em = $container->get(EntityManagerInterface::class);

        $this->promulgatorService = $this->createMock(PromulgatorService::class);
        $container->set(PromulgatorService::class, $this->promulgatorService);

        $this->existingPromulgator = new Promulgator();
        $this->existingPromulgator->setNodeId('promulgator');
        foreach ($this->promulgatorData as $key => $value) {
            $setter = sprintf('set%s', ucfirst($key));
            $this->existingPromulgator->$setter($value);
        }
        $em->persist($this->existingPromulgator);

        $this->chapter = new Chapter();
        $this->chapter->setNodeId('chapter');
        $this->chapter->addChild($this->existingPromulgator);
        $em->persist($this->chapter);

        $this->project = new Project();
        $this->project->setShortCode('promulgator-test');
        $em->persist($this->project);

        $em->flush();
    }

    public function testList(): void
    {
        $this->promulgatorService
            ->expects($this->once())
            ->method('list')
            ->willReturn([$this->existingPromulgator]);

        $uri = sprintf('/api/v2/books/%s/promulgators', $this->project->getShortCode());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertCount(1, $response);
    }

    public function testCreate(): void
    {
        $data = array_merge($this->promulgatorData, [
            'parentNode'    => $this->chapter->getNodeId(),
            'international' => true,
        ]);

        $this->promulgatorService
            ->expects($this->once())
            ->method('create')
            ->with(
                $this->project,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(CreatePromulgatorRequest::class, $request);
                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturn($this->createMock(Promulgator::class));

        $uri = sprintf('/api/v2/books/%s/promulgators', $this->project->getShortCode());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseFormatSame('json');
    }

    public function testRead(): void
    {
        $uri = sprintf('/api/v2/books/%s/promulgators/%s', $this->project->getShortCode(), $this->existingPromulgator->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');

        $response = json_decode($client->getResponse()->getContent(), true);
        foreach ($this->promulgatorData as $key => $value) {
            $this->assertSame($value, $response[$key]);
        }
    }

    public function testUpdate(): void
    {
        $data = [
            'status'  => Status::CODES_FINAL,
            'acronym' => 'new acronym',
        ];

        $this->promulgatorService
            ->expects($this->once())
            ->method('update')
            ->with(
                $this->existingPromulgator,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(UpdatePromulgatorRequest::class, $request);
                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturnArgument(0);

        $uri = sprintf('/api/v2/books/%s/promulgators/%s', $this->project->getShortCode(), $this->existingPromulgator->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }

    public function testDelete(): void
    {
        $this->promulgatorService
            ->expects($this->once())
            ->method('delete')
            ->with($this->existingPromulgator)
            ->willReturn($this->createMock(DeletePromulgatorResponse::class));

        $uri = sprintf('/api/v2/books/%s/promulgators/%s', $this->project->getShortCode(), $this->existingPromulgator->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('DELETE', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }

    public function testMove(): void
    {
        $data = [
            'parentNode'   => $this->chapter->getNodeId(),
            'validateOnly' => true,
        ];

        $this->promulgatorService
            ->expects($this->once())
            ->method('move')
            ->with(
                $this->project,
                $this->existingPromulgator,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(MovePromulgatorRequest::class, $request);
                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturn($this->createMock(MovePromulgatorResponse::class));

        $uri = sprintf('/api/v2/books/%s/promulgators/%s/move', $this->project->getShortCode(), $this->existingPromulgator->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }

    public function testAddStandard(): void
    {
        $data = [
            'standardId' => 'standardId',
        ];

        $this->promulgatorService
            ->expects($this->once())
            ->method('addStandard')
            ->with(
                $this->existingPromulgator,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(AddStandardToPromulgatorRequest::class, $request);
                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturn($this->createMock(AddStandardToPromulgatorResponse::class));

        $uri = sprintf('/api/v2/books/%s/promulgators/%s/move-standard', $this->project->getShortCode(), $this->existingPromulgator->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseFormatSame('json');
    }
}
