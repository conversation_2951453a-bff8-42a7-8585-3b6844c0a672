<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\Appendix\CreateAppendixRequest;
use App\Dto\Xml2\Appendix\UpdateAppendixRequest;
use App\Entity\CodeBook\Appendix;
use App\Entity\CodeBook\BackMatter;
use App\Entity\Project;
use App\Enum\Status;
use App\Service\Xml2\AppendixService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function array_walk;
use function json_decode;
use function json_encode;
use function sprintf;
use function ucfirst;

/**
 * @covers \App\Controller\Api\AppendixController
 */
class AppendixControllerTest extends ApiTestCase
{
    private AppendixService $appendixService;
    private Project $project;
    private Appendix $appendix;
    private BackMatter $backMatter;
    private array $appendixData = [
        'indexNumber'          => 'Appx A',
        'tocEntry'             => true,
        'tocAutoAdd'           => true,
        'superTitle'           => 'superTitle',
        'committeeDesignation' => 'committeeDesignation',
        'label'                => 'Appendix',
        'number'               => 'A',
        'title'                => 'Test',
        'titleAbbreviation'    => 'titleAbbreviation',
        'titleYear'            => 'titleYear',
        'subTitle'             => 'subTitle',
        'history'              => 'history',
        'objectivesTitle'      => 'objectivesTitle',
        'objectives'           => 'objectives',
        'noteTitle'            => 'noteTitle',
        'note'                 => 'note',
        'abstractTitle'        => 'abstractTitle',
        'abstract'             => 'abstract',
        'keywordsTitle'        => 'keywordsTitle',
        'keywords'             => 'keywords',
        'body'                 => 'body',
    ];

    protected function setUp(): void
    {
        $container = self::getContainer();
        $em = $container->get(EntityManagerInterface::class);

        $this->appendixService = $this->createMock(AppendixService::class);
        $container->set(AppendixService::class, $this->appendixService);

        $this->appendix = new Appendix();
        $this->appendix->setNodeId('existingAppendix');
        foreach ($this->appendixData as $key => $value) {
            $setter = sprintf('set%s', ucfirst($key));
            $this->appendix->$setter($value);
        }
        $em->persist($this->appendix);

        $this->backMatter = new BackMatter();
        $this->backMatter->setNodeId('backMatter');
        $this->backMatter->addChild($this->appendix);
        $em->persist($this->backMatter);

        $this->project = new Project();
        $this->project->setShortCode('appendix-test');
        $em->persist($this->project);

        $em->flush();
    }

    public function testIndex(): void
    {
        $this->appendixService
            ->expects($this->once())
            ->method('list')
            ->with($this->project)
            ->willReturn([$this->appendix]);

        $uri = sprintf('/api/v2/books/%s/appendices', $this->project->getShortCode());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertCount(1, $response);
    }

    public function testCreate(): void
    {
        $data = $this->appendixData;
        $data['parentNode'] = $this->backMatter->getNodeId();
        $data['insertBefore'] = $this->appendix->getNodeId();
        $data['action'] = 'action';
        $data['neighbor'] = 'neighbor';
        $data['number'] = 'number';
        $data['title'] = 'title';

        $this->appendixService
            ->expects($this->once())
            ->method('create')
            ->with($this->project, $this->callback(function ($request) use ($data): bool {
                $this->assertInstanceOf(CreateAppendixRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                return true;
            }))
            ->willReturn($this->createMock(Appendix::class));

        $uri = sprintf('/api/v2/books/%s/appendices', $this->project->getShortCode());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseFormatSame('json');
    }

    public function testRead(): void
    {
        $uri = sprintf('/api/v2/books/appendix-test/appendices/%s', $this->appendix->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');

        $response = json_decode($client->getResponse()->getContent(), true);
        foreach ($this->appendixData as $key => $value) {
            $this->assertSame($value, $response[$key]);
        }
    }

    public function testUpdate(): void
    {
        $data = [
            'status' => Status::IN_PROGRESS,
            'title'  => 'New Title',
        ];

        $this->appendixService
            ->expects($this->once())
            ->method('update')
            ->with($this->project, $this->appendix, $this->callback(function ($request) use ($data): bool {
                $this->assertInstanceOf(UpdateAppendixRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                return true;
            }))
            ->willReturnArgument(1);

        $uri = sprintf('/api/v2/books/appendix-test/appendices/%s', $this->appendix->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_ACCEPTED);
        $this->assertResponseFormatSame('json');
    }

    public function testDelete(): void
    {
        $this->appendixService
            ->expects($this->once())
            ->method('delete')
            ->with($this->appendix)
            ->willReturnCallback(function (Appendix $appendix) {
                $appendix->setRevisionBy('test user');
                $appendix->deleteNode('test user');
                return null;
            });

        $uri = sprintf('/api/v2/books/appendix-test/appendices/%s', $this->appendix->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('DELETE', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertEquals('test user', $response['revisionBy']);
        $this->assertEquals('test user', $response['deletedBy']);
        $this->assertFalse($response['showDeletionMarker']);
    }
}
