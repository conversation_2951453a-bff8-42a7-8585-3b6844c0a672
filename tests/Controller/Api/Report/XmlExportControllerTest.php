<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api\Report;

use App\Entity\Project;
use App\Service\Xml2\ReportService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function http_build_query;
use function sprintf;

/**
 * @covers \App\Controller\Api\Report\XmlExportController
 */
class XmlExportControllerTest extends ApiTestCase
{
    private Project $project;
    private ReportService $reportService;

    protected function setUp(): void
    {
        $container = static::getContainer();
        $em = $container->get(EntityManagerInterface::class);

        $this->reportService = $this->createMock(ReportService::class);
        $container->set(ReportService::class, $this->reportService);

        $this->project = new Project();
        $this->project->setShortCode('pdf-export-test');
        $em->persist($this->project);

        $em->flush();
    }

    public function testExport(): void
    {
        $data = [
            'clean' => false,
        ];

        $uri = sprintf('/api/v2/reports/%s/export-xml?%s', $this->project->getShortCode(), http_build_query($data));
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseFormatSame('json');
    }
}
