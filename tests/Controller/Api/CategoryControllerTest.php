<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Project\CreateProjectCategoryRequest;
use App\Dto\Project\ProjectCategoryDto;
use App\Dto\Project\UpdateProjectCategoryRequest;
use App\Entity\ProjectCategory;
use App\Service\ProjectCategoryService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function json_decode;
use function json_encode;
use function sprintf;
use function urlencode;

/**
 * @covers \App\Controller\Api\CategoryController
 */
class CategoryControllerTest extends ApiTestCase
{
    private ProjectCategoryService $categoryService;
    private ProjectCategory $category;
    private EntityManagerInterface $em;

    protected function setUp(): void
    {
        $container = self::getContainer();

        $this->categoryService = $this->createMock(ProjectCategoryService::class);
        $container->set(ProjectCategoryService::class, $this->categoryService);

        $this->category = new ProjectCategory();
        $this->category->setName('category');

        $this->em = $container->get(EntityManagerInterface::class);
        $this->em->persist($this->category);
        $this->em->flush();
    }

    public function testAll(): void
    {
        $this->categoryService
            ->expects($this->once())
            ->method('getAll')
            ->willReturn([
                ProjectCategoryDto::create($this->category),
            ]);

        $client = static::createApiClient($this->CODES);
        $client->request('GET', '/api/v2/categories');

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(200);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testUser(): void
    {
        $email = '<EMAIL>';
        $this->categoryService
            ->expects($this->once())
            ->method('getByUser')
            ->with($this->callback(function ($requestEmail) use (&$email) {
                $this->assertSame($requestEmail, $email);
                return true;
            }))
            ->willReturn([]);

        $client = static::createApiClient($this->CODES);
        $client->request('GET', sprintf('/api/v2/categories/user-categories/%s', urlencode($email)));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(200);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        // get email from $user
        $email = $this->CODES['username'];
        $client->request('GET', sprintf('/api/v2/categories/user-categories'));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(200);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testCreate(): void
    {
        $this->categoryService
            ->expects($this->once())
            ->method('create')
            ->with($this->callback(function ($request) {
                $this->assertInstanceOf(CreateProjectCategoryRequest::class, $request);
                $this->assertSame('category', $request->name);
                return true;
            }))
            ->willReturn($this->category);

        $uri = '/api/v2/categories';
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode([
            'name' => 'category',
        ]));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testView(): void
    {
        $uri = sprintf('/api/v2/categories/%d', $this->category->getId());
        $client = static::createApiClient($this->VIEWER);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertSame($this->category->getName(), $response['name']);
    }

    public function testUpdate(): void
    {
        $data = ['name' => 'new name'];

        $this->categoryService
            ->expects($this->once())
            ->method('update')
            ->with($this->category, $this->callback(function ($request) use ($data) {
                $this->assertInstanceOf(UpdateProjectCategoryRequest::class, $request);
                $this->assertSame($data['name'], $request->name);
                return true;
            }))
            ->willReturnArgument(0);

        $uri = sprintf('/api/v2/categories/%d', $this->category->getId());
        $client = static::createApiClient($this->ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }

    public function testDelete(): void
    {
        $this->categoryService
            ->expects($this->once())
            ->method('delete')
            ->with($this->category)
            ->willReturn(true);

        $uri = sprintf('/api/v2/categories/%d', $this->category->getId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('DELETE', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseHeaderSame('content-type', 'application/json');
    }
}
