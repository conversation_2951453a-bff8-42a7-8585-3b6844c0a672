<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\Project\CreateProjectRequest;
use App\Dto\Xml2\Project\UpdateProjectRequest;
use App\Entity\Project;
use App\Service\Xml2\ProjectService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function array_merge;
use function array_walk;
use function json_encode;
use function sprintf;
use function ucfirst;

/**
 * @covers \App\Controller\Api\ProjectController
 */
class ProjectControllerTest extends ApiTestCase
{
    private ProjectService $projectService;
    private Project $existingProject;
    private array $projectData = [
        'bookTitle'    => 'bookTitle',
        'workingTitle' => 'workingTitle',
    ];

    protected function setUp(): void
    {
        $container = static::getContainer();
        $em = $container->get(EntityManagerInterface::class);

        $this->projectService = $this->createMock(ProjectService::class);
        $container->set(ProjectService::class, $this->projectService);

        $this->existingProject = new Project();
        $this->existingProject->setShortCode('project-test');
        $this->existingProject->setLastModifiedDate(new \DateTime());
        foreach ($this->projectData as $key => $value) {
            $setter = sprintf('set%s', ucfirst($key));
            $this->existingProject->$setter($value);
        }
        $em->persist($this->existingProject);

        $em->flush();
    }

    public function testCreate(): void
    {
        $data = array_merge($this->projectData, [
            'baseBook'          => 'baseBook',
            'shortCode'         => 'shortCode',
            'projectType'       => 0,
            'commentaryEnabled' => false,
            'hasCdpAccess'      => false,
            'cdpEndpointId'     => 0,
            'cdpBookIds'        => [],
            'cycle'             => [],
        ]);

        $this->projectService
            ->expects($this->once())
            ->method('create')
            ->willReturnCallback(function ($request) use ($data): Project {
                $this->assertInstanceOf(CreateProjectRequest::class, $request);
                array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));

                $project = new Project();
                $project->setShortCode($request->shortCode);
                $project->setLastModifiedDate(new \DateTime());
                return $project;
            });

        $uri = '/api/v2/projects';
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseFormatSame('json');
    }

    public function testUpdate(): void
    {
        $data = [
            'bookTitle'    => 'new bookTitle',
            'workingTitle' => 'new workingTitle',
        ];

        $this->projectService
            ->expects($this->once())
            ->method('update')
            ->with(
                $this->existingProject,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(UpdateProjectRequest::class, $request);
                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturnArgument(0);

        $uri = sprintf('/api/v2/projects/%s', $this->existingProject->getId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }
}
