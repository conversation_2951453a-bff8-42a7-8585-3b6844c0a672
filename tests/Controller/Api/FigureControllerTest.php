<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api;

use App\Dto\Xml2\DeleteNodeRequest;
use App\Dto\Xml2\Figure\CreateFigureRequest;
use App\Dto\Xml2\Figure\DeleteFigureResponse;
use App\Dto\Xml2\Figure\UpdateFigureRequest;
use App\Entity\CodeBook\Figure;
use App\Entity\CodeBook\Section;
use App\Entity\Project;
use App\Enum\Status;
use App\Service\Xml2\FigureService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function array_merge;
use function array_walk;
use function json_decode;
use function json_encode;
use function sprintf;
use function ucfirst;

/**
 * @covers \App\Controller\Api\FigureController
 */
class FigureControllerTest extends ApiTestCase
{
    private FigureService $figureService;
    private Project $project;
    private Figure $existingFigure;
    private Section $section;
    private $figureData = [
        'superTitle'           => 'Super Title',
        'committeeDesignation' => '[CD]',
        'label'                => 'Figure',
        'number'               => '603.2 (2)',
        'title'                => 'Title',
        'subTitle'             => 'Sub Title',
        'media'                => '<mediaobject-group/>',
        'caption'              => 'Caption',
        'figureNotes'          => 'Figure Notes',
        'legend'               => 'Legend',
        'source'               => 'Source',
        'creditTitle'          => 'Credit Title',
        'credit'               => 'Credit',
    ];

    protected function setUp(): void
    {
        $container = self::getContainer();
        $em = $container->get(EntityManagerInterface::class);

        $this->figureService = $this->createMock(FigureService::class);
        $container->set(FigureService::class, $this->figureService);

        $this->existingFigure = new Figure();
        $this->existingFigure->setNodeId('existingFigure');
        foreach ($this->figureData as $key => $value) {
            $setter = sprintf('set%s', ucfirst($key));
            $this->existingFigure->$setter($value);
        }
        $em->persist($this->existingFigure);

        $this->section = new Section();
        $this->section->setNodeId('section');
        $this->section->addChild($this->existingFigure);
        $em->persist($this->section);

        $this->project = new Project();
        $this->project->setShortCode('figure-test');
        $em->persist($this->project);

        $em->flush();
    }

    public function testList(): void
    {
        $this->figureService
            ->expects($this->once())
            ->method('list')
            ->willReturn([$this->existingFigure]);

        $uri = sprintf('/api/v2/books/%s/figures', $this->project->getShortCode());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');

        $response = json_decode($client->getResponse()->getContent(), true);
        $this->assertIsArray($response);
        $this->assertCount(1, $response);
    }

    public function testCreate(): void
    {
        $data = array_merge($this->figureData, [
            'parentNode'   => $this->section->getNodeId(),
            'insertBefore' => $this->existingFigure->getNodeId(),
            'content'      => '<mediaobject/>',
        ]);

        $this->figureService
            ->expects($this->once())
            ->method('create')
            ->with(
                $this->project,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(CreateFigureRequest::class, $request);

                    // change key name for property name
                    $data['media'] = $data['content'];
                    unset($data['content']);

                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturn($this->createMock(Figure::class));

        $uri = sprintf('/api/v2/books/%s/figures', $this->project->getShortCode());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseFormatSame('json');
    }

    public function testRead(): void
    {
        $uri = sprintf('/api/v2/books/%s/figures/%s', $this->project->getShortCode(), $this->existingFigure->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');

        $response = json_decode($client->getResponse()->getContent(), true);
        foreach ($this->figureData as $key => $value) {
            $this->assertSame($value, $response[$key]);
        }
    }

    public function testUpdate(): void
    {
        $data = [
            'status'               => Status::WAITING_CODES,
            'superTitle'           => 'Super Title',
            'committeeDesignation' => '[CD]',
            'label'                => '<delete>Figure</delete>',
            'title'                => '<delete>Title</delete>',
            'subTitle'             => 'Sub Title',
            'content'              => '<mediaobject/>',
            'caption'              => 'Caption',
            'figureNotes'          => 'Figure Notes',
            'legend'               => 'Legend',
            'source'               => 'Source',
            'creditTitle'          => 'Credit Title',
            'credit'               => 'Credit',
        ];

        $this->figureService
            ->expects($this->once())
            ->method('update')
            ->with(
                $this->project,
                $this->existingFigure,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(UpdateFigureRequest::class, $request);

                    // change key name for property name
                    $data['media'] = $data['content'];
                    unset($data['content']);

                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturnArgument(1);

        $uri = sprintf('/api/v2/books/%s/figures/%s', $this->project->getShortCode(), $this->existingFigure->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }

    public function testDelete(): void
    {
        $this->figureService
            ->expects($this->once())
            ->method('delete')
            ->with(
                $this->existingFigure,
                $this->callback(function ($request): bool {
                    $this->assertInstanceOf(DeleteNodeRequest::class, $request);
                    $this->assertTrue($request->showDeletionMarker);
                    return true;
                })
            )
            ->willReturn($this->createMock(DeleteFigureResponse::class));

        $uri = sprintf('/api/v2/books/%s/figures/%s', $this->project->getShortCode(), $this->existingFigure->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('DELETE', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode([
            'showDeletionMarker' => true,
        ]));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }
}
