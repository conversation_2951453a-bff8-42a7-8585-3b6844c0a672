<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\Controller\Api\FrontMatter;

use App\Dto\Xml2\FrontMatter\CreateTitlePageRequest;
use App\Dto\Xml2\FrontMatter\DeleteTitlePageResponse;
use App\Dto\Xml2\FrontMatter\UpdateTitlePageRequest;
use App\Entity\CodeBook\FrontMatter;
use App\Entity\CodeBook\TitlePage;
use App\Entity\Project;
use App\Service\Xml2\FrontMatter\TitlePageService;
use App\Tests\Controller\ApiTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

use function array_walk;
use function json_encode;
use function sprintf;
use function ucfirst;

class TitlePageControllerTest extends ApiTestCase
{
    private TitlePageService $titlePageService;
    private Project $project;
    private FrontMatter $frontMatter;
    private FrontMatter $emptyFrontMatter;
    private TitlePage $existingTitlePage;
    private array $titlePageData = [
        'superTitle' => 'superTitle',
        'title'      => 'title',
        'subTitle'   => 'subTitle',
    ];

    protected function setUp(): void
    {
        $container = static::getContainer();
        $em = $container->get(EntityManagerInterface::class);

        $this->titlePageService = $this->createMock(TitlePageService::class);
        $container->set(TitlePageService::class, $this->titlePageService);

        $this->existingTitlePage = new TitlePage();
        $this->existingTitlePage->setNodeId('titlePage');
        foreach ($this->titlePageData as $key => $value) {
            $setter = sprintf('set%s', ucfirst($key));
            $this->existingTitlePage->$setter($value);
        }
        $em->persist($this->existingTitlePage);

        $this->frontMatter = new FrontMatter();
        $this->frontMatter->setNodeId('frontMatter');
        $this->frontMatter->addChild($this->existingTitlePage);
        $em->persist($this->frontMatter);

        $this->emptyFrontMatter = new FrontMatter();
        $this->frontMatter->setNodeId('emptyFrontMatter');
        $em->persist($this->emptyFrontMatter);

        $this->project = new Project();
        $this->project->setShortCode('title-page-test');
        $em->persist($this->project);

        $em->flush();
    }

    public function testCreate(): void
    {
        $data = $this->titlePageData;
        $data['parentNode'] = $this->emptyFrontMatter->getNodeId();

        $this->titlePageService
            ->expects($this->once())
            ->method('create')
            ->with(
                $this->project,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(CreateTitlePageRequest::class, $request);
                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturn($this->createMock(TitlePage::class));

        $uri = sprintf('/api/v2/books/%s/front-matter/title-page', $this->project->getShortCode());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('POST', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_CREATED);
        $this->assertResponseFormatSame('json');
    }

    public function testRead(): void
    {
        $uri = sprintf('/api/v2/books/%s/front-matter/title-page/%s', $this->project->getShortCode(), $this->existingTitlePage->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('GET', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }

    public function testUpdate(): void
    {
        $data = $this->titlePageData;
        $data['superTitle'] = 'new superTitle';

        $this->titlePageService
            ->expects($this->once())
            ->method('update')
            ->with(
                $this->existingTitlePage,
                $this->callback(function ($request) use ($data): bool {
                    $this->assertInstanceOf(UpdateTitlePageRequest::class, $request);
                    array_walk($data, fn($value, $key) => $this->assertSame($value, $request->{$key}));
                    return true;
                })
            )
            ->willReturnArgument(0);

        $uri = sprintf('/api/v2/books/%s/front-matter/title-page/%s', $this->project->getShortCode(), $this->existingTitlePage->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->xmlHttpRequest('PUT', $uri, [], [], ['CONTENT_TYPE' => 'application/json'], json_encode($data));

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }

    public function testDelete(): void
    {
        $this->titlePageService
            ->expects($this->once())
            ->method('delete')
            ->with($this->existingTitlePage)
            ->willReturn($this->createMock(DeleteTitlePageResponse::class));

        $uri = sprintf('/api/v2/books/%s/front-matter/title-page/%s', $this->project->getShortCode(), $this->existingTitlePage->getNodeId());
        $client = static::createApiClient($this->SUPER_ADMIN);
        $client->request('DELETE', $uri);

        $this->assertResponseIsSuccessful();
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertResponseFormatSame('json');
    }
}
