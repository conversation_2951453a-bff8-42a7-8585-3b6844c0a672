<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare (strict_types=1);

namespace App\Tests\EventSubscriber\CodeBook;

use App\Dto\Xml2\BaseBook\CreateBaseBookRequest;
use App\Event\BaseBook\PreCreateBaseBookEvent;
use App\EventSubscriber\BaseBook\PreIngestEvents\EmphasisTypeSetListener;
use App\Serializer\Encoder\Xml2\Xml2Schema;
use PHPUnit\Framework\TestCase;

use function sprintf;

/**
 * @covers \App\EventSubscriber\BaseBook\PreIngestEvents\EmphasisTypeSetListener
 */
class EmphasisTypeSetListenerTest extends TestCase
{
    private EmphasisTypeSetListener $listener;
    private PreCreateBaseBookEvent $event;
    private CreateBaseBookRequest $request;

    protected function setUp(): void
    {
        $this->listener = new EmphasisTypeSetListener();
        $this->request = new CreateBaseBookRequest();
        $this->event = new PreCreateBaseBookEvent($this->request);
    }

    public function testInvokeAddsTypeAttributeToEmphasisNodesWithoutType(): void
    {
        $xml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis>Some text</emphasis>
    <emphasis type="bold">Already has type</emphasis>
    <emphasis>Another text</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $expectedXml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis type="italic">Some text</emphasis>
    <emphasis type="bold">Already has type</emphasis>
    <emphasis type="italic">Another text</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);

        $this->request->setXml($xml);
        ($this->listener)($this->event);

        $this->assertXmlStringEqualsXmlString($expectedXml, $this->request->getXml());
    }

    public function testInvokeHandlesSingleEmphasisNode(): void
    {
        $xml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis>Single emphasis node</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $expectedXml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis type="italic">Single emphasis node</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);

        $this->request->setXml($xml);
        ($this->listener)($this->event);

        $this->assertXmlStringEqualsXmlString($expectedXml, $this->request->getXml());
    }

    public function testInvokePreservesExistingTypeAttributes(): void
    {
        $xml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis type="bold">Keep bold</emphasis>
    <emphasis>Add italic</emphasis>
    <emphasis type="underline">Keep underline</emphasis>
    <emphasis>Add italic too</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $expectedXml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis type="bold">Keep bold</emphasis>
    <emphasis type="italic">Add italic</emphasis>
    <emphasis type="underline">Keep underline</emphasis>
    <emphasis type="italic">Add italic too</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);

        $this->request->setXml($xml);
        ($this->listener)($this->event);

        $this->assertXmlStringEqualsXmlString($expectedXml, $this->request->getXml());
    }

    public function testInvokeHandlesEmphasisWithEmptyTypeAttribute(): void
    {
        $xml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis type="">Empty type</emphasis>
    <emphasis>No type</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);
        $expectedXml = sprintf(<<<XML
<root xmlns="%s" xmlns:m="%s">
    <emphasis type="italic">Empty type</emphasis>
    <emphasis type="italic">No type</emphasis>
</root>
XML, Xml2Schema::XMLNS, Xml2Schema::XMLNS_M);

        $this->request->setXml($xml);
        ($this->listener)($this->event);

        $this->assertXmlStringEqualsXmlString($expectedXml, $this->request->getXml());
    }

    /** @dataProvider realCases */
    public function testRealCases(string $xml, string $expectedXml): void
    {
        $xml = sprintf('<root xmlns="%s" xmlns:m="%s">%s</root>', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $xml);
        $expectedXml = sprintf('<root xmlns="%s" xmlns:m="%s">%s</root>', Xml2Schema::XMLNS, Xml2Schema::XMLNS_M, $expectedXml);

        $this->request->setXml($xml);
        ($this->listener)($this->event);

        $this->assertXmlStringEqualsXmlString($expectedXml, $this->request->getXml());
    }

    public function realCases(): iterable
    {
        yield 'legalnotice IECC2024' => [<<<XML
<legalnotice>
          <p>ALL RIGHTS RESERVED. This 2024 <emphasis>International Energy Conservation
                        Code</emphasis><sup>® </sup> is a copyrighted work owned by the
                        International Code Council, Inc. (“ICC”). Without separate written
                        permission from the ICC, no part of this book may be reproduced, distributed
                        or transmitted in any form or by any means, including, without limitation,
                        electronic, optical or mechanical means (by way of example, and not
                        limitation, photocopying or recording by or in an information storage and/or
                        retrieval system). For information on use rights and permissions, please
                        contact: ICC Publications, 4051 Flossmoor Road, Country Club Hills, Illinois
                        60478; 1-888-ICC-SAFE (422-7233);.
                       </p>
        </legalnotice>
XML, <<<XML
<legalnotice>
          <p>ALL RIGHTS RESERVED. This 2024 <emphasis type="italic">International Energy Conservation
                        Code</emphasis><sup>® </sup> is a copyrighted work owned by the
                        International Code Council, Inc. (“ICC”). Without separate written
                        permission from the ICC, no part of this book may be reproduced, distributed
                        or transmitted in any form or by any means, including, without limitation,
                        electronic, optical or mechanical means (by way of example, and not
                        limitation, photocopying or recording by or in an information storage and/or
                        retrieval system). For information on use rights and permissions, please
                        contact: ICC Publications, 4051 Flossmoor Road, Country Club Hills, Illinois
                        60478; 1-888-ICC-SAFE (422-7233);.
                       </p>
        </legalnotice>
XML];
    }
}
