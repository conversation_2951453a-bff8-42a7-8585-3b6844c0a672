<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare (strict_types=1);

namespace App\Tests\EventSubscriber\CodeBook;

use App\Entity\CodeBook\Publication;
use App\Entity\Project;
use App\Entity\ProjectType;
use App\Event\CodeBook\CreateCodeBookEvent;
use App\EventListener\CodeBook\CreateCodeBookEvents\RemoveErrataListener;
use App\Service\StopwatchService;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;

/**
 * @covers \App\EventListener\CodeBook\CreateCodeBookEvents\RemoveErrataListener
 */
class RemoveErrataListenerTest extends TestCase
{
    private RemoveErrataListener $listener;

    protected function setUp(): void
    {
        $this->listener = new RemoveErrataListener(new StopwatchService(new NullLogger()));
    }

    public function testInvokeSendsEvents(): void
    {
        $projectType = $this->createMock(ProjectType::class);
        $projectType
            ->expects($this->once())
            ->method('getName')
            ->willReturn(ProjectType::PROJECT_TYPE_FIRST_PRINTING);
        $project = new Project();
        $project->setProjectType($projectType);

        $event = new CreateCodeBookEvent(new Publication(), $project);
        ($this->listener)($event);
    }
}
