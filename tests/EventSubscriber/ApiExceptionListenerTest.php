<?php
/*
 * (c) International Code Council
 */

declare(strict_types=1);

namespace App\Tests\EventSubscriber;

use App\Dto\Xml2\Validation\XmlValidationResult;
use App\Enum\XmlValidationIssueType;
use App\EventSubscriber\ApiExceptionListener;
use App\Exception\XmlValidationException;
use App\Dto\Xml2\Validation\XmlValidationMessage;
use App\Service\Xml2\XmlValidationErrorFormatter;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;

class ApiExceptionListenerTest extends TestCase
{
    public function testIncludesIssuesWhenXmlValidationExceptionThrown(): void
    {
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->once())->method('error')->with('Malformed XML detected.');

        $xmlValidationErrorFormatter = $this->createMock(XmlValidationErrorFormatter::class);
        $xmlValidationErrorFormatter->expects($this->once())
            ->method('normalize')
            ->willReturn([
                [
                    'title'     => 'Title',
                    'body'      => '<para>',
                    'sectionId' => 'node-1',
                    'number'    => '1',
                    'type'      => XmlValidationIssueType::INVALID_NESTING->value,
                ]
            ]);

        $listener = new ApiExceptionListener($logger, 'prod', $xmlValidationErrorFormatter);

        $message = new XmlValidationMessage('Broken markup (line 12)', 12, 4);
        $issue = new XmlValidationResult(
            'node-1',                                    // nodeId
            'ulid-1',                                   // nodeUlid
            'chapter-ulid-1',                           // chapterNodeUlid
            'parent-ulid-1',                            // parentNodeUlid
            'Volume_Ch1_Sec1',                          // pathId
            'section',                                  // nodeType
            'body',                                     // field
            $message->message,                          // message
            '<para>',                                   // snippet
            XmlValidationIssueType::INVALID_NESTING,   // type
            'Title',                                    // title
            '1',                                        // number
            [$message],                                 // errors
            $message->line,                             // line
            $message->column                            // column
        );

        $exception = new XmlValidationException([$issue]);
        $kernel = $this->createMock(HttpKernelInterface::class);
        $request = Request::create('/api/v2/books/export');

        $event = new ExceptionEvent(
            $kernel,
            $request,
            HttpKernelInterface::MAIN_REQUEST,
            $exception
        );

        $listener($event);

        $response = $event->getResponse();

        self::assertSame(400, $response->getStatusCode());

        $payload = json_decode((string) $response->getContent(), true, 512, JSON_THROW_ON_ERROR);

        self::assertSame('Malformed XML detected.', $payload['message']);
        self::assertSame(400, $payload['code']);
        self::assertArrayHasKey('issues', $payload);
        self::assertNotEmpty($payload['issues']);
        self::assertSame([
            'title'     => 'Title',
            'body'      => '<para>',
            'sectionId' => 'node-1',
            'number'    => '1',
            'type'      => XmlValidationIssueType::INVALID_NESTING->value,
        ], $payload['issues'][0]);
    }
}
