<?php
/*
 * (c) International Code Council
 */

declare(strict_types=1);

namespace App\Tests\EventSubscriber\Project\Export;

use App\Dto\Xml2\Validation\XmlValidationResult;
use App\Entity\CodeBook\Publication;
use App\Entity\Project;
use App\Event\Project\PreExportProject;
use App\EventSubscriber\Project\Export\XmlValidationSubscriber;
use App\Exception\XmlValidationException;
use App\Service\StopwatchService;
use App\Service\Xml2\XmlValidationService;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

class XmlValidationSubscriberTest extends TestCase
{
    public function testThrowsWhenIssuesDetected(): void
    {
        $project = new Project();
        $project->setShortCode('TEST');
        $project->setCodeBook(new Publication());

        $event = new PreExportProject($project);

        $service = $this->createMock(XmlValidationService::class);
        $logger = $this->createMock(LoggerInterface::class);
        $stopwatch = new StopwatchService(new NullLogger());

        $issue = new XmlValidationResult(
            'node-1',                    // nodeId
            'ulid-1',                   // nodeUlid
            'chapter-ulid-1',           // chapterNodeUlid
            'parent-ulid-1',            // parentNodeUlid
            'Volume_Ch1_Sec1',          // pathId
            'section',                  // nodeType
            'body',                     // field
            'Broken markup',            // message
            '<para>'                    // snippet
        );

        $service->expects($this->once())
            ->method('validateProject')
            ->with($project)
            ->willReturn([$issue]);

        $logger->expects($this->once())->method('error');

        $subscriber = new XmlValidationSubscriber($service, $logger, $stopwatch);

        $this->expectException(XmlValidationException::class);
        $subscriber->onPreExport($event);
    }

    public function testDoesNotThrowWhenNoIssuesDetected(): void
    {
        $project = new Project();
        $project->setShortCode('TEST');
        $project->setCodeBook(new Publication());

        $event = new PreExportProject($project);

        $service = $this->createMock(XmlValidationService::class);
        $logger = $this->createMock(LoggerInterface::class);
        $stopwatch = new StopwatchService(new NullLogger());

        $service->expects($this->once())
            ->method('validateProject')
            ->with($project)
            ->willReturn([]);

        $logger->expects($this->never())->method('error');

        $subscriber = new XmlValidationSubscriber($service, $logger, $stopwatch);

        $subscriber->onPreExport($event);

        $this->addToAssertionCount(1);
    }
}
