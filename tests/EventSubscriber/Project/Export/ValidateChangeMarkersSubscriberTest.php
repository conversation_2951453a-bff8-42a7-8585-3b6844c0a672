<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

namespace App\Tests\EventSubscriber\Project\Export;

use App\Entity\Project;
use App\Event\Project\PostExportProject;
use App\EventSubscriber\Project\Export\ValidateChangeMarkersSubscriber;
use App\Helper\Xml2Helper;
use App\Serializer\Encoder\Xml2\Xml2Schema;
use App\Service\Xml2\ChangeMarkerAttributeSanitizer;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

/**
 * @covers \App\EventSubscriber\Project\Export\ValidateChangeMarkersSubscriber
 */
class ValidateChangeMarkersSubscriberTest extends TestCase
{
    public function testValidChangeMarkersPass(): void
    {
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->never())->method('error');
        $logger->expects($this->never())->method('warning');

        $subscriber = new ValidateChangeMarkersSubscriber($logger, new ChangeMarkerAttributeSanitizer());

        $project = $this->createStub(Project::class);
        $project->method('getShortCode')->willReturn('project.TEST');

        $xml = sprintf(
            '<publication xmlns="%s"><section><insert data-changed="added" revision="rev">Text</insert><delete data-changed="deleted" revision="rev">Old</delete></section></publication>',
            Xml2Schema::XMLNS
        );

        $event = new PostExportProject($project, $xml);

        $subscriber->validateChangeMarkers($event);

        $this->assertSame($xml, $event->getXml());
    }

    public function testMissingAttributesThrowsException(): void
    {
        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->once())
            ->method('warning')
            ->with(
                $this->stringContains('Sanitized change marker attributes'),
                $this->callback(function (array $context): bool {
                    return isset($context['book_id'])
                        && 'project.TEST' === $context['book_id'];
                })
            );

        $subscriber = new ValidateChangeMarkersSubscriber($logger, new ChangeMarkerAttributeSanitizer());

        $project = $this->createStub(Project::class);
        $project->method('getShortCode')->willReturn('project.TEST');

        $xml = sprintf(
            '<publication xmlns="%s"><section><insert revision="rev">Text</insert></section></publication>',
            Xml2Schema::XMLNS
        );

        $event = new PostExportProject($project, $xml);

        $subscriber->validateChangeMarkers($event);

        $document = Xml2Helper::createDOMDocument(
            sprintf('<root xmlns="%s">%s</root>', Xml2Schema::XMLNS, $event->getXml())
        );
        $xpath = Xml2Helper::createDOMXPath($document);
        $insert = $xpath->query('//x:insert')->item(0);

        $this->assertSame('changed_level0', $insert->getAttribute('data-changed'));
        $this->assertSame('rev', $insert->getAttribute('revision'));
    }
}
