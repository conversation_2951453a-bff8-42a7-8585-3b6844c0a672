<?php
/*
 * (c) International Code Council
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

use Doctrine\ORM\Tools\SchemaTool;
use Symfony\Component\Dotenv\Dotenv;

require dirname(__DIR__) . '/vendor/autoload.php';

if (method_exists(Dotenv::class, 'bootEnv')) {
    (new Dotenv())->bootEnv(dirname(__DIR__) . '/.env');
}

/**
 * Return true when the database needs rebuilding based on Doctrine metadata.
 * The Kernel is booted here so we can access the container/EntityManager.
 */
function needsDatabaseRebuild(): bool
{
    $kernel = new App\Kernel('test', true);
    $kernel->boot();
    $container = $kernel->getContainer();
    $entityManager = $container->get('doctrine')->getManager();

    $schemaTool = new SchemaTool($entityManager);
    $metadata = $entityManager->getMetadataFactory()->getAllMetadata();

    try {
        $sql = $schemaTool->getUpdateSchemaSql($metadata, false);
        return !empty($sql);
    } catch (Exception $exception) {
    }

    return true;
}

passthru(sprintf('rm -rf %s/../var/cache/test', __DIR__));

$console = sprintf('APP_ENV=test php -d memory_limit=-1 %s/../bin/console', __DIR__);
passthru(sprintf('%s doctrine:database:create --if-not-exists -n -q', $console));

// Read the force flag from the environment. Set APP_TEST_REBUILD_DB=1 to force.
$forceRebuild = filter_var(getenv('APP_TEST_REBUILD_DB') ?: false, FILTER_VALIDATE_BOOLEAN);

if (needsDatabaseRebuild() || $forceRebuild) {
    passthru(sprintf('%s doctrine:schema:drop --force --full-database -n', $console));
    passthru(sprintf('%s doctrine:schema:update --complete --force -n', $console));
    passthru(sprintf('%s doctrine:fixtures:load --append --group=prod --group=test -n', $console));
} else {
    echo "Database schema is valid, skipping rebuild.\n";
}
