[program:frankenphp]
command=/usr/local/bin/frankenphp run --config /etc/frankenphp/Caddyfile --watch
logfile=/var/log/supervisor/franken.log

; =======================
; CODEBOOK WORKER
; =======================
[program:async_codebook]
command=php bin/console messenger:consume async_codebook --time-limit=3600 -vv
directory=/app
autostart=true
autorestart=true
startsecs=5
startretries=5
stopwaitsecs=60
numprocs=1
process_name=%(program_name)s_%(process_num)02d
stdout_logfile=/var/log/supervisor/async_codebook.log
stderr_logfile=/var/log/supervisor/async_codebook_error.log

; =======================
; HEAVY WORKER
; =======================
[program:async_heavy]
command=php bin/console messenger:consume async_heavy --time-limit=3600 -vv
directory=/app
autostart=true
autorestart=true
startsecs=5
startretries=5
stopwaitsecs=60
numprocs=1
process_name=%(program_name)s_%(process_num)02d
stdout_logfile=/var/log/supervisor/async_heavy.log
stderr_logfile=/var/log/supervisor/async_heavy_error.log

; =======================
; GROUP (optional)
; =======================
[group:workers]
programs=async_codebook,async_heavy
